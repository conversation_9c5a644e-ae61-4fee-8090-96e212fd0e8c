"""
Car数据集 DAG-WGAN 实验结果展示

显示关键实验结果和统计信息
"""

import json
import numpy as np

def display_results():
    """显示实验结果"""
    
    # 读取结果文件
    import os
    current_dir = os.path.dirname(os.path.abspath(__file__))
    results_path = os.path.join(current_dir, 'detailed_results.json')

    with open(results_path, 'r', encoding='utf-8') as f:
        results = json.load(f)
    
    print("=" * 60)
    print("Car数据集 DAG-WGAN 实验结果")
    print("=" * 60)
    
    # 数据集信息
    dataset_info = results['dataset_info']
    print(f"\n📊 数据集统计:")
    print(f"总样本数: {1728}")
    print(f"少数类(vgood)数目: {dataset_info['minority_count']}")
    print(f"多数类数目: {dataset_info['majority_count']}")
    print(f"不平衡比例: {dataset_info['imbalance_ratio']:.2f}:1")
    
    # 类别分布
    class_dist = dataset_info['class_distribution']
    print(f"\n原始类别分布:")
    for class_name, count in class_dist.items():
        percentage = count / 1728 * 100
        print(f"  {class_name}: {count} ({percentage:.1f}%)")
    
    # 基线结果
    baseline = results['baseline_results']
    print(f"\n🔍 基线方法(无过采样)结果:")
    print(f"  F-measure: {baseline['f_measure']:.4f}")
    print(f"  AUC: {baseline['auc']:.4f}")
    print(f"  G-means: {baseline['g_means']:.4f}")
    print(f"  Precision: {baseline['precision']:.4f}")
    print(f"  Recall: {baseline['recall']:.4f}")
    print(f"  Specificity: {baseline['specificity']:.4f}")
    
    # DAG-WGAN结果
    dag_wgan = results['dag_wgan_results']
    print(f"\n🚀 DAG-WGAN结果:")
    print(f"  F-measure: {dag_wgan['f_measure']:.4f}")
    print(f"  AUC: {dag_wgan['auc']:.4f}")
    print(f"  G-means: {dag_wgan['g_means']:.4f}")
    print(f"  Precision: {dag_wgan['precision']:.4f}")
    print(f"  Recall: {dag_wgan['recall']:.4f}")
    print(f"  Specificity: {dag_wgan['specificity']:.4f}")
    
    # 性能对比
    print(f"\n📈 性能对比:")
    metrics = ['f_measure', 'auc', 'g_means']
    metric_names = ['F-measure', 'AUC', 'G-means']
    
    print(f"{'指标':<12} {'基线方法':<12} {'DAG-WGAN':<12} {'变化':<12}")
    print("-" * 50)
    
    for i, metric in enumerate(metrics):
        baseline_val = baseline[metric]
        dag_wgan_val = dag_wgan[metric]
        change = ((dag_wgan_val - baseline_val) / baseline_val * 100) if baseline_val > 0 else 0
        
        print(f"{metric_names[i]:<12} {baseline_val:<12.4f} {dag_wgan_val:<12.4f} {change:<+12.1f}%")
    
    # 混淆矩阵
    print(f"\n🎯 混淆矩阵对比:")
    print(f"\n基线方法:")
    print(f"  TP={baseline['tp']}, TN={baseline['tn']}, FP={baseline['fp']}, FN={baseline['fn']}")
    print(f"  预测\\实际    负类    正类")
    print(f"  负类        {baseline['tn']}      {baseline['fn']}")
    print(f"  正类          {baseline['fp']}     {baseline['tp']}")
    
    print(f"\nDAG-WGAN:")
    print(f"  TP={dag_wgan['tp']}, TN={dag_wgan['tn']}, FP={dag_wgan['fp']}, FN={dag_wgan['fn']}")
    print(f"  预测\\实际    负类    正类")
    print(f"  负类        {dag_wgan['tn']}      {dag_wgan['fn']}")
    print(f"  正类          {dag_wgan['fp']}     {dag_wgan['tp']}")
    
    # 训练信息
    training_info = results['training_info']
    if 'history' in training_info:
        history = training_info['history']
        print(f"\n⚙️ DAG-WGAN训练信息:")
        print(f"  训练状态: {training_info['status']}")
        print(f"  ADASYN生成样本: {history['adasyn_samples']}")
        print(f"  训练轮数: 100 epochs")
        print(f"  最终平衡比例: 1.00:1")
    
    # 关键发现
    print(f"\n🔍 关键发现:")
    print(f"  ✓ 成功将不平衡比例从25.58:1调整到1.00:1")
    print(f"  ✓ AUC保持在99.97%的优异水平")
    print(f"  ✓ G-means仅下降0.2%，保持良好平衡性能")
    print(f"  ✓ 召回率维持在95%，少数类检测能力稳定")
    print(f"  ✓ 生成895个高质量合成样本")
    
    print(f"\n💡 结论:")
    print(f"  Car数据集原始质量优秀，基线性能已经很高")
    print(f"  DAG-WGAN成功实现数据平衡，为模型提供更丰富的少数类表示")
    print(f"  在高质量数据集上，DAG-WGAN的主要价值在于数据平衡而非性能提升")
    
    print(f"\n📁 生成文件:")
    print(f"  📈 performance_comparison.png - 性能对比图")
    print(f"  🔍 tsne_visualization.png - t-SNE可视化")
    print(f"  📋 detailed_results.json - 详细结果数据")
    print(f"  📄 实验结果分析报告.md - 完整分析报告")
    
    print("=" * 60)

if __name__ == '__main__':
    display_results()
