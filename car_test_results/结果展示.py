"""
Car数据集测试结果展示

显示SMOTE vs ADASYN的对比结果
"""

import json
import numpy as np

def display_results():
    """显示实验结果"""
    
    # 读取结果文件
    import os
    current_dir = os.path.dirname(os.path.abspath(__file__))
    results_path = os.path.join(current_dir, 'test_results.json')

    with open(results_path, 'r', encoding='utf-8') as f:
        results = json.load(f)
    
    print("=" * 80)
    print("Car数据集处理测试结果")
    print("vgood作为少数类(1)，其他合并为多数类(0)")
    print("=" * 80)
    
    # 数据集信息
    dataset_info = results['dataset_info']
    print(f"\n📊 数据集统计:")
    print(f"总样本数: {dataset_info['total_samples']:,}")
    print(f"少数类(vgood)数目: {dataset_info['minority_count']}")
    print(f"多数类数目: {dataset_info['majority_count']}")
    print(f"不平衡比例: {dataset_info['imbalance_ratio']:.2f}:1")
    
    print(f"\n原始类别分布:")
    for class_name, count in dataset_info['class_distribution'].items():
        percentage = (count / dataset_info['total_samples']) * 100
        print(f"  {class_name}: {count:,} ({percentage:.1f}%)")
    
    # 方法结果对比
    method_results = results['results']
    
    print(f"\n🏆 方法性能对比:")
    print(f"{'方法':<20} {'F-measure':<12} {'AUC':<12} {'G-means':<12}")
    print("-" * 65)
    
    for result in method_results:
        method_name = result['method_name']
        f_measure = result['f_measure']
        auc = result['auc']
        g_means = result['g_means']
        
        print(f"{method_name:<20} {f_measure:<12.4f} {auc:<12.4f} {g_means:<12.4f}")
    
    # 最佳性能
    best_performance = results['best_performance']
    print(f"\n🥇 最佳性能:")
    print(f"  F-measure: {best_performance['f_measure']['method']} ({best_performance['f_measure']['score']:.4f})")
    print(f"  AUC: {best_performance['auc']['method']} ({best_performance['auc']['score']:.4f})")
    print(f"  G-means: {best_performance['g_means']['method']} ({best_performance['g_means']['score']:.4f})")
    
    # 详细分析
    print(f"\n📈 详细分析:")
    
    baseline = method_results[0]  # 基线方法
    smote = method_results[1]     # SMOTE
    adasyn = method_results[2]    # ADASYN
    
    print(f"\n基线方法(无过采样):")
    print(f"  混淆矩阵: TP={baseline['tp']}, TN={baseline['tn']}, FP={baseline['fp']}, FN={baseline['fn']}")
    print(f"  精确率: {baseline['precision']:.4f}")
    print(f"  召回率: {baseline['recall']:.4f}")
    print(f"  特异性: {baseline['specificity']:.4f}")
    
    print(f"\nSMOTE (k=5个邻居):")
    print(f"  混淆矩阵: TP={smote['tp']}, TN={smote['tn']}, FP={smote['fp']}, FN={smote['fn']}")
    print(f"  精确率: {smote['precision']:.4f}")
    print(f"  召回率: {smote['recall']:.4f}")
    print(f"  特异性: {smote['specificity']:.4f}")
    print(f"  重采样后样本数: 2,328 (原始: 1,209)")
    print(f"  类别平衡: [1164, 1164] (完美平衡)")
    
    print(f"\nADASYN (默认参数):")
    print(f"  混淆矩阵: TP={adasyn['tp']}, TN={adasyn['tn']}, FP={adasyn['fp']}, FN={adasyn['fn']}")
    print(f"  精确率: {adasyn['precision']:.4f}")
    print(f"  召回率: {adasyn['recall']:.4f}")
    print(f"  特异性: {adasyn['specificity']:.4f}")
    print(f"  重采样后样本数: 2,320 (原始: 1,209)")
    print(f"  类别平衡: [1164, 1156] (接近平衡)")
    
    # 方法对比分析
    print(f"\n🔍 方法对比分析:")
    
    # F-measure对比
    baseline_f = baseline['f_measure']
    smote_f = smote['f_measure']
    adasyn_f = adasyn['f_measure']
    
    smote_f_change = ((smote_f - baseline_f) / baseline_f * 100)
    adasyn_f_change = ((adasyn_f - baseline_f) / baseline_f * 100)
    
    print(f"\nF-measure变化:")
    print(f"  SMOTE vs 基线: {smote_f_change:+.1f}% ({baseline_f:.4f} → {smote_f:.4f})")
    print(f"  ADASYN vs 基线: {adasyn_f_change:+.1f}% ({baseline_f:.4f} → {adasyn_f:.4f})")
    
    # AUC对比
    baseline_auc = baseline['auc']
    smote_auc = smote['auc']
    adasyn_auc = adasyn['auc']
    
    smote_auc_change = ((smote_auc - baseline_auc) / baseline_auc * 100)
    adasyn_auc_change = ((adasyn_auc - baseline_auc) / baseline_auc * 100)
    
    print(f"\nAUC变化:")
    print(f"  SMOTE vs 基线: {smote_auc_change:+.2f}% ({baseline_auc:.4f} → {smote_auc:.4f})")
    print(f"  ADASYN vs 基线: {adasyn_auc_change:+.2f}% ({baseline_auc:.4f} → {adasyn_auc:.4f})")
    
    # G-means对比
    baseline_g = baseline['g_means']
    smote_g = smote['g_means']
    adasyn_g = adasyn['g_means']
    
    smote_g_change = ((smote_g - baseline_g) / baseline_g * 100)
    adasyn_g_change = ((adasyn_g - baseline_g) / baseline_g * 100)
    
    print(f"\nG-means变化:")
    print(f"  SMOTE vs 基线: {smote_g_change:+.1f}% ({baseline_g:.4f} → {smote_g:.4f})")
    print(f"  ADASYN vs 基线: {adasyn_g_change:+.1f}% ({baseline_g:.4f} → {adasyn_g:.4f})")
    
    # 关键发现
    print(f"\n💡 关键发现:")
    print(f"  1. Car数据集原始质量很高，基线F-measure达到97.44%")
    print(f"  2. 基线方法在AUC上表现最佳 (99.99%)")
    print(f"  3. SMOTE和ADASYN在G-means上表现更好 (+2.4%)")
    print(f"  4. 过采样方法提高了召回率 (95% → 100%)")
    print(f"  5. 过采样方法略微降低了精确率 (100% → 90.91%)")
    print(f"  6. 所有方法的AUC都超过99.8%，显示数据可分性优秀")
    
    # 实际应用建议
    print(f"\n📋 实际应用建议:")
    print(f"  • 如果追求最高F-measure: 使用基线方法(无过采样)")
    print(f"  • 如果追求最高AUC: 使用基线方法(无过采样)")
    print(f"  • 如果追求平衡性能(G-means): 使用SMOTE或ADASYN")
    print(f"  • 如果不能容忍假阴性: 使用SMOTE或ADASYN (召回率100%)")
    print(f"  • 如果不能容忍假阳性: 使用基线方法 (精确率100%)")
    
    # 技术细节
    print(f"\n⚙️ 技术配置:")
    config = results['experiment_config']
    print(f"  分类器: {config['classifier']}")
    print(f"  测试集比例: {config['test_size']*100}%")
    print(f"  随机种子: {config['random_state']}")
    print(f"  SMOTE邻居数: {config['smote_k_neighbors']}")
    print(f"  ADASYN参数: {config['adasyn_params']}")
    
    print("=" * 80)

if __name__ == '__main__':
    display_results()
