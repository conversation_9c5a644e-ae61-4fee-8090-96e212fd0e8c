{"dataset_info": {"total_samples": 1728, "minority_count": "65", "majority_count": "1663", "imbalance_ratio": 25.584615384615386, "class_distribution": {"unacc": 1210, "acc": 384, "good": 69, "vgood": 65}}, "experiment_config": {"test_size": 0.3, "random_state": 42, "classifier": "RandomForest", "smote_k_neighbors": 5, "adasyn_params": "default"}, "results": [{"method_name": "基线方法(无过采样)", "f_measure": 0.9743589743589743, "auc": 0.9998997995991985, "g_means": 0.9746794344808963, "precision": 1.0, "recall": 0.95, "specificity": 1.0, "tp": "19", "tn": "499", "fp": "0", "fn": "1"}, {"method_name": "SMOTE (k=5)", "f_measure": 0.9523809523809523, "auc": 0.9986973947895791, "g_means": 0.9979939799256987, "precision": 0.9090909090909091, "recall": 1.0, "specificity": 0.9959919839679359, "tp": "20", "tn": "497", "fp": "2", "fn": "0"}, {"method_name": "ADASYN (默认)", "f_measure": 0.9523809523809523, "auc": 0.9985971943887776, "g_means": 0.9979939799256987, "precision": 0.9090909090909091, "recall": 1.0, "specificity": 0.9959919839679359, "tp": "20", "tn": "497", "fp": "2", "fn": "0"}], "best_performance": {"f_measure": {"method": "基线方法(无过采样)", "score": 0.9743589743589743}, "auc": {"method": "基线方法(无过采样)", "score": 0.9998997995991985}, "g_means": {"method": "SMOTE (k=5)", "score": 0.9979939799256987}}}