{"experiment_framework": "5.1-5.4实验设计", "datasets": {"car-1": {"dimensions": 6, "samples": 1728, "imbalance_ratio": 25.58, "application": "车辆故障诊断", "file_path": "data/car.data", "target_class": "vgood", "expected_f1": 0.85}, "ecoil-1": {"dimensions": 7, "samples": 366, "imbalance_ratio": 3.36, "application": "微生物分类", "file_path": null, "target_class": 1, "expected_f1": 0.91}, "glass-1": {"dimensions": 9, "samples": 214, "imbalance_ratio": 6.38, "application": "材料缺陷检测", "file_path": null, "target_class": 1, "expected_f1": 0.87}, "predictive": {"dimensions": 8, "samples": 10000, "imbalance_ratio": 85.96, "application": "金融风险预测", "file_path": null, "target_class": 1, "expected_f1": 0.82}, "statlog": {"dimensions": 20, "samples": 1000, "imbalance_ratio": 2.33, "application": "卫星图像识别", "file_path": null, "target_class": 1, "expected_f1": 0.93}, "wisconsin-1": {"dimensions": 9, "samples": 699, "imbalance_ratio": 1.89, "application": "医疗病理分析", "file_path": null, "target_class": 1, "expected_f1": 0.89}, "yeast-1": {"dimensions": 8, "samples": 1484, "imbalance_ratio": 28.68, "application": "生物活性预测", "file_path": null, "target_class": 1, "expected_f1": 0.84}}, "methods": {"SMOTE": {"k_neighbors": 5}, "ADASYN": {"random_state": 42}, "Focal Loss": {"alpha": 0.25, "gamma": 2}, "WGAN-GP": {"lambda_gp": 10}, "BAGAN": {"random_state": 42}, "ADASYN-GAN": {"random_state": 42}, "Density-WGAN-GP": {"random_state": 42}, "DAG-WGAN": {"random_state": 42}}, "results": {"car-1": {"Baseline": {"method_name": "Baseline", "minority_f1_score": 0.9743589743589743, "boundary_confusion_index": 0.08531343283582088, "high_density_overlap_rate": 0.0, "training_time_hours": 3.9924648072984484e-05, "auc": 0.9998997995991983, "g_means": 0.9746794344808963, "precision": 1.0, "recall": 0.95, "tp": "19", "tn": "499", "fp": "0", "fn": "1"}, "SMOTE": {"method_name": "SMOTE", "minority_f1_score": 0.9500000000000001, "boundary_confusion_index": 0.0784776119402985, "high_density_overlap_rate": 0.5772118800363126, "training_time_hours": 7.604320844014485e-05, "auc": 0.9997995991983968, "g_means": 0.9737023119951854, "precision": 0.95, "recall": 0.95, "tp": "19", "tn": "498", "fp": "1", "fn": "1"}, "ADASYN": {"method_name": "ADASYN", "minority_f1_score": 0.9500000000000001, "boundary_confusion_index": 0.0791044776119403, "high_density_overlap_rate": 0.581805228573252, "training_time_hours": 8.61154662238227e-05, "auc": 0.9997995991983968, "g_means": 0.9737023119951854, "precision": 0.95, "recall": 0.95, "tp": "19", "tn": "498", "fp": "1", "fn": "1"}, "Focal Loss": {"method_name": "Focal Loss", "minority_f1_score": 0.9743589743589743, "boundary_confusion_index": 0.08531343283582088, "high_density_overlap_rate": 0.2916629882191678, "training_time_hours": 4.6924551328023274e-05, "auc": 0.9998997995991983, "g_means": 0.9746794344808963, "precision": 1.0, "recall": 0.95, "tp": "19", "tn": "499", "fp": "0", "fn": "1"}, "WGAN-GP": {"method_name": "WGAN-GP", "minority_f1_score": 0.9743589743589743, "boundary_confusion_index": 0.08531343283582088, "high_density_overlap_rate": 0.3038985021005668, "training_time_hours": 4.9189064237806534e-05, "auc": 0.9998997995991983, "g_means": 0.9746794344808963, "precision": 1.0, "recall": 0.95, "tp": "19", "tn": "499", "fp": "0", "fn": "1"}, "BAGAN": {"method_name": "BAGAN", "minority_f1_score": 0.9743589743589743, "boundary_confusion_index": 0.08531343283582088, "high_density_overlap_rate": 0.20680256628563864, "training_time_hours": 4.5785374111599395e-05, "auc": 0.9998997995991983, "g_means": 0.9746794344808963, "precision": 1.0, "recall": 0.95, "tp": "19", "tn": "499", "fp": "0", "fn": "1"}, "ADASYN-GAN": {"method_name": "ADASYN-GAN", "minority_f1_score": 0.9743589743589743, "boundary_confusion_index": 0.08531343283582088, "high_density_overlap_rate": 0.2339549937567651, "training_time_hours": 4.8218104574415416e-05, "auc": 0.9998997995991983, "g_means": 0.9746794344808963, "precision": 1.0, "recall": 0.95, "tp": "19", "tn": "499", "fp": "0", "fn": "1"}, "Density-WGAN-GP": {"method_name": "Density-WGAN-GP", "minority_f1_score": 0.9743589743589743, "boundary_confusion_index": 0.08531343283582088, "high_density_overlap_rate": 0.2154302300020977, "training_time_hours": 4.526237646738688e-05, "auc": 0.9998997995991983, "g_means": 0.9746794344808963, "precision": 1.0, "recall": 0.95, "tp": "19", "tn": "499", "fp": "0", "fn": "1"}, "DAG-WGAN": {"method_name": "DAG-WGAN", "minority_f1_score": 0.9743589743589743, "boundary_confusion_index": 0.08531343283582088, "high_density_overlap_rate": 0.3373000546647197, "training_time_hours": 4.631691508822971e-05, "auc": 0.9998997995991983, "g_means": 0.9746794344808963, "precision": 1.0, "recall": 0.95, "tp": "19", "tn": "499", "fp": "0", "fn": "1"}}, "ecoil-1": {"Baseline": {"method_name": "Baseline", "minority_f1_score": 0.8695652173913043, "boundary_confusion_index": 0.30118181818181816, "high_density_overlap_rate": 0.0, "training_time_hours": 5.087839232550727e-05, "auc": 0.9670588235294117, "g_means": 0.8891502883619381, "precision": 0.9523809523809523, "recall": 0.8, "tp": "20", "tn": "84", "fp": "1", "fn": "5"}, "SMOTE": {"method_name": "SMOTE", "minority_f1_score": 0.8936170212765958, "boundary_confusion_index": 0.27754545454545454, "high_density_overlap_rate": 0.35492604323653665, "training_time_hours": 5.157470703125e-05, "auc": 0.968, "g_means": 0.911107922838356, "precision": 0.9545454545454546, "recall": 0.84, "tp": "21", "tn": "84", "fp": "1", "fn": "4"}, "ADASYN": {"method_name": "ADASYN", "minority_f1_score": 0.8571428571428572, "boundary_confusion_index": 0.35127272727272724, "high_density_overlap_rate": 0.2625739057137937, "training_time_hours": 6.375385655297174e-05, "auc": 0.9670588235294117, "g_means": 0.9001960570767185, "precision": 0.875, "recall": 0.84, "tp": "21", "tn": "82", "fp": "3", "fn": "4"}, "Focal Loss": {"method_name": "Focal Loss", "minority_f1_score": 0.8695652173913043, "boundary_confusion_index": 0.30118181818181816, "high_density_overlap_rate": 0.3353257699114691, "training_time_hours": 5.026267634497748e-05, "auc": 0.9670588235294117, "g_means": 0.8891502883619381, "precision": 0.9523809523809523, "recall": 0.8, "tp": "20", "tn": "84", "fp": "1", "fn": "5"}, "WGAN-GP": {"method_name": "WGAN-GP", "minority_f1_score": 0.8695652173913043, "boundary_confusion_index": 0.30118181818181816, "high_density_overlap_rate": 0.3575459600866806, "training_time_hours": 5.0221681594848635e-05, "auc": 0.9670588235294117, "g_means": 0.8891502883619381, "precision": 0.9523809523809523, "recall": 0.8, "tp": "20", "tn": "84", "fp": "1", "fn": "5"}, "BAGAN": {"method_name": "BAGAN", "minority_f1_score": 0.8695652173913043, "boundary_confusion_index": 0.30118181818181816, "high_density_overlap_rate": 0.3748441344193987, "training_time_hours": 4.9753586451212566e-05, "auc": 0.9670588235294117, "g_means": 0.8891502883619381, "precision": 0.9523809523809523, "recall": 0.8, "tp": "20", "tn": "84", "fp": "1", "fn": "5"}, "ADASYN-GAN": {"method_name": "ADASYN-GAN", "minority_f1_score": 0.8695652173913043, "boundary_confusion_index": 0.30118181818181816, "high_density_overlap_rate": 0.3713277177358034, "training_time_hours": 5.024565590752496e-05, "auc": 0.9670588235294117, "g_means": 0.8891502883619381, "precision": 0.9523809523809523, "recall": 0.8, "tp": "20", "tn": "84", "fp": "1", "fn": "5"}, "Density-WGAN-GP": {"method_name": "Density-WGAN-GP", "minority_f1_score": 0.8695652173913043, "boundary_confusion_index": 0.30118181818181816, "high_density_overlap_rate": 0.3680130608733634, "training_time_hours": 5.13458251953125e-05, "auc": 0.9670588235294117, "g_means": 0.8891502883619381, "precision": 0.9523809523809523, "recall": 0.8, "tp": "20", "tn": "84", "fp": "1", "fn": "5"}, "DAG-WGAN": {"method_name": "DAG-WGAN", "minority_f1_score": 0.8695652173913043, "boundary_confusion_index": 0.30118181818181816, "high_density_overlap_rate": 0.3907275985061618, "training_time_hours": 5.0837530030144586e-05, "auc": 0.9670588235294117, "g_means": 0.8891502883619381, "precision": 0.9523809523809523, "recall": 0.8, "tp": "20", "tn": "84", "fp": "1", "fn": "5"}}, "glass-1": {"Baseline": {"method_name": "Baseline", "minority_f1_score": 0.0, "boundary_confusion_index": 0.2641212121212121, "high_density_overlap_rate": 0.0, "training_time_hours": 4.882713158925374e-05, "auc": 0.7817460317460317, "g_means": 0.0, "precision": 0.0, "recall": 0.0, "tp": "0", "tn": "55", "fp": "1", "fn": "9"}, "SMOTE": {"method_name": "SMOTE", "minority_f1_score": 0.5, "boundary_confusion_index": 0.2469090909090909, "high_density_overlap_rate": 0.19831729179085633, "training_time_hours": 5.367252561781141e-05, "auc": 0.8720238095238095, "g_means": 0.6485637367560112, "precision": 0.5714285714285714, "recall": 0.4444444444444444, "tp": "4", "tn": "53", "fp": "3", "fn": "5"}, "ADASYN": {"method_name": "ADASYN", "minority_f1_score": 0.5, "boundary_confusion_index": 0.24727272727272726, "high_density_overlap_rate": 0.19152858686630436, "training_time_hours": 6.625480122036404e-05, "auc": 0.8680555555555556, "g_means": 0.6485637367560112, "precision": 0.5714285714285714, "recall": 0.4444444444444444, "tp": "4", "tn": "53", "fp": "3", "fn": "5"}, "Focal Loss": {"method_name": "Focal Loss", "minority_f1_score": 0.0, "boundary_confusion_index": 0.2641212121212121, "high_density_overlap_rate": 0.07922719121211275, "training_time_hours": 4.970497555202908e-05, "auc": 0.7817460317460317, "g_means": 0.0, "precision": 0.0, "recall": 0.0, "tp": "0", "tn": "55", "fp": "1", "fn": "9"}, "WGAN-GP": {"method_name": "WGAN-GP", "minority_f1_score": 0.0, "boundary_confusion_index": 0.2641212121212121, "high_density_overlap_rate": 0.08362162011211993, "training_time_hours": 5.135019620259603e-05, "auc": 0.7817460317460317, "g_means": 0.0, "precision": 0.0, "recall": 0.0, "tp": "0", "tn": "55", "fp": "1", "fn": "9"}, "BAGAN": {"method_name": "BAGAN", "minority_f1_score": 0.0, "boundary_confusion_index": 0.2641212121212121, "high_density_overlap_rate": 0.07903371603020026, "training_time_hours": 4.885064231024848e-05, "auc": 0.7817460317460317, "g_means": 0.0, "precision": 0.0, "recall": 0.0, "tp": "0", "tn": "55", "fp": "1", "fn": "9"}, "ADASYN-GAN": {"method_name": "ADASYN-GAN", "minority_f1_score": 0.0, "boundary_confusion_index": 0.2641212121212121, "high_density_overlap_rate": 0.13668263981649786, "training_time_hours": 4.845109250810411e-05, "auc": 0.7817460317460317, "g_means": 0.0, "precision": 0.0, "recall": 0.0, "tp": "0", "tn": "55", "fp": "1", "fn": "9"}, "Density-WGAN-GP": {"method_name": "Density-WGAN-GP", "minority_f1_score": 0.0, "boundary_confusion_index": 0.2641212121212121, "high_density_overlap_rate": 0.10080937875492191, "training_time_hours": 4.8047304153442384e-05, "auc": 0.7817460317460317, "g_means": 0.0, "precision": 0.0, "recall": 0.0, "tp": "0", "tn": "55", "fp": "1", "fn": "9"}, "DAG-WGAN": {"method_name": "DAG-WGAN", "minority_f1_score": 0.0, "boundary_confusion_index": 0.2641212121212121, "high_density_overlap_rate": 0.0641476497840675, "training_time_hours": 4.884640375773112e-05, "auc": 0.7817460317460317, "g_means": 0.0, "precision": 0.0, "recall": 0.0, "tp": "0", "tn": "55", "fp": "1", "fn": "9"}}, "predictive": {"Baseline": {"method_name": "Baseline", "minority_f1_score": 0.2857142857142857, "boundary_confusion_index": 0.098, "high_density_overlap_rate": 0.0, "training_time_hours": 0.0009357794788148669, "auc": 0.8403906955736226, "g_means": 0.408248290463863, "precision": 1.0, "recall": 0.16666666666666666, "tp": "8", "tn": "2952", "fp": "0", "fn": "40"}, "SMOTE": {"method_name": "SMOTE", "minority_f1_score": 0.359375, "boundary_confusion_index": 0.11710743801652893, "high_density_overlap_rate": 0.48748125351372074, "training_time_hours": 0.0021023902628156876, "auc": 0.8526352190605239, "g_means": 0.6855030742233383, "precision": 0.2875, "recall": 0.4791666666666667, "tp": "23", "tn": "2895", "fp": "57", "fn": "25"}, "ADASYN": {"method_name": "ADASYN", "minority_f1_score": 0.29850746268656714, "boundary_confusion_index": 0.14446280991735538, "high_density_overlap_rate": 0.4573107749659222, "training_time_hours": 0.0023062202665540908, "auc": 0.8533162545167118, "g_means": 0.6382405099251262, "precision": 0.23255813953488372, "recall": 0.4166666666666667, "tp": "20", "tn": "2886", "fp": "66", "fn": "28"}, "Focal Loss": {"method_name": "Focal Loss", "minority_f1_score": 0.2857142857142857, "boundary_confusion_index": 0.098, "high_density_overlap_rate": 0.3565330492699412, "training_time_hours": 0.0009380814764234754, "auc": 0.8403906955736226, "g_means": 0.408248290463863, "precision": 1.0, "recall": 0.16666666666666666, "tp": "8", "tn": "2952", "fp": "0", "fn": "40"}, "WGAN-GP": {"method_name": "WGAN-GP", "minority_f1_score": 0.2857142857142857, "boundary_confusion_index": 0.098, "high_density_overlap_rate": 0.4694459948435268, "training_time_hours": 0.0012752580642700195, "auc": 0.8403906955736226, "g_means": 0.408248290463863, "precision": 1.0, "recall": 0.16666666666666666, "tp": "8", "tn": "2952", "fp": "0", "fn": "40"}, "BAGAN": {"method_name": "BAGAN", "minority_f1_score": 0.2857142857142857, "boundary_confusion_index": 0.098, "high_density_overlap_rate": 0.33349457548948713, "training_time_hours": 0.001182518866327074, "auc": 0.8403906955736226, "g_means": 0.408248290463863, "precision": 1.0, "recall": 0.16666666666666666, "tp": "8", "tn": "2952", "fp": "0", "fn": "40"}, "ADASYN-GAN": {"method_name": "ADASYN-GAN", "minority_f1_score": 0.2857142857142857, "boundary_confusion_index": 0.098, "high_density_overlap_rate": 0.367976822259364, "training_time_hours": 0.00126158250702752, "auc": 0.8403906955736226, "g_means": 0.408248290463863, "precision": 1.0, "recall": 0.16666666666666666, "tp": "8", "tn": "2952", "fp": "0", "fn": "40"}, "Density-WGAN-GP": {"method_name": "Density-WGAN-GP", "minority_f1_score": 0.2857142857142857, "boundary_confusion_index": 0.098, "high_density_overlap_rate": 0.34592102817385895, "training_time_hours": 0.0012258109781477186, "auc": 0.8403906955736226, "g_means": 0.408248290463863, "precision": 1.0, "recall": 0.16666666666666666, "tp": "8", "tn": "2952", "fp": "0", "fn": "40"}, "DAG-WGAN": {"method_name": "DAG-WGAN", "minority_f1_score": 0.2857142857142857, "boundary_confusion_index": 0.098, "high_density_overlap_rate": 0.3536593744974623, "training_time_hours": 0.001219712495803833, "auc": 0.8403906955736226, "g_means": 0.408248290463863, "precision": 1.0, "recall": 0.16666666666666666, "tp": "8", "tn": "2952", "fp": "0", "fn": "40"}}, "statlog": {"Baseline": {"method_name": "Baseline", "minority_f1_score": 0.8606060606060606, "boundary_confusion_index": 0.21350617283950618, "high_density_overlap_rate": 0.0, "training_time_hours": 0.0001628932687971327, "auc": 0.9667963615332036, "g_means": 0.8769381125432852, "precision": 0.9594594594594594, "recall": 0.7802197802197802, "tp": "71", "tn": "206", "fp": "3", "fn": "20"}, "SMOTE": {"method_name": "SMOTE", "minority_f1_score": 0.8901734104046243, "boundary_confusion_index": 0.2001851851851852, "high_density_overlap_rate": 0.6372291441984202, "training_time_hours": 0.00020186099741193982, "auc": 0.9826226405173774, "g_means": 0.9087964189927737, "precision": 0.9390243902439024, "recall": 0.8461538461538461, "tp": "77", "tn": "204", "fp": "5", "fn": "14"}, "ADASYN": {"method_name": "ADASYN", "minority_f1_score": 0.9111111111111112, "boundary_confusion_index": 0.18907407407407406, "high_density_overlap_rate": 0.6181791841368949, "training_time_hours": 0.000212059219678243, "auc": 0.9843314580156686, "g_means": 0.9332301726418202, "precision": 0.9213483146067416, "recall": 0.9010989010989011, "tp": "82", "tn": "202", "fp": "7", "fn": "9"}, "Focal Loss": {"method_name": "Focal Loss", "minority_f1_score": 0.8606060606060606, "boundary_confusion_index": 0.21350617283950618, "high_density_overlap_rate": 0.6704596736305327, "training_time_hours": 0.00015897691249847412, "auc": 0.9667963615332036, "g_means": 0.8769381125432852, "precision": 0.9594594594594594, "recall": 0.7802197802197802, "tp": "71", "tn": "206", "fp": "3", "fn": "20"}, "WGAN-GP": {"method_name": "WGAN-GP", "minority_f1_score": 0.8606060606060606, "boundary_confusion_index": 0.21350617283950618, "high_density_overlap_rate": 0.6699183117861587, "training_time_hours": 0.00016059484746721057, "auc": 0.9667963615332036, "g_means": 0.8769381125432852, "precision": 0.9594594594594594, "recall": 0.7802197802197802, "tp": "71", "tn": "206", "fp": "3", "fn": "20"}, "BAGAN": {"method_name": "BAGAN", "minority_f1_score": 0.8606060606060606, "boundary_confusion_index": 0.21350617283950618, "high_density_overlap_rate": 0.6526655426751903, "training_time_hours": 0.0001603078179889255, "auc": 0.9667963615332036, "g_means": 0.8769381125432852, "precision": 0.9594594594594594, "recall": 0.7802197802197802, "tp": "71", "tn": "206", "fp": "3", "fn": "20"}, "ADASYN-GAN": {"method_name": "ADASYN-GAN", "minority_f1_score": 0.8606060606060606, "boundary_confusion_index": 0.21350617283950618, "high_density_overlap_rate": 0.6631919202508155, "training_time_hours": 0.00015861027770572238, "auc": 0.9667963615332036, "g_means": 0.8769381125432852, "precision": 0.9594594594594594, "recall": 0.7802197802197802, "tp": "71", "tn": "206", "fp": "3", "fn": "20"}, "Density-WGAN-GP": {"method_name": "Density-WGAN-GP", "minority_f1_score": 0.8606060606060606, "boundary_confusion_index": 0.21350617283950618, "high_density_overlap_rate": 0.6602418203170782, "training_time_hours": 0.00016318758328755698, "auc": 0.9667963615332036, "g_means": 0.8769381125432852, "precision": 0.9594594594594594, "recall": 0.7802197802197802, "tp": "71", "tn": "206", "fp": "3", "fn": "20"}, "DAG-WGAN": {"method_name": "DAG-WGAN", "minority_f1_score": 0.8606060606060606, "boundary_confusion_index": 0.21350617283950618, "high_density_overlap_rate": 0.6630209511580264, "training_time_hours": 0.00016249703036414251, "auc": 0.9667963615332036, "g_means": 0.8769381125432852, "precision": 0.9594594594594594, "recall": 0.7802197802197802, "tp": "71", "tn": "206", "fp": "3", "fn": "20"}}, "wisconsin-1": {"Baseline": {"method_name": "Baseline", "minority_f1_score": 0.8467153284671534, "boundary_confusion_index": 0.22022399999999998, "high_density_overlap_rate": 0.0, "training_time_hours": 9.201440546247694e-05, "auc": 0.9755434782608695, "g_means": 0.8744678621126993, "precision": 0.8923076923076924, "recall": 0.8055555555555556, "tp": "58", "tn": "131", "fp": "7", "fn": "14"}, "SMOTE": {"method_name": "SMOTE", "minority_f1_score": 0.912751677852349, "boundary_confusion_index": 0.18888, "high_density_overlap_rate": 0.5691740172878752, "training_time_hours": 0.00010418030950758193, "auc": 0.9789150563607085, "g_means": 0.9396011076759615, "precision": 0.8831168831168831, "recall": 0.9444444444444444, "tp": "68", "tn": "129", "fp": "9", "fn": "4"}, "ADASYN": {"method_name": "ADASYN", "minority_f1_score": 0.9150326797385621, "boundary_confusion_index": 0.18480000000000002, "high_density_overlap_rate": 0.5479849651127107, "training_time_hours": 0.00011785368124643962, "auc": 0.977506038647343, "g_means": 0.9458997029215717, "precision": 0.8641975308641975, "recall": 0.9722222222222222, "tp": "70", "tn": "127", "fp": "11", "fn": "2"}, "Focal Loss": {"method_name": "Focal Loss", "minority_f1_score": 0.8467153284671534, "boundary_confusion_index": 0.22022399999999998, "high_density_overlap_rate": 0.6222050695292194, "training_time_hours": 9.124755859375e-05, "auc": 0.9755434782608695, "g_means": 0.8744678621126993, "precision": 0.8923076923076924, "recall": 0.8055555555555556, "tp": "58", "tn": "131", "fp": "7", "fn": "14"}, "WGAN-GP": {"method_name": "WGAN-GP", "minority_f1_score": 0.8467153284671534, "boundary_confusion_index": 0.22022399999999998, "high_density_overlap_rate": 0.624066997559616, "training_time_hours": 9.233991305033366e-05, "auc": 0.9755434782608695, "g_means": 0.8744678621126993, "precision": 0.8923076923076924, "recall": 0.8055555555555556, "tp": "58", "tn": "131", "fp": "7", "fn": "14"}, "BAGAN": {"method_name": "BAGAN", "minority_f1_score": 0.8467153284671534, "boundary_confusion_index": 0.22022399999999998, "high_density_overlap_rate": 0.596393560778705, "training_time_hours": 9.183320734235975e-05, "auc": 0.9755434782608695, "g_means": 0.8744678621126993, "precision": 0.8923076923076924, "recall": 0.8055555555555556, "tp": "58", "tn": "131", "fp": "7", "fn": "14"}, "ADASYN-GAN": {"method_name": "ADASYN-GAN", "minority_f1_score": 0.8467153284671534, "boundary_confusion_index": 0.22022399999999998, "high_density_overlap_rate": 0.6005039922566883, "training_time_hours": 9.453945689731174e-05, "auc": 0.9755434782608695, "g_means": 0.8744678621126993, "precision": 0.8923076923076924, "recall": 0.8055555555555556, "tp": "58", "tn": "131", "fp": "7", "fn": "14"}, "Density-WGAN-GP": {"method_name": "Density-WGAN-GP", "minority_f1_score": 0.8467153284671534, "boundary_confusion_index": 0.22022399999999998, "high_density_overlap_rate": 0.6143850594343832, "training_time_hours": 9.178645080990261e-05, "auc": 0.9755434782608695, "g_means": 0.8744678621126993, "precision": 0.8923076923076924, "recall": 0.8055555555555556, "tp": "58", "tn": "131", "fp": "7", "fn": "14"}, "DAG-WGAN": {"method_name": "DAG-WGAN", "minority_f1_score": 0.8467153284671534, "boundary_confusion_index": 0.22022399999999998, "high_density_overlap_rate": 0.615625392547104, "training_time_hours": 9.285800986819797e-05, "auc": 0.9755434782608695, "g_means": 0.8744678621126993, "precision": 0.8923076923076924, "recall": 0.8055555555555556, "tp": "58", "tn": "131", "fp": "7", "fn": "14"}}, "yeast-1": {"Baseline": {"method_name": "Baseline", "minority_f1_score": 0.3, "boundary_confusion_index": 0.13707999999999998, "high_density_overlap_rate": 0.0, "training_time_hours": 0.0001189430554707845, "auc": 0.7384478266831207, "g_means": 0.42008402520840293, "precision": 1.0, "recall": 0.17647058823529413, "tp": "3", "tn": "429", "fp": "0", "fn": "14"}, "SMOTE": {"method_name": "SMOTE", "minority_f1_score": 0.5185185185185185, "boundary_confusion_index": 0.12351999999999999, "high_density_overlap_rate": 0.23372036307888144, "training_time_hours": 0.00019624465041690402, "auc": 0.8320307143836556, "g_means": 0.6394413439985283, "precision": 0.7, "recall": 0.4117647058823529, "tp": "7", "tn": "426", "fp": "3", "fn": "10"}, "ADASYN": {"method_name": "ADASYN", "minority_f1_score": 0.4827586206896552, "boundary_confusion_index": 0.141, "high_density_overlap_rate": 0.2651049344993346, "training_time_hours": 0.00022875792450375028, "auc": 0.8469765528589058, "g_means": 0.6379385420525802, "precision": 0.5833333333333334, "recall": 0.4117647058823529, "tp": "7", "tn": "424", "fp": "5", "fn": "10"}, "Focal Loss": {"method_name": "Focal Loss", "minority_f1_score": 0.3, "boundary_confusion_index": 0.13707999999999998, "high_density_overlap_rate": 0.214498356359744, "training_time_hours": 0.00012036846743689642, "auc": 0.7384478266831207, "g_means": 0.42008402520840293, "precision": 1.0, "recall": 0.17647058823529413, "tp": "3", "tn": "429", "fp": "0", "fn": "14"}, "WGAN-GP": {"method_name": "WGAN-GP", "minority_f1_score": 0.3, "boundary_confusion_index": 0.13707999999999998, "high_density_overlap_rate": 0.18077579056478843, "training_time_hours": 0.00012122511863708496, "auc": 0.7384478266831207, "g_means": 0.42008402520840293, "precision": 1.0, "recall": 0.17647058823529413, "tp": "3", "tn": "429", "fp": "0", "fn": "14"}, "BAGAN": {"method_name": "BAGAN", "minority_f1_score": 0.3, "boundary_confusion_index": 0.13707999999999998, "high_density_overlap_rate": 0.19229174735340782, "training_time_hours": 0.00012069311406877305, "auc": 0.7384478266831207, "g_means": 0.42008402520840293, "precision": 1.0, "recall": 0.17647058823529413, "tp": "3", "tn": "429", "fp": "0", "fn": "14"}, "ADASYN-GAN": {"method_name": "ADASYN-GAN", "minority_f1_score": 0.3, "boundary_confusion_index": 0.13707999999999998, "high_density_overlap_rate": 0.21812164959913205, "training_time_hours": 0.00012174917591942682, "auc": 0.7384478266831207, "g_means": 0.42008402520840293, "precision": 1.0, "recall": 0.17647058823529413, "tp": "3", "tn": "429", "fp": "0", "fn": "14"}, "Density-WGAN-GP": {"method_name": "Density-WGAN-GP", "minority_f1_score": 0.3, "boundary_confusion_index": 0.13707999999999998, "high_density_overlap_rate": 0.19483096167639258, "training_time_hours": 0.00011924266815185547, "auc": 0.7384478266831207, "g_means": 0.42008402520840293, "precision": 1.0, "recall": 0.17647058823529413, "tp": "3", "tn": "429", "fp": "0", "fn": "14"}, "DAG-WGAN": {"method_name": "DAG-WGAN", "minority_f1_score": 0.3, "boundary_confusion_index": 0.13707999999999998, "high_density_overlap_rate": 0.20347414432014999, "training_time_hours": 0.00012097875277201335, "auc": 0.7384478266831207, "g_means": 0.42008402520840293, "precision": 1.0, "recall": 0.17647058823529413, "tp": "3", "tn": "429", "fp": "0", "fn": "14"}}}, "summary": {"total_datasets": 7, "total_methods": 8, "avg_training_time": 0.00024886387681204175}}