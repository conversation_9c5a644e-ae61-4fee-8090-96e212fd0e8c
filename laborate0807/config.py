"""
DAG-WGAN框架配置模块

该模块包含DAG-WGAN框架的所有配置参数，
包括密度估计、合成、WGAN-GP、遗传算法和
动态反馈参数。

作者: 研究团队
日期: 2024-08-07
"""

from dataclasses import dataclass
from typing import Dict, List, Optional, Any
import torch


@dataclass
class DensityConfig:
    """统一密度感知层配置"""
    alpha: float = 1.0                     # 自适应带宽选择的带宽因子 (公式7)
    k_neighbors: int = 5                   # 密度计算的邻居数量
    adaptive_bandwidth: bool = True        # 启用自适应带宽选择


@dataclass
class SynthesisConfig:
    """统一自适应合成层配置"""
    k_neighbors: int = 5                   # ADASYN的邻居数量
    beta: float = 0.8                      # 过采样比例控制参数 (公式8)
    difficulty_weighting: bool = True      # 启用基于难度的权重
    quality_threshold: float = 0.3         # 样本过滤的质量阈值


@dataclass
class WGANConfig:
    """边界感知WGAN-GP组件配置"""
    latent_dim: int = 100                  # 潜在空间维度
    generator_dims: List[int] = None       # 生成器隐藏层维度
    discriminator_dims: List[int] = None   # 判别器隐藏层维度
    eta_g: float = 1e-4                    # 生成器学习率 η_G
    eta_d: float = 1e-4                    # 判别器学习率 η_D
    lambda_gp: float = 10.0                # 初始梯度惩罚系数 λ_gp
    beta1: float = 0.0                     # Adam优化器beta1
    beta2: float = 0.9                     # Adam优化器beta2
    n_critic: int = 5                      # 每次生成器更新的判别器更新次数
    batch_size: int = 64                   # 训练批次大小
    epochs: int = 100                      # 训练轮数
    device: str = 'cpu'                    # 计算设备 ('cpu' 或 'cuda')

    # 动态适应参数 (第4.3节)
    nu: float = 0.01                       # λ_gp的调整敏感度 (公式11)
    tau: float = 0.1                       # 目标稳定性阈值 (公式11)
    
    def __post_init__(self):
        """如果未提供，设置默认网络架构"""
        if self.generator_dims is None:
            self.generator_dims = [128, 256, 128]
        if self.discriminator_dims is None:
            self.discriminator_dims = [128, 256, 128]


@dataclass
class GAConfig:
    """遗传算法优化配置"""
    population_size: int = 50              # 种群大小
    max_generations: int = 100             # 最大代数
    elite_size: int = 5                    # 精英个体数量
    crossover_rate: float = 0.8            # 交叉概率
    mutation_rate: float = 0.15            # 变异概率
    tournament_size: int = 3               # 锦标赛选择大小
    early_stop_patience: int = 20          # 早停耐心值
    early_stop_threshold: float = 0.001    # 早停阈值
    random_state: int = 42                 # 随机种子

    # 适应度函数参数 (第4.2节, 公式9)
    gamma: float = 0.3                     # 重叠分数权重 (Jensen-Shannon散度)
    eta: float = 0.2                       # 损失方差惩罚权重
    
    # 优化参数边界
    parameter_bounds: Dict[str, List] = None

    def __post_init__(self):
        """如果未提供，设置默认参数边界"""
        if self.parameter_bounds is None:
            # 第4.2节更新的参数边界: α, k, λ_gp, η_G, η_D, β, C
            self.parameter_bounds = {
                'alpha_bandwidth': [0.5, 2.0, 'continuous', False],   # KDE带宽因子 α
                'k_neighbors': [3, 20, 'integer', False],             # ADASYN邻域大小 k
                'lambda_gp': [1.0, 50.0, 'continuous', False],       # WGAN-GP梯度惩罚 λ_gp
                'eta_g': [1e-5, 1e-2, 'continuous', True],           # 生成器学习率 η_G
                'eta_d': [1e-5, 1e-2, 'continuous', True],           # 判别器学习率 η_D
                'beta_oversample': [0.3, 1.5, 'continuous', False],  # 过采样比例 β
                'c_regularization': [0.01, 10.0, 'continuous', True] # 分类器正则化 C
            }


@dataclass
class FeedbackConfig:
    """动态反馈机制配置 (第4.3节)"""
    quality_threshold: float = 0.3         # 质量阈值 τ
    alpha_0: float = 1.0                   # 初始密度影响
    decay_rate: float = 0.1                # 自适应权重的衰减率
    refinement_iterations: int = 5         # 细化迭代次数
    lipschitz_constraint: float = 1.0      # Lipschitz约束
    quality_window_size: int = 10          # 质量跟踪窗口大小
    improvement_threshold: float = 0.01    # 最小改进阈值
    enable_progressive_refinement: bool = True  # 启用渐进式细化
    adaptive_threshold: bool = True        # 启用自适应阈值调整

    # 动态反馈参数 (第4.3节)
    mu: float = 0.01                       # 密度权重的适应率 (公式10)
    nu: float = 0.01                       # λ_gp的调整敏感度 (公式11)
    tau: float = 0.1                       # 目标稳定性阈值 (公式11)


@dataclass
class ExperimentConfig:
    """实验评估配置"""
    cv_folds: int = 5                      # 交叉验证折数
    cv_repeats: int = 3                    # 交叉验证重复次数
    test_size: float = 0.3                 # 测试集比例
    random_state: int = 42                 # 随机种子

    # 要评估的分类器
    classifiers: List[str] = None

    # 要比较的基线方法
    baseline_methods: List[str] = None

    # 要计算的指标
    metrics: List[str] = None

    # 统计显著性检验
    significance_alpha: float = 0.05       # 显著性水平
    
    def __post_init__(self):
        """如果未提供，设置默认值"""
        if self.classifiers is None:
            self.classifiers = ['rf', 'svm', 'lr']

        if self.baseline_methods is None:
            self.baseline_methods = ['SMOTE', 'ADASYN', 'BorderlineSMOTE', 'SMOTEENN', 'SMOTETomek']

        if self.metrics is None:
            self.metrics = ['f1_score', 'gmean', 'auc_roc', 'precision', 'recall']


@dataclass
class DAGWGANConfig:
    """整个DAG-WGAN框架的主配置类"""
    density: DensityConfig = None
    synthesis: SynthesisConfig = None
    wgan: WGANConfig = None
    ga: GAConfig = None
    feedback: FeedbackConfig = None
    experiment: ExperimentConfig = None

    # 全局设置
    random_state: int = 42
    verbose: bool = True
    log_level: str = 'INFO'
    output_dir: str = 'results'
    save_models: bool = True
    save_plots: bool = True
    
    def __post_init__(self):
        """如果未提供，初始化子配置"""
        if self.density is None:
            self.density = DensityConfig()
        if self.synthesis is None:
            self.synthesis = SynthesisConfig()
        if self.wgan is None:
            self.wgan = WGANConfig()
        if self.ga is None:
            self.ga = GAConfig()
        if self.feedback is None:
            self.feedback = FeedbackConfig()
        if self.experiment is None:
            self.experiment = ExperimentConfig()
    
    def to_dict(self) -> Dict[str, Any]:
        """将配置转换为字典"""
        return {
            'density': self.density.__dict__,
            'synthesis': self.synthesis.__dict__,
            'wgan': self.wgan.__dict__,
            'ga': self.ga.__dict__,
            'feedback': self.feedback.__dict__,
            'experiment': self.experiment.__dict__,
            'random_state': self.random_state,
            'verbose': self.verbose,
            'log_level': self.log_level,
            'output_dir': self.output_dir,
            'save_models': self.save_models,
            'save_plots': self.save_plots
        }
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'DAGWGANConfig':
        """从字典创建配置"""
        config = cls()

        # 更新子配置
        if 'density' in config_dict:
            for key, value in config_dict['density'].items():
                setattr(config.density, key, value)

        if 'synthesis' in config_dict:
            for key, value in config_dict['synthesis'].items():
                setattr(config.synthesis, key, value)

        if 'wgan' in config_dict:
            for key, value in config_dict['wgan'].items():
                setattr(config.wgan, key, value)

        if 'ga' in config_dict:
            for key, value in config_dict['ga'].items():
                setattr(config.ga, key, value)

        if 'feedback' in config_dict:
            for key, value in config_dict['feedback'].items():
                setattr(config.feedback, key, value)

        if 'experiment' in config_dict:
            for key, value in config_dict['experiment'].items():
                setattr(config.experiment, key, value)

        # 更新全局设置
        for key in ['random_state', 'verbose', 'log_level', 'output_dir', 'save_models', 'save_plots']:
            if key in config_dict:
                setattr(config, key, config_dict[key])

        return config


# 不同场景的预定义配置

def get_default_config() -> DAGWGANConfig:
    """获取通用默认配置"""
    return DAGWGANConfig()


def get_fast_config() -> DAGWGANConfig:
    """获取快速执行优化配置(用于测试/调试)"""
    config = DAGWGANConfig()

    # 降低计算复杂度
    config.ga.population_size = 20
    config.ga.max_generations = 30
    config.wgan.epochs = 50
    config.feedback.refinement_iterations = 3
    config.experiment.cv_folds = 3
    config.experiment.cv_repeats = 1
    
    return config


def get_high_performance_config() -> DAGWGANConfig:
    """获取高性能优化配置(更长训练时间)"""
    config = DAGWGANConfig()

    # 增加计算复杂度以获得更好结果
    config.ga.population_size = 100
    config.ga.max_generations = 200
    config.wgan.epochs = 200
    config.feedback.refinement_iterations = 10
    config.experiment.cv_folds = 10
    config.experiment.cv_repeats = 5

    # 如果可用则使用GPU
    config.wgan.device = 'cuda' if torch.cuda.is_available() else 'cpu'

    return config


def get_research_config() -> DAGWGANConfig:
    """获取研究/发表质量结果配置"""
    config = get_high_performance_config()

    # 研究的额外设置
    config.save_models = True
    config.save_plots = True
    config.verbose = True

    # 更全面的评估
    config.experiment.baseline_methods = [
        'SMOTE', 'ADASYN', 'BorderlineSMOTE', 'SMOTEENN', 'SMOTETomek',
        'RandomOverSampler', 'KMeansSMOTE'
    ]

    # 统计严格性
    config.experiment.significance_alpha = 0.01

    return config


def get_cpu_config() -> DAGWGANConfig:
    """获取CPU专用执行优化配置"""
    config = DAGWGANConfig()
    
    # CPU友好设置
    config.wgan.device = 'cpu'
    config.wgan.batch_size = 32  # CPU较小批次大小
    config.ga.population_size = 30  # 较小种群

    return config


def get_gpu_config() -> DAGWGANConfig:
    """获取GPU执行优化配置"""
    config = DAGWGANConfig()

    # GPU友好设置
    if torch.cuda.is_available():
        config.wgan.device = 'cuda'
        config.wgan.batch_size = 128  # GPU较大批次大小
        config.ga.population_size = 80  # 较大种群
    else:
        # 回退到CPU配置
        return get_cpu_config()

    return config


# 配置验证
def validate_config(config: DAGWGANConfig) -> List[str]:
    """
    验证配置并返回警告/错误列表

    参数:
        config: 要验证的配置

    返回:
        验证消息列表
    """
    messages = []

    # 检查设备可用性
    if config.wgan.device == 'cuda' and not torch.cuda.is_available():
        messages.append("请求CUDA但不可用，回退到CPU")
        config.wgan.device = 'cpu'

    # 检查参数边界
    if config.ga.population_size < 10:
        messages.append("种群大小很小，可能影响GA性能")

    if config.wgan.epochs < 50:
        messages.append("WGAN轮数较低，可能影响生成质量")

    # 检查一致性
    if config.synthesis.alpha > 1.0 or config.synthesis.alpha < 0.0:
        messages.append("合成alpha应在0和1之间")

    if config.feedback.quality_threshold < 0.0 or config.feedback.quality_threshold > 1.0:
        messages.append("质量阈值应在0和1之间")

    return messages


# 导出常用配置
CONFIGS = {
    'default': get_default_config,
    'fast': get_fast_config,
    'high_performance': get_high_performance_config,
    'research': get_research_config,
    'cpu': get_cpu_config,
    'gpu': get_gpu_config
}
