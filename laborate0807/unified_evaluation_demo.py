"""
统一评估演示脚本

使用论文5.2节中描述的统一实验设置和评估指标:

架构设置:
- 所有方法均采用相同的ResNet-18架构
- 均使用Adam优化算法(学习率=0.001, β₁=0.9, β₂=0.999)
- 自适应带宽KDE + k-d树加速 + 大津法阈值
- GA: 锦标赛选择(size=3), SBX交叉(η_c=15), 多项式变异(η_m=20)
- GA参数: 种群50, 运行50代

评估指标:
1. 少数类F1分数
2. 边界混淆指数(BCI)  
3. 高密度重叠率(HDOR)
4. 训练时间
5. 损失波动方差

作者: 研究团队
日期: 2024-08-07
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Agg')  # 非交互式后端
from sklearn.datasets import make_classification
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import classification_report
import logging
import os
import time
import warnings
warnings.filterwarnings('ignore')

# 导入统一组件
from config import get_default_config
from dag_wgan_framework import DAGWGANFramework
from unified_architecture import ResNet18Classifier, create_unified_optimizer
from unified_metrics import UnifiedMetricsCalculator, MetricsTracker
from unified_genetic_algorithm import UnifiedGeneticAlgorithm, UnifiedGAConfig

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def create_imbalanced_dataset(n_samples=2000, imbalance_ratio=0.05, random_state=42):
    """创建不平衡数据集"""
    weights = [1 - imbalance_ratio, imbalance_ratio]
    
    X, y = make_classification(
        n_samples=n_samples,
        n_features=20,
        n_informative=15,
        n_redundant=5,
        n_clusters_per_class=3,
        weights=weights,
        flip_y=0.01,
        random_state=random_state
    )
    
    return X, y


def evaluate_with_unified_metrics(X_train, y_train, X_test, y_test, 
                                X_generated, method_name="方法"):
    """使用统一指标评估方法"""
    
    # 使用统一的ResNet-18分类器
    classifier = ResNet18Classifier(X_train.shape[1], num_classes=2)
    optimizer = create_unified_optimizer(classifier.parameters())
    criterion = torch.nn.CrossEntropyLoss()
    
    # 训练分类器
    classifier.train()
    X_train_tensor = torch.FloatTensor(X_train)
    y_train_tensor = torch.LongTensor(y_train)
    
    for epoch in range(100):
        optimizer.zero_grad()
        outputs = classifier(X_train_tensor)
        loss = criterion(outputs, y_train_tensor)
        loss.backward()
        optimizer.step()
    
    # 预测
    classifier.eval()
    with torch.no_grad():
        X_test_tensor = torch.FloatTensor(X_test)
        outputs = classifier(X_test_tensor)
        probabilities = torch.softmax(outputs, dim=1)
        predictions = torch.argmax(outputs, dim=1)
    
    y_pred = predictions.numpy()
    y_proba = probabilities[:, 1].numpy()
    
    # 计算统一指标
    metrics_calculator = UnifiedMetricsCalculator()
    
    # 核心指标
    minority_f1 = metrics_calculator.compute_minority_f1_score(y_test, y_pred)
    bci = metrics_calculator.compute_boundary_confusion_index(X_test, y_test, y_pred, y_proba)
    hdor = metrics_calculator.compute_high_density_overlap_rate(
        X_test[y_test == 1], X_generated
    )
    
    results = {
        'minority_f1_score': minority_f1,
        'boundary_confusion_index': bci,
        'high_density_overlap_rate': hdor,
        'method_name': method_name
    }
    
    return results


def main():
    """主函数"""
    print("=== DAG-WGAN统一架构和评估指标演示 ===\n")
    
    # 创建输出目录
    output_dir = "unified_evaluation_results"
    os.makedirs(output_dir, exist_ok=True)
    
    # 1. 创建不平衡数据集
    logger.info("创建不平衡数据集...")
    X, y = create_imbalanced_dataset(n_samples=2000, imbalance_ratio=0.05)
    
    print(f"数据集创建完成:")
    print(f"  总样本数: {len(X)}")
    print(f"  特征数: {X.shape[1]}")
    print(f"  类别分布: {np.bincount(y)}")
    print(f"  不平衡比例: {np.sum(y==0)/np.sum(y==1):.1f}:1")
    
    # 2. 数据分割
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.3, random_state=42, stratify=y
    )
    
    print(f"\n数据分割:")
    print(f"  训练集: {len(X_train)} 样本")
    print(f"  测试集: {len(X_test)} 样本")
    
    # 3. 基线评估(无过采样)
    logger.info("基线评估(无过采样)...")
    baseline_results = evaluate_with_unified_metrics(
        X_train, y_train, X_test, y_test, 
        np.empty((0, X.shape[1])), "基线方法"
    )
    
    # 4. DAG-WGAN评估
    logger.info("初始化DAG-WGAN框架...")
    
    # 使用统一配置
    config = get_default_config()
    
    # 统一的框架配置
    framework_config = {
        'alpha': config.density.alpha,
        'k_neighbors': config.density.k_neighbors,
        'beta': config.synthesis.beta,
        'lr_g': 0.001,              # 统一学习率
        'lr_d': 0.001,              # 统一学习率
        'lambda_gp': config.wgan.lambda_gp,
        'n_critic': config.wgan.n_critic,
        'device': config.wgan.device,
        'latent_dim': config.wgan.latent_dim,
        'nu': config.wgan.nu,
        'tau': config.wgan.tau
    }
    
    dag_wgan = DAGWGANFramework(framework_config)
    
    try:
        # 训练DAG-WGAN
        logger.info("训练DAG-WGAN框架...")
        logger.info("使用统一架构: ResNet-18, Adam(lr=0.001, β₁=0.9, β₂=0.999)")
        logger.info("KDE: 高斯核 + 自适应带宽 + k-d树加速 + 大津法阈值")
        logger.info("GA: 锦标赛选择(size=3), SBX交叉(η_c=15), 多项式变异(η_m=20)")
        
        training_result = dag_wgan.fit(X_train, y_train, epochs=100)
        
        if training_result['status'] == 'success':
            print(f"\nDAG-WGAN训练成功!")
            
            # 获取训练历史
            history = training_result['history']
            print(f"  ADASYN样本生成数: {history['adasyn_samples']}")
            print(f"  训练时间: {history.get('training_time', 0):.2f}秒")
            
            # 生成样本用于评估
            X_adasyn = dag_wgan.synthesis_layer.density_guided_adasyn(X_train, y_train)
            X_wgan = dag_wgan.wgan.generate_samples(min(len(X_adasyn), 200))
            X_dag_wgan_combined = np.vstack([X_adasyn, X_wgan]) if len(X_wgan) > 0 else X_adasyn
            
            # DAG-WGAN评估
            dag_wgan_results = evaluate_with_unified_metrics(
                X_train, y_train, X_test, y_test,
                X_dag_wgan_combined, "DAG-WGAN"
            )
            
            # 添加训练相关指标
            dag_wgan_results['training_time'] = history.get('training_time', 0)
            dag_wgan_results.update(history.get('unified_metrics', {}))
            
            # 5. 结果对比
            print("\n" + "="*80)
            print("统一架构和评估指标 - 实验结果对比")
            print("="*80)
            
            print(f"\n📊 核心评估指标:")
            print(f"{'指标':<25} {'基线方法':<15} {'DAG-WGAN':<15} {'变化':<15}")
            print("-" * 75)
            
            # 核心指标对比
            core_metrics = [
                ('少数类F1分数', 'minority_f1_score'),
                ('边界混淆指数(BCI)', 'boundary_confusion_index'),
                ('高密度重叠率(HDOR)', 'high_density_overlap_rate')
            ]
            
            for metric_name, metric_key in core_metrics:
                baseline_val = baseline_results.get(metric_key, 0)
                dag_wgan_val = dag_wgan_results.get(metric_key, 0)
                
                if baseline_val > 0:
                    change = ((dag_wgan_val - baseline_val) / baseline_val * 100)
                    change_str = f"{change:+.1f}%"
                else:
                    change_str = "N/A"
                
                print(f"{metric_name:<25} {baseline_val:<15.4f} {dag_wgan_val:<15.4f} {change_str:<15}")
            
            # 辅助指标
            print(f"\n⚡ 辅助指标:")
            print(f"  训练时间: {dag_wgan_results.get('training_time', 0):.2f}秒")
            print(f"  生成器损失方差: {dag_wgan_results.get('generator_loss_variance', 0):.6f}")
            print(f"  判别器损失方差: {dag_wgan_results.get('discriminator_loss_variance', 0):.6f}")
            print(f"  总损失波动方差: {dag_wgan_results.get('total_loss_variance', 0):.6f}")
            
            # 6. 架构验证信息
            print(f"\n🏗️ 统一架构验证:")
            print(f"  ✅ ResNet-18架构: 生成器、判别器、分类器")
            print(f"  ✅ Adam优化器: lr=0.001, β₁=0.9, β₂=0.999")
            print(f"  ✅ 自适应带宽KDE: 高斯核 + k-d树加速")
            print(f"  ✅ 大津法动态阈值处理")
            print(f"  ✅ 遗传算法: 锦标赛选择(3) + SBX交叉(η_c=15) + 多项式变异(η_m=20)")
            print(f"  ✅ GA参数: 种群50, 运行50代")
            
            # 7. 保存结果
            results_summary = {
                'experiment_config': {
                    'architecture': 'ResNet-18',
                    'optimizer': 'Adam(lr=0.001, β₁=0.9, β₂=0.999)',
                    'kde': '高斯核 + 自适应带宽 + k-d树加速 + 大津法',
                    'ga': '锦标赛选择(3) + SBX交叉(η_c=15) + 多项式变异(η_m=20)',
                    'ga_params': '种群50, 运行50代'
                },
                'baseline_results': baseline_results,
                'dag_wgan_results': dag_wgan_results,
                'dataset_info': {
                    'total_samples': len(X),
                    'features': X.shape[1],
                    'imbalance_ratio': f"{np.sum(y==0)/np.sum(y==1):.1f}:1",
                    'class_distribution': np.bincount(y).tolist()
                }
            }
            
            import json
            results_path = os.path.join(output_dir, 'unified_evaluation_results.json')
            with open(results_path, 'w', encoding='utf-8') as f:
                json.dump(results_summary, f, indent=2, ensure_ascii=False, default=str)
            
            print(f"\n🎉 统一架构评估完成!")
            print(f"📊 结果保存至: {output_dir}")
            print(f"📋 详细结果: unified_evaluation_results.json")
            
        else:
            print(f"DAG-WGAN训练失败: {training_result.get('reason', '未知错误')}")
            
    except Exception as e:
        logger.error(f"实验过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    # 导入torch
    import torch
    main()
