{"dataset_name": "synthetic", "dataset_info": {"total_samples": 2000, "features": 20, "train_samples": 1400, "test_samples": 600, "minority_ratio": 0.107, "imbalance_ratio": 8.345794392523365}, "timestamp": "2025-08-07T10:00:47.463261", "dag_wgan": {"metrics": {"f1_score": 0.7476635514018692, "f1_macro": 0.8614804490769639, "f1_weighted": 0.9510164085813716, "precision": 0.9302325581395349, "recall": 0.625, "gmean": 0.7883538962113663, "true_positive": "40", "true_negative": "533", "false_positive": "3", "false_negative": "24", "sensitivity": 0.625, "specificity": 0.9944029850746269, "auc_roc": 0.945429104477612}, "balanced_samples": 2500, "status": "success"}, "baselines": {"SMOTE": {"rf": {"method": "SMOTE", "classifier": "rf", "original_samples": 1400, "resampled_samples": 2500, "sampling_ratio": 1.7857142857142858, "metrics": {"f1_score": 0.7964601769911506, "f1_macro": 0.887650511678648, "f1_weighted": 0.9593869082994794, "precision": 0.9183673469387755, "recall": 0.703125, "gmean": 0.8353908058552373, "true_positive": "45", "true_negative": "532", "false_positive": "4", "false_negative": "19", "sensitivity": 0.703125, "specificity": 0.9925373134328358, "auc_roc": 0.9448169309701493}, "status": "success"}}, "ADASYN": {"rf": {"method": "ADASYN", "classifier": "rf", "original_samples": 1400, "resampled_samples": 2498, "sampling_ratio": 1.7842857142857143, "metrics": {"f1_score": 0.8067226890756303, "f1_macro": 0.8927230466654748, "f1_weighted": 0.960376661302819, "precision": 0.8727272727272727, "recall": 0.75, "gmean": 0.8603518023928334, "true_positive": "48", "true_negative": "529", "false_positive": "7", "false_negative": "16", "sensitivity": 0.75, "specificity": 0.9869402985074627, "auc_roc": 0.9415665811567163}, "status": "success"}}, "BorderlineSMOTE": {"rf": {"method": "BorderlineSMOTE", "classifier": "rf", "original_samples": 1400, "resampled_samples": 2500, "sampling_ratio": 1.7857142857142858, "metrics": {"f1_score": 0.7826086956521738, "f1_macro": 0.879783610498898, "f1_weighted": 0.9562278768449877, "precision": 0.8823529411764706, "recall": 0.703125, "gmean": 0.8338190437746035, "true_positive": "45", "true_negative": "530", "false_positive": "6", "false_negative": "19", "sensitivity": 0.703125, "specificity": 0.9888059701492538, "auc_roc": 0.9466243003731343}, "status": "success"}}, "SMOTEENN": {"rf": {"method": "SMOTEENN", "classifier": "rf", "original_samples": 1400, "resampled_samples": 2376, "sampling_ratio": 1.697142857142857, "metrics": {"f1_score": 0.7377049180327868, "f1_macro": 0.8540101584598072, "f1_weighted": 0.9455036142623965, "precision": 0.7758620689655172, "recall": 0.703125, "gmean": 0.8282943891813186, "true_positive": "45", "true_negative": "523", "false_positive": "13", "false_negative": "19", "sensitivity": 0.703125, "specificity": 0.9757462686567164, "auc_roc": 0.9359112639925373}, "status": "success"}}, "SMOTETomek": {"rf": {"method": "SMOTETomek", "classifier": "rf", "original_samples": 1400, "resampled_samples": 2500, "sampling_ratio": 1.7857142857142858, "metrics": {"f1_score": 0.7964601769911506, "f1_macro": 0.887650511678648, "f1_weighted": 0.9593869082994794, "precision": 0.9183673469387755, "recall": 0.703125, "gmean": 0.8353908058552373, "true_positive": "45", "true_negative": "532", "false_positive": "4", "false_negative": "19", "sensitivity": 0.703125, "specificity": 0.9925373134328358, "auc_roc": 0.9448169309701493}, "status": "success"}}}, "cross_validation": {"fold_results": [{"repeat": 0, "fold": 0, "metrics": {"f1_score": 0.9113924050632912, "f1_macro": 0.9508418335996067, "f1_weighted": 0.982006882143296, "precision": 0.972972972972973, "recall": 0.8571428571428571, "gmean": 0.9245261507128694, "true_positive": "36", "true_negative": "357", "false_positive": "1", "false_negative": "6", "sensitivity": 0.8571428571428571, "specificity": 0.9972067039106145, "auc_roc": 0.9953445065176909}, "train_size": 1600, "test_size": 400, "balanced_size": 2856}, {"repeat": 0, "fold": 1, "metrics": {"f1_score": 0.7887323943661972, "f1_macro": 0.8840781313394772, "f1_weighted": 0.958924534863502, "precision": 1.0, "recall": 0.6511627906976745, "gmean": 0.806946584785929, "true_positive": "28", "true_negative": "357", "false_positive": "0", "false_negative": "15", "sensitivity": 0.6511627906976745, "specificity": 1.0, "auc_roc": 0.9618917334375611}, "train_size": 1600, "test_size": 400, "balanced_size": 2858}, {"repeat": 0, "fold": 2, "metrics": {"f1_score": 0.7692307692307693, "f1_macro": 0.8721500106541658, "f1_weighted": 0.9529416151715321, "precision": 0.8571428571428571, "recall": 0.6976744186046512, "gmean": 0.8293992212463343, "true_positive": "30", "true_negative": "352", "false_positive": "5", "false_negative": "13", "sensitivity": 0.6976744186046512, "specificity": 0.9859943977591037, "auc_roc": 0.9450524395804833}, "train_size": 1600, "test_size": 400, "balanced_size": 2858}, {"repeat": 0, "fold": 3, "metrics": {"f1_score": 0.8461538461538461, "f1_macro": 0.9147666737694439, "f1_weighted": 0.9686277434476881, "precision": 0.9428571428571428, "recall": 0.7674418604651163, "gmean": 0.873580257497181, "true_positive": "33", "true_negative": "355", "false_positive": "2", "false_negative": "10", "sensitivity": 0.7674418604651163, "specificity": 0.9943977591036415, "auc_roc": 0.9744642042863656}, "train_size": 1600, "test_size": 400, "balanced_size": 2858}, {"repeat": 0, "fold": 4, "metrics": {"f1_score": 0.8395061728395061, "f1_macro": 0.9107127526228129, "f1_weighted": 0.9666099177527089, "precision": 0.8947368421052632, "recall": 0.7906976744186046, "gmean": 0.8842162160478836, "true_positive": "34", "true_negative": "353", "false_positive": "4", "false_negative": "9", "sensitivity": 0.7906976744186046, "specificity": 0.988795518207283, "auc_roc": 0.9442707315484333}, "train_size": 1600, "test_size": 400, "balanced_size": 2858}, {"repeat": 1, "fold": 0, "metrics": {"f1_score": 0.8571428571428571, "f1_macro": 0.9209642363169334, "f1_weighted": 0.9713831258644536, "precision": 0.9428571428571428, "recall": 0.7857142857142857, "gmean": 0.8839258002971548, "true_positive": "33", "true_negative": "356", "false_positive": "2", "false_negative": "9", "sensitivity": 0.7857142857142857, "specificity": 0.994413407821229, "auc_roc": 0.9467943602021814}, "train_size": 1600, "test_size": 400, "balanced_size": 2856}, {"repeat": 1, "fold": 1, "metrics": {"f1_score": 0.7894736842105263, "f1_macro": 0.8836871183483571, "f1_weighted": 0.9576446641465544, "precision": 0.9090909090909091, "recall": 0.6976744186046512, "gmean": 0.8317521315658145, "true_positive": "30", "true_negative": "354", "false_positive": "3", "false_negative": "13", "sensitivity": 0.6976744186046512, "specificity": 0.9915966386554622, "auc_roc": 0.9502638264608168}, "train_size": 1600, "test_size": 400, "balanced_size": 2858}, {"repeat": 1, "fold": 2, "metrics": {"f1_score": 0.8461538461538461, "f1_macro": 0.9147666737694439, "f1_weighted": 0.9686277434476881, "precision": 0.9428571428571428, "recall": 0.7674418604651163, "gmean": 0.873580257497181, "true_positive": "33", "true_negative": "355", "false_positive": "2", "false_negative": "10", "sensitivity": 0.7674418604651163, "specificity": 0.9943977591036415, "auc_roc": 0.9750830564784053}, "train_size": 1600, "test_size": 400, "balanced_size": 2858}, {"repeat": 1, "fold": 3, "metrics": {"f1_score": 0.7631578947368421, "f1_macro": 0.8691480081419017, "f1_weighted": 0.9523502471648736, "precision": 0.8787878787878788, "recall": 0.6744186046511628, "gmean": 0.8166162462715759, "true_positive": "29", "true_negative": "353", "false_positive": "4", "false_negative": "14", "sensitivity": 0.6744186046511628, "specificity": 0.988795518207283, "auc_roc": 0.9540746531170609}, "train_size": 1600, "test_size": 400, "balanced_size": 2858}, {"repeat": 1, "fold": 4, "metrics": {"f1_score": 0.8717948717948717, "f1_macro": 0.9289722281412032, "f1_weighted": 0.9738564528730734, "precision": 0.9714285714285714, "recall": 0.7906976744186046, "gmean": 0.8879655595767568, "true_positive": "34", "true_negative": "356", "false_positive": "1", "false_negative": "9", "sensitivity": 0.7906976744186046, "specificity": 0.9971988795518207, "auc_roc": 0.974203634942349}, "train_size": 1600, "test_size": 400, "balanced_size": 2858}, {"repeat": 2, "fold": 0, "metrics": {"f1_score": 0.8684210526315789, "f1_macro": 0.9273044489677231, "f1_weighted": 0.9738223320732771, "precision": 0.9705882352941176, "recall": 0.7857142857142857, "gmean": 0.8851663985221229, "true_positive": "33", "true_negative": "357", "false_positive": "1", "false_negative": "9", "sensitivity": 0.7857142857142857, "specificity": 0.9972067039106145, "auc_roc": 0.972266560255387}, "train_size": 1600, "test_size": 400, "balanced_size": 2856}, {"repeat": 2, "fold": 1, "metrics": {"f1_score": 0.875, "f1_macro": 0.9305555555555556, "f1_weighted": 0.9741666666666667, "precision": 0.9459459459459459, "recall": 0.813953488372093, "gmean": 0.8996630062705708, "true_positive": "35", "true_negative": "355", "false_positive": "2", "false_negative": "8", "sensitivity": 0.813953488372093, "specificity": 0.9943977591036415, "auc_roc": 0.970360237118103}, "train_size": 1600, "test_size": 400, "balanced_size": 2858}, {"repeat": 2, "fold": 2, "metrics": {"f1_score": 0.8888888888888888, "f1_macro": 0.9381857518157934, "f1_weighted": 0.9768837892134137, "precision": 0.9473684210526315, "recall": 0.8372093023255814, "gmean": 0.9124248210857053, "true_positive": "36", "true_negative": "355", "false_positive": "2", "false_negative": "7", "sensitivity": 0.8372093023255814, "specificity": 0.9943977591036415, "auc_roc": 0.9562243502051984}, "train_size": 1600, "test_size": 400, "balanced_size": 2858}, {"repeat": 2, "fold": 3, "metrics": {"f1_score": 0.8641975308641974, "f1_macro": 0.9244492522193031, "f1_weighted": 0.9717468534830612, "precision": 0.9210526315789473, "recall": 0.813953488372093, "gmean": 0.8983949816710105, "true_positive": "35", "true_negative": "354", "false_positive": "3", "false_negative": "8", "sensitivity": 0.813953488372093, "specificity": 0.9915966386554622, "auc_roc": 0.9496775454367793}, "train_size": 1600, "test_size": 400, "balanced_size": 2858}, {"repeat": 2, "fold": 4, "metrics": {"f1_score": 0.8433734939759036, "f1_macro": 0.9126211960813967, "f1_weighted": 0.9669806422342089, "precision": 0.875, "recall": 0.813953488372093, "gmean": 0.8958535480598173, "true_positive": "35", "true_negative": "352", "false_positive": "5", "false_negative": "8", "sensitivity": 0.813953488372093, "specificity": 0.9859943977591037, "auc_roc": 0.974594488958374}, "train_size": 1600, "test_size": 400, "balanced_size": 2858}], "mean_metrics": {"f1_score": 0.8415079805368749, "f1_macro": 0.9122135914228744, "f1_weighted": 0.9677715473697333, "precision": 0.9315124462647683, "recall": 0.7696566998892579, "gmean": 0.8736007454071939, "true_positive": 32.93333333333333, "true_negative": 354.73333333333335, "false_positive": 2.466666666666667, "false_negative": 9.866666666666667, "sensitivity": 0.7696566998892579, "specificity": 0.9930926560568362, "auc_roc": 0.9629710885696793}, "std_metrics": {"f1_score": 0.042862252348319016, "f1_macro": 0.023549305424693315, "f1_weighted": 0.008450119240373137, "precision": 0.039870230664133366, "recall": 0.05943553680262997, "gmean": 0.034490953498384526, "true_positive": 2.462158041682585, "true_negative": 1.6519348924485153, "false_positive": 1.4544949486180951, "false_negative": 2.5785439474418292, "sensitivity": 0.05943553680262997, "specificity": 0.0040756044750013, "auc_roc": 0.014435140535107208}, "all_metrics": [{"f1_score": 0.9113924050632912, "f1_macro": 0.9508418335996067, "f1_weighted": 0.982006882143296, "precision": 0.972972972972973, "recall": 0.8571428571428571, "gmean": 0.9245261507128694, "true_positive": "36", "true_negative": "357", "false_positive": "1", "false_negative": "6", "sensitivity": 0.8571428571428571, "specificity": 0.9972067039106145, "auc_roc": 0.9953445065176909}, {"f1_score": 0.7887323943661972, "f1_macro": 0.8840781313394772, "f1_weighted": 0.958924534863502, "precision": 1.0, "recall": 0.6511627906976745, "gmean": 0.806946584785929, "true_positive": "28", "true_negative": "357", "false_positive": "0", "false_negative": "15", "sensitivity": 0.6511627906976745, "specificity": 1.0, "auc_roc": 0.9618917334375611}, {"f1_score": 0.7692307692307693, "f1_macro": 0.8721500106541658, "f1_weighted": 0.9529416151715321, "precision": 0.8571428571428571, "recall": 0.6976744186046512, "gmean": 0.8293992212463343, "true_positive": "30", "true_negative": "352", "false_positive": "5", "false_negative": "13", "sensitivity": 0.6976744186046512, "specificity": 0.9859943977591037, "auc_roc": 0.9450524395804833}, {"f1_score": 0.8461538461538461, "f1_macro": 0.9147666737694439, "f1_weighted": 0.9686277434476881, "precision": 0.9428571428571428, "recall": 0.7674418604651163, "gmean": 0.873580257497181, "true_positive": "33", "true_negative": "355", "false_positive": "2", "false_negative": "10", "sensitivity": 0.7674418604651163, "specificity": 0.9943977591036415, "auc_roc": 0.9744642042863656}, {"f1_score": 0.8395061728395061, "f1_macro": 0.9107127526228129, "f1_weighted": 0.9666099177527089, "precision": 0.8947368421052632, "recall": 0.7906976744186046, "gmean": 0.8842162160478836, "true_positive": "34", "true_negative": "353", "false_positive": "4", "false_negative": "9", "sensitivity": 0.7906976744186046, "specificity": 0.988795518207283, "auc_roc": 0.9442707315484333}, {"f1_score": 0.8571428571428571, "f1_macro": 0.9209642363169334, "f1_weighted": 0.9713831258644536, "precision": 0.9428571428571428, "recall": 0.7857142857142857, "gmean": 0.8839258002971548, "true_positive": "33", "true_negative": "356", "false_positive": "2", "false_negative": "9", "sensitivity": 0.7857142857142857, "specificity": 0.994413407821229, "auc_roc": 0.9467943602021814}, {"f1_score": 0.7894736842105263, "f1_macro": 0.8836871183483571, "f1_weighted": 0.9576446641465544, "precision": 0.9090909090909091, "recall": 0.6976744186046512, "gmean": 0.8317521315658145, "true_positive": "30", "true_negative": "354", "false_positive": "3", "false_negative": "13", "sensitivity": 0.6976744186046512, "specificity": 0.9915966386554622, "auc_roc": 0.9502638264608168}, {"f1_score": 0.8461538461538461, "f1_macro": 0.9147666737694439, "f1_weighted": 0.9686277434476881, "precision": 0.9428571428571428, "recall": 0.7674418604651163, "gmean": 0.873580257497181, "true_positive": "33", "true_negative": "355", "false_positive": "2", "false_negative": "10", "sensitivity": 0.7674418604651163, "specificity": 0.9943977591036415, "auc_roc": 0.9750830564784053}, {"f1_score": 0.7631578947368421, "f1_macro": 0.8691480081419017, "f1_weighted": 0.9523502471648736, "precision": 0.8787878787878788, "recall": 0.6744186046511628, "gmean": 0.8166162462715759, "true_positive": "29", "true_negative": "353", "false_positive": "4", "false_negative": "14", "sensitivity": 0.6744186046511628, "specificity": 0.988795518207283, "auc_roc": 0.9540746531170609}, {"f1_score": 0.8717948717948717, "f1_macro": 0.9289722281412032, "f1_weighted": 0.9738564528730734, "precision": 0.9714285714285714, "recall": 0.7906976744186046, "gmean": 0.8879655595767568, "true_positive": "34", "true_negative": "356", "false_positive": "1", "false_negative": "9", "sensitivity": 0.7906976744186046, "specificity": 0.9971988795518207, "auc_roc": 0.974203634942349}, {"f1_score": 0.8684210526315789, "f1_macro": 0.9273044489677231, "f1_weighted": 0.9738223320732771, "precision": 0.9705882352941176, "recall": 0.7857142857142857, "gmean": 0.8851663985221229, "true_positive": "33", "true_negative": "357", "false_positive": "1", "false_negative": "9", "sensitivity": 0.7857142857142857, "specificity": 0.9972067039106145, "auc_roc": 0.972266560255387}, {"f1_score": 0.875, "f1_macro": 0.9305555555555556, "f1_weighted": 0.9741666666666667, "precision": 0.9459459459459459, "recall": 0.813953488372093, "gmean": 0.8996630062705708, "true_positive": "35", "true_negative": "355", "false_positive": "2", "false_negative": "8", "sensitivity": 0.813953488372093, "specificity": 0.9943977591036415, "auc_roc": 0.970360237118103}, {"f1_score": 0.8888888888888888, "f1_macro": 0.9381857518157934, "f1_weighted": 0.9768837892134137, "precision": 0.9473684210526315, "recall": 0.8372093023255814, "gmean": 0.9124248210857053, "true_positive": "36", "true_negative": "355", "false_positive": "2", "false_negative": "7", "sensitivity": 0.8372093023255814, "specificity": 0.9943977591036415, "auc_roc": 0.9562243502051984}, {"f1_score": 0.8641975308641974, "f1_macro": 0.9244492522193031, "f1_weighted": 0.9717468534830612, "precision": 0.9210526315789473, "recall": 0.813953488372093, "gmean": 0.8983949816710105, "true_positive": "35", "true_negative": "354", "false_positive": "3", "false_negative": "8", "sensitivity": 0.813953488372093, "specificity": 0.9915966386554622, "auc_roc": 0.9496775454367793}, {"f1_score": 0.8433734939759036, "f1_macro": 0.9126211960813967, "f1_weighted": 0.9669806422342089, "precision": 0.875, "recall": 0.813953488372093, "gmean": 0.8958535480598173, "true_positive": "35", "true_negative": "352", "false_positive": "5", "false_negative": "8", "sensitivity": 0.813953488372093, "specificity": 0.9859943977591037, "auc_roc": 0.974594488958374}]}, "significance_tests": {"SMOTE": {"statistic": -Infinity, "p_value": 0.0, "is_significant": "True", "alpha": 0.05, "effect_size": 0.0, "mean_difference": -0.04879662558928133, "interpretation": "significant"}, "ADASYN": {"statistic": -Infinity, "p_value": 0.0, "is_significant": "True", "alpha": 0.05, "effect_size": 0.0, "mean_difference": -0.05905913767376103, "interpretation": "significant"}, "BorderlineSMOTE": {"statistic": -Infinity, "p_value": 0.0, "is_significant": "True", "alpha": 0.05, "effect_size": 0.0, "mean_difference": -0.034945144250304594, "interpretation": "significant"}, "SMOTEENN": {"statistic": Infinity, "p_value": 0.0, "is_significant": "True", "alpha": 0.05, "effect_size": 0.0, "mean_difference": 0.009958633369082426, "interpretation": "significant"}, "SMOTETomek": {"statistic": -Infinity, "p_value": 0.0, "is_significant": "True", "alpha": 0.05, "effect_size": 0.0, "mean_difference": -0.04879662558928133, "interpretation": "significant"}}}