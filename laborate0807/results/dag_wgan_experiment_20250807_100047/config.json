{"density": {"method": "kde", "bandwidth": "scott", "k_neighbors": 5, "adaptive_bandwidth": true}, "synthesis": {"k_neighbors": 5, "lambda_param": 0.5, "alpha": 0.8, "difficulty_weighting": true, "quality_threshold": 0.3}, "wgan": {"latent_dim": 100, "generator_dims": [128, 256, 128], "discriminator_dims": [128, 256, 128], "lr_g": 0.0001, "lr_d": 0.0001, "lambda_gp": 10.0, "beta1": 0.0, "beta2": 0.9, "n_critic": 5, "batch_size": 64, "epochs": 100, "device": "cpu"}, "ga": {"population_size": 50, "max_generations": 100, "elite_size": 5, "crossover_rate": 0.8, "mutation_rate": 0.15, "tournament_size": 3, "early_stop_patience": 20, "early_stop_threshold": 0.001, "random_state": 42, "beta": 0.3, "eta": 0.2, "parameter_bounds": {"bandwidth_h": [0.1, 5.0, "continuous", false], "lambda_param": [0.1, 1.0, "continuous", false], "k_neighbors": [3, 20, "integer", false], "k_neigh_density": [3, 15, "integer", false], "lr_g": [1e-05, 0.01, "continuous", true], "lr_d": [1e-05, 0.01, "continuous", true], "alpha_balance": [0.5, 1.0, "continuous", false]}}, "feedback": {"quality_threshold": 0.3, "alpha_0": 1.0, "decay_rate": 0.1, "refinement_iterations": 5, "lipschitz_constraint": 1.0, "quality_window_size": 10, "improvement_threshold": 0.01, "enable_progressive_refinement": true, "adaptive_threshold": true}, "experiment": {"cv_folds": 5, "cv_repeats": 3, "test_size": 0.3, "random_state": 42, "classifiers": ["rf", "svm", "lr"], "baseline_methods": ["SMOTE", "ADASYN", "BorderlineSMOTE", "SMOTEENN", "SMOTETomek"], "metrics": ["f1_score", "gmean", "auc_roc", "precision", "recall"], "significance_alpha": 0.05}, "random_state": 42, "verbose": true, "log_level": "INFO", "output_dir": "results", "save_models": true, "save_plots": true}