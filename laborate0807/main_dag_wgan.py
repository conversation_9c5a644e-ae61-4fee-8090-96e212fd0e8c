"""
DAG-WGAN框架主执行脚本

该脚本提供运行DAG-WGAN框架的主入口点，
包含全面评估和与基线方法的比较。

用法:
    python main_dag_wgan.py --dataset credit --config fast
    python main_dag_wgan.py --dataset all --config research --output results_research

作者: 研究团队
日期: 2024-08-07
"""

import argparse
import logging
import os
import sys
import json
import numpy as np
import pandas as pd
from datetime import datetime
from typing import Dict, List, Tuple, Optional
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.manifold import TSNE
from sklearn.decomposition import PCA
import matplotlib.patches as mpatches

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import DAG-WGAN components
from config import DAGWGANConfig, CONFIGS, validate_config
from dag_wgan_framework import DAGWGANFramework
from density_synthesis import AdvancedDensityEstimator, WeightedADASYNGenerator, DensityGuidedWGANGP
from genetic_optimization import MultiDimensionalGeneticAlgorithm, GAParameters, MultiObjectiveFitnessFunction
from dynamic_feedback import DynamicFeedbackController, FeedbackConfig
from experimental_evaluation import ExperimentalEvaluator

# Data loading utilities
from sklearn.datasets import make_classification
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.model_selection import train_test_split
import warnings
warnings.filterwarnings('ignore')


def setup_logging(log_level: str = 'INFO', log_file: Optional[str] = None):
    """
    Setup logging configuration.
    
    Args:
        log_level: Logging level
        log_file: Optional log file path
    """
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    handlers = [logging.StreamHandler(sys.stdout)]
    if log_file:
        handlers.append(logging.FileHandler(log_file))
    
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format=log_format,
        handlers=handlers
    )


def load_dataset(dataset_name: str, data_dir: str = '../data') -> Tuple[np.ndarray, np.ndarray, str]:
    """
    Load dataset by name.
    
    Args:
        dataset_name: Name of the dataset
        data_dir: Directory containing data files
        
    Returns:
        Tuple of (features, labels, dataset_info)
    """
    logger = logging.getLogger(__name__)
    
    if dataset_name == 'synthetic':
        # Generate synthetic imbalanced dataset
        X, y = make_classification(
            n_samples=2000, n_features=20, n_informative=15,
            n_redundant=5, n_clusters_per_class=1, weights=[0.9, 0.1],
            flip_y=0.01, random_state=42
        )
        info = f"Synthetic dataset: 2000 samples, 20 features, 10% minority class"
        
    elif dataset_name == 'credit':
        # Load credit card dataset
        try:
            data_path = os.path.join(data_dir, 'credit.data')
            if os.path.exists(data_path):
                data = pd.read_csv(data_path)
                X = data.iloc[:, :-1].values
                y = data.iloc[:, -1].values
                
                # Encode labels if necessary
                if y.dtype == 'object':
                    le = LabelEncoder()
                    y = le.fit_transform(y)
                
                info = f"Credit dataset: {len(X)} samples, {X.shape[1]} features"
            else:
                logger.warning(f"Credit dataset not found at {data_path}, using synthetic data")
                return load_dataset('synthetic', data_dir)
                
        except Exception as e:
            logger.error(f"Error loading credit dataset: {e}")
            return load_dataset('synthetic', data_dir)
    
    elif dataset_name == 'wisconsin':
        # Load Wisconsin breast cancer dataset
        try:
            data_path = os.path.join(data_dir, 'wisconsin.data')
            if os.path.exists(data_path):
                data = pd.read_csv(data_path)
                X = data.iloc[:, :-1].values
                y = data.iloc[:, -1].values
                
                if y.dtype == 'object':
                    le = LabelEncoder()
                    y = le.fit_transform(y)
                
                info = f"Wisconsin dataset: {len(X)} samples, {X.shape[1]} features"
            else:
                logger.warning(f"Wisconsin dataset not found, using synthetic data")
                return load_dataset('synthetic', data_dir)
                
        except Exception as e:
            logger.error(f"Error loading Wisconsin dataset: {e}")
            return load_dataset('synthetic', data_dir)
    
    else:
        logger.warning(f"Unknown dataset: {dataset_name}, using synthetic data")
        return load_dataset('synthetic', data_dir)
    
    # Ensure binary classification
    if len(np.unique(y)) > 2:
        # Convert to binary by grouping minority classes
        minority_class = np.argmin(np.bincount(y))
        y = (y == minority_class).astype(int)
        info += f" (converted to binary, minority class: {minority_class})"
    
    # Standardize features
    scaler = StandardScaler()
    X = scaler.fit_transform(X)
    
    logger.info(f"Loaded {info}")
    logger.info(f"Class distribution: {np.bincount(y)}")
    
    return X, y, info


class TSNEVisualizer:
    """
    t-SNE可视化类，用于比较真实样本 vs DAG-WGAN vs ADASYN样本。
    """

    def __init__(self, random_state: int = 42, figsize: Tuple[int, int] = (15, 10)):
        """
        初始化t-SNE可视化器。

        参数:
            random_state: 可重现性的随机种子
            figsize: 图形大小
        """
        self.random_state = random_state
        self.figsize = figsize
        self.colors = {
            'real_majority': '#1f77b4',      # Blue
            'real_minority': '#ff7f0e',      # Orange
            'adasyn_samples': '#2ca02c',     # Green
            'wgan_samples': '#d62728',       # Red
            'dag_wgan_samples': '#9467bd'    # Purple
        }

    def prepare_data_for_visualization(self, X_real: np.ndarray, y_real: np.ndarray,
                                     X_adasyn: np.ndarray, X_wgan: np.ndarray,
                                     max_samples: int = 2000) -> Tuple[np.ndarray, np.ndarray, List[str]]:
        """
        Prepare and combine data for t-SNE visualization.

        Args:
            X_real: Real samples
            y_real: Real labels
            X_adasyn: ADASYN generated samples
            X_wgan: WGAN generated samples
            max_samples: Maximum samples to visualize (for performance)

        Returns:
            Tuple of (combined_data, combined_labels, label_names)
        """
        # Separate real samples by class
        X_real_majority = X_real[y_real == 0]
        X_real_minority = X_real[y_real == 1]

        # Sample data if too large
        def sample_data(X, max_size):
            if len(X) > max_size:
                indices = np.random.choice(len(X), max_size, replace=False)
                return X[indices]
            return X

        # Sample each category
        max_per_category = max_samples // 5  # Distribute among 5 categories
        X_real_majority_sampled = sample_data(X_real_majority, max_per_category)
        X_real_minority_sampled = sample_data(X_real_minority, max_per_category)
        X_adasyn_sampled = sample_data(X_adasyn, max_per_category) if len(X_adasyn) > 0 else X_adasyn
        X_wgan_sampled = sample_data(X_wgan, max_per_category) if len(X_wgan) > 0 else X_wgan

        # Combine all data
        data_list = []
        labels = []
        label_names = []

        if len(X_real_majority_sampled) > 0:
            data_list.append(X_real_majority_sampled)
            labels.extend([0] * len(X_real_majority_sampled))
            label_names.extend(['Real Majority'] * len(X_real_majority_sampled))

        if len(X_real_minority_sampled) > 0:
            data_list.append(X_real_minority_sampled)
            labels.extend([1] * len(X_real_minority_sampled))
            label_names.extend(['Real Minority'] * len(X_real_minority_sampled))

        if len(X_adasyn_sampled) > 0:
            data_list.append(X_adasyn_sampled)
            labels.extend([2] * len(X_adasyn_sampled))
            label_names.extend(['ADASYN Generated'] * len(X_adasyn_sampled))

        if len(X_wgan_sampled) > 0:
            data_list.append(X_wgan_sampled)
            labels.extend([3] * len(X_wgan_sampled))
            label_names.extend(['WGAN Generated'] * len(X_wgan_sampled))

        # Combine all data
        if data_list:
            X_combined = np.vstack(data_list)
            y_combined = np.array(labels)
        else:
            X_combined = np.empty((0, X_real.shape[1]))
            y_combined = np.array([])

        return X_combined, y_combined, label_names

    def create_tsne_visualization(self, X_real: np.ndarray, y_real: np.ndarray,
                                X_adasyn: np.ndarray, X_wgan: np.ndarray,
                                title: str = "t-SNE Visualization: Real vs Generated Samples",
                                save_path: Optional[str] = None) -> plt.Figure:
        """
        Create comprehensive t-SNE visualization comparing all sample types.

        Args:
            X_real: Real samples
            y_real: Real labels
            X_adasyn: ADASYN generated samples
            X_wgan: WGAN generated samples
            title: Plot title
            save_path: Path to save the plot

        Returns:
            Matplotlib figure
        """
        # Prepare data
        X_combined, y_combined, label_names = self.prepare_data_for_visualization(
            X_real, y_real, X_adasyn, X_wgan
        )

        if len(X_combined) == 0:
            print("No data available for visualization")
            return None

        # Apply t-SNE
        print(f"Applying t-SNE to {len(X_combined)} samples...")
        tsne = TSNE(n_components=2, random_state=self.random_state,
                   perplexity=min(30, len(X_combined)//4), n_iter=1000)
        X_tsne = tsne.fit_transform(X_combined)

        # Create visualization
        fig, axes = plt.subplots(2, 2, figsize=self.figsize)
        fig.suptitle(title, fontsize=16, fontweight='bold')

        # Plot 1: All samples together
        ax1 = axes[0, 0]
        scatter_plots = []

        # Real majority samples
        mask_real_maj = y_combined == 0
        if np.any(mask_real_maj):
            scatter = ax1.scatter(X_tsne[mask_real_maj, 0], X_tsne[mask_real_maj, 1],
                                c=self.colors['real_majority'], alpha=0.6, s=20,
                                label='Real Majority')
            scatter_plots.append(scatter)

        # Real minority samples
        mask_real_min = y_combined == 1
        if np.any(mask_real_min):
            scatter = ax1.scatter(X_tsne[mask_real_min, 0], X_tsne[mask_real_min, 1],
                                c=self.colors['real_minority'], alpha=0.8, s=30,
                                label='Real Minority')
            scatter_plots.append(scatter)

        # ADASYN samples
        mask_adasyn = y_combined == 2
        if np.any(mask_adasyn):
            scatter = ax1.scatter(X_tsne[mask_adasyn, 0], X_tsne[mask_adasyn, 1],
                                c=self.colors['adasyn_samples'], alpha=0.7, s=25,
                                marker='^', label='ADASYN Generated')
            scatter_plots.append(scatter)

        # WGAN samples
        mask_wgan = y_combined == 3
        if np.any(mask_wgan):
            scatter = ax1.scatter(X_tsne[mask_wgan, 0], X_tsne[mask_wgan, 1],
                                c=self.colors['wgan_samples'], alpha=0.7, s=25,
                                marker='s', label='WGAN Generated')
            scatter_plots.append(scatter)

        ax1.set_title('All Samples Comparison')
        ax1.set_xlabel('t-SNE Component 1')
        ax1.set_ylabel('t-SNE Component 2')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # Plot 2: Real vs ADASYN
        ax2 = axes[0, 1]
        if np.any(mask_real_min):
            ax2.scatter(X_tsne[mask_real_min, 0], X_tsne[mask_real_min, 1],
                       c=self.colors['real_minority'], alpha=0.8, s=30,
                       label='Real Minority')
        if np.any(mask_adasyn):
            ax2.scatter(X_tsne[mask_adasyn, 0], X_tsne[mask_adasyn, 1],
                       c=self.colors['adasyn_samples'], alpha=0.7, s=25,
                       marker='^', label='ADASYN Generated')
        ax2.set_title('Real Minority vs ADASYN')
        ax2.set_xlabel('t-SNE Component 1')
        ax2.set_ylabel('t-SNE Component 2')
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        # Plot 3: Real vs WGAN
        ax3 = axes[1, 0]
        if np.any(mask_real_min):
            ax3.scatter(X_tsne[mask_real_min, 0], X_tsne[mask_real_min, 1],
                       c=self.colors['real_minority'], alpha=0.8, s=30,
                       label='Real Minority')
        if np.any(mask_wgan):
            ax3.scatter(X_tsne[mask_wgan, 0], X_tsne[mask_wgan, 1],
                       c=self.colors['wgan_samples'], alpha=0.7, s=25,
                       marker='s', label='WGAN Generated')
        ax3.set_title('Real Minority vs WGAN')
        ax3.set_xlabel('t-SNE Component 1')
        ax3.set_ylabel('t-SNE Component 2')
        ax3.legend()
        ax3.grid(True, alpha=0.3)

        # Plot 4: ADASYN vs WGAN
        ax4 = axes[1, 1]
        if np.any(mask_adasyn):
            ax4.scatter(X_tsne[mask_adasyn, 0], X_tsne[mask_adasyn, 1],
                       c=self.colors['adasyn_samples'], alpha=0.7, s=25,
                       marker='^', label='ADASYN Generated')
        if np.any(mask_wgan):
            ax4.scatter(X_tsne[mask_wgan, 0], X_tsne[mask_wgan, 1],
                       c=self.colors['wgan_samples'], alpha=0.7, s=25,
                       marker='s', label='WGAN Generated')
        ax4.set_title('ADASYN vs WGAN Comparison')
        ax4.set_xlabel('t-SNE Component 1')
        ax4.set_ylabel('t-SNE Component 2')
        ax4.legend()
        ax4.grid(True, alpha=0.3)

        plt.tight_layout()

        # Save plot if path provided
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"t-SNE visualization saved to: {save_path}")

        return fig

    def create_distribution_analysis(self, X_real: np.ndarray, y_real: np.ndarray,
                                   X_adasyn: np.ndarray, X_wgan: np.ndarray,
                                   save_path: Optional[str] = None) -> plt.Figure:
        """
        Create distribution analysis plots for generated vs real samples.

        Args:
            X_real: Real samples
            y_real: Real labels
            X_adasyn: ADASYN generated samples
            X_wgan: WGAN generated samples
            save_path: Path to save the plot

        Returns:
            Matplotlib figure
        """
        X_real_minority = X_real[y_real == 1]

        # Select first few features for distribution comparison
        n_features = min(4, X_real.shape[1])

        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('Feature Distribution Analysis: Real vs Generated Samples',
                    fontsize=16, fontweight='bold')

        axes = axes.flatten()

        for i in range(n_features):
            ax = axes[i]

            # Plot distributions
            if len(X_real_minority) > 0:
                ax.hist(X_real_minority[:, i], bins=30, alpha=0.7,
                       color=self.colors['real_minority'], label='Real Minority', density=True)

            if len(X_adasyn) > 0:
                ax.hist(X_adasyn[:, i], bins=30, alpha=0.6,
                       color=self.colors['adasyn_samples'], label='ADASYN', density=True)

            if len(X_wgan) > 0:
                ax.hist(X_wgan[:, i], bins=30, alpha=0.6,
                       color=self.colors['wgan_samples'], label='WGAN', density=True)

            ax.set_title(f'Feature {i+1} Distribution')
            ax.set_xlabel('Feature Value')
            ax.set_ylabel('Density')
            ax.legend()
            ax.grid(True, alpha=0.3)

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Distribution analysis saved to: {save_path}")

        return fig


class DAGWGANExperiment:
    """
    Main experiment coordinator for DAG-WGAN framework.
    """
    
    def __init__(self, config: DAGWGANConfig, output_dir: str = 'results', enable_visualization: bool = False):
        """
        Initialize experiment coordinator.

        Args:
            config: DAG-WGAN configuration
            output_dir: Output directory for results
            enable_visualization: Whether to generate t-SNE visualizations
        """
        self.config = config
        self.output_dir = output_dir
        self.enable_visualization = enable_visualization
        self.logger = logging.getLogger(__name__)

        # Create output directory
        os.makedirs(output_dir, exist_ok=True)

        # Initialize components
        self.evaluator = ExperimentalEvaluator(output_dir, config.random_state)
        if enable_visualization:
            self.tsne_visualizer = TSNEVisualizer(config.random_state)
        
    def run_single_dataset_experiment(self, dataset_name: str, X: np.ndarray, y: np.ndarray) -> Dict:
        """
        Run experiment on a single dataset.
        
        Args:
            dataset_name: Name of the dataset
            X: Features
            y: Labels
            
        Returns:
            Experiment results dictionary
        """
        self.logger.info(f"Starting experiment on {dataset_name}")
        
        # Initialize DAG-WGAN framework with updated parameters
        framework_config = {
            'alpha': self.config.density.alpha,           # KDE bandwidth factor α
            'k_neighbors': self.config.density.k_neighbors,
            'beta': self.config.synthesis.beta,           # Oversampling ratio β
            'lr_g': self.config.wgan.eta_g,              # Generator learning rate η_G
            'lr_d': self.config.wgan.eta_d,              # Critic learning rate η_D
            'lambda_gp': self.config.wgan.lambda_gp,     # Gradient penalty λ_gp
            'n_critic': self.config.wgan.n_critic,
            'device': self.config.wgan.device,
            'latent_dim': self.config.wgan.latent_dim,
            'nu': self.config.wgan.nu,                   # Dynamic adaptation parameters
            'tau': self.config.wgan.tau
        }
        
        dag_wgan_framework = DAGWGANFramework(framework_config)
        
        # Run comprehensive evaluation
        results = self.evaluator.comprehensive_evaluation(
            dag_wgan_framework, X, y, dataset_name
        )
        
        # Generate and save report
        report = self.evaluator.generate_comparison_report(results)
        report_path = os.path.join(self.output_dir, dataset_name, 'report.txt')
        with open(report_path, 'w') as f:
            f.write(report)
        
        # Generate t-SNE visualizations if requested
        if hasattr(self, 'enable_visualization') and self.enable_visualization:
            self.generate_tsne_visualizations(dataset_name, X, y, dag_wgan_framework)

        self.logger.info(f"Experiment completed for {dataset_name}")
        return results

    def generate_tsne_visualizations(self, dataset_name: str, X: np.ndarray, y: np.ndarray,
                                   dag_wgan_framework) -> None:
        """
        Generate comprehensive t-SNE visualizations for the experiment.

        Args:
            dataset_name: Name of the dataset
            X: Original features
            y: Original labels
            dag_wgan_framework: Trained DAG-WGAN framework
        """
        try:
            self.logger.info(f"Generating t-SNE visualizations for {dataset_name}")

            # Split data for visualization
            from sklearn.model_selection import train_test_split
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.3, random_state=self.config.random_state, stratify=y
            )

            # Generate samples using different methods
            X_minority = X_train[y_train == 1]
            X_majority = X_train[y_train == 0]

            # Generate ADASYN samples only
            X_adasyn = dag_wgan_framework.synthesis_layer.density_guided_adasyn(X_train, y_train)

            # Generate WGAN samples only
            if hasattr(dag_wgan_framework, 'wgan') and dag_wgan_framework.wgan is not None:
                n_wgan_samples = min(len(X_adasyn), 500)  # Limit for visualization
                X_wgan = dag_wgan_framework.wgan.generate_samples(n_wgan_samples)
            else:
                X_wgan = np.empty((0, X_train.shape[1]))

            # Create visualization directory
            viz_dir = os.path.join(self.output_dir, dataset_name, 'visualizations')
            os.makedirs(viz_dir, exist_ok=True)

            # Generate t-SNE visualization
            tsne_path = os.path.join(viz_dir, 'tsne_comparison.png')
            fig_tsne = self.tsne_visualizer.create_tsne_visualization(
                X_train, y_train, X_adasyn, X_wgan,
                title=f't-SNE Visualization: {dataset_name.upper()} Dataset',
                save_path=tsne_path
            )

            # Generate distribution analysis
            dist_path = os.path.join(viz_dir, 'distribution_analysis.png')
            fig_dist = self.tsne_visualizer.create_distribution_analysis(
                X_train, y_train, X_adasyn, X_wgan,
                save_path=dist_path
            )

            # Generate sample statistics
            stats_path = os.path.join(viz_dir, 'sample_statistics.txt')
            self.generate_sample_statistics(X_train, y_train, X_adasyn, X_wgan, stats_path)

            # Close figures to save memory
            if fig_tsne:
                plt.close(fig_tsne)
            if fig_dist:
                plt.close(fig_dist)

            self.logger.info(f"t-SNE visualizations saved to: {viz_dir}")

        except Exception as e:
            self.logger.error(f"Error generating t-SNE visualizations: {e}")

    def generate_sample_statistics(self, X_train: np.ndarray, y_train: np.ndarray,
                                 X_adasyn: np.ndarray, X_wgan: np.ndarray,
                                 save_path: str) -> None:
        """
        Generate detailed statistics about the samples.

        Args:
            X_train: Training data
            y_train: Training labels
            X_adasyn: ADASYN generated samples
            X_wgan: WGAN generated samples
            save_path: Path to save statistics
        """
        X_minority = X_train[y_train == 1]
        X_majority = X_train[y_train == 0]

        stats = []
        stats.append("=== DAG-WGAN Sample Generation Statistics ===\n")
        stats.append(f"Dataset: {os.path.basename(os.path.dirname(save_path))}\n")
        stats.append(f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

        # Original data statistics
        stats.append("Original Data:")
        stats.append(f"  Total samples: {len(X_train)}")
        stats.append(f"  Majority class: {len(X_majority)}")
        stats.append(f"  Minority class: {len(X_minority)}")
        stats.append(f"  Imbalance ratio: {len(X_majority)/len(X_minority):.2f}:1")
        stats.append(f"  Features: {X_train.shape[1]}\n")

        # ADASYN statistics
        stats.append("ADASYN Generated Samples:")
        stats.append(f"  Count: {len(X_adasyn)}")
        if len(X_adasyn) > 0:
            stats.append(f"  Mean feature values: {np.mean(X_adasyn, axis=0)[:5]}...")
            stats.append(f"  Std feature values: {np.std(X_adasyn, axis=0)[:5]}...")
        stats.append("")

        # WGAN statistics
        stats.append("WGAN Generated Samples:")
        stats.append(f"  Count: {len(X_wgan)}")
        if len(X_wgan) > 0:
            stats.append(f"  Mean feature values: {np.mean(X_wgan, axis=0)[:5]}...")
            stats.append(f"  Std feature values: {np.std(X_wgan, axis=0)[:5]}...")
        stats.append("")

        # Comparison statistics
        if len(X_adasyn) > 0 and len(X_minority) > 0:
            # Compute distance between ADASYN and real minority samples
            from scipy.spatial.distance import cdist
            distances_adasyn = cdist(X_adasyn, X_minority, metric='euclidean')
            min_distances_adasyn = np.min(distances_adasyn, axis=1)

            stats.append("ADASYN Quality Metrics:")
            stats.append(f"  Mean distance to nearest real sample: {np.mean(min_distances_adasyn):.4f}")
            stats.append(f"  Std distance to nearest real sample: {np.std(min_distances_adasyn):.4f}")
            stats.append("")

        if len(X_wgan) > 0 and len(X_minority) > 0:
            # Compute distance between WGAN and real minority samples
            distances_wgan = cdist(X_wgan, X_minority, metric='euclidean')
            min_distances_wgan = np.min(distances_wgan, axis=1)

            stats.append("WGAN Quality Metrics:")
            stats.append(f"  Mean distance to nearest real sample: {np.mean(min_distances_wgan):.4f}")
            stats.append(f"  Std distance to nearest real sample: {np.std(min_distances_wgan):.4f}")
            stats.append("")

        # Final balanced dataset
        total_synthetic = len(X_adasyn) + len(X_wgan)
        final_minority = len(X_minority) + total_synthetic

        stats.append("Final Balanced Dataset:")
        stats.append(f"  Majority class: {len(X_majority)}")
        stats.append(f"  Minority class (real + synthetic): {final_minority}")
        stats.append(f"  Final balance ratio: {len(X_majority)/final_minority:.2f}:1")
        stats.append(f"  Synthetic sample ratio: {total_synthetic/final_minority:.2%}")

        # Save statistics
        with open(save_path, 'w') as f:
            f.write('\n'.join(stats))

        print(f"Sample statistics saved to: {save_path}")
    
    def run_optimization_experiment(self, dataset_name: str, X: np.ndarray, y: np.ndarray) -> Dict:
        """
        Run genetic algorithm optimization experiment.
        
        Args:
            dataset_name: Name of the dataset
            X: Features
            y: Labels
            
        Returns:
            Optimization results dictionary
        """
        self.logger.info(f"Starting GA optimization experiment on {dataset_name}")
        
        # Split data for optimization
        X_train, X_val, y_train, y_val = train_test_split(
            X, y, test_size=0.3, random_state=self.config.random_state, stratify=y
        )
        
        # Initialize GA components
        ga_params = GAParameters(
            population_size=self.config.ga.population_size,
            max_generations=self.config.ga.max_generations,
            elite_size=self.config.ga.elite_size,
            crossover_rate=self.config.ga.crossover_rate,
            mutation_rate=self.config.ga.mutation_rate,
            tournament_size=self.config.ga.tournament_size,
            early_stop_patience=self.config.ga.early_stop_patience,
            early_stop_threshold=self.config.ga.early_stop_threshold,
            random_state=self.config.random_state
        )
        
        fitness_function = MultiObjectiveFitnessFunction(
            beta=self.config.ga.beta,
            eta=self.config.ga.eta
        )
        
        ga_optimizer = MultiDimensionalGeneticAlgorithm(ga_params, fitness_function)
        
        # Create framework instance for optimization
        framework_config = {'random_state': self.config.random_state}
        dag_wgan_framework = DAGWGANFramework(framework_config)
        
        # Run optimization
        optimization_results = ga_optimizer.optimize(
            X_train, y_train, X_val, y_val, dag_wgan_framework
        )
        
        # Save optimization results
        opt_dir = os.path.join(self.output_dir, dataset_name, 'optimization')
        os.makedirs(opt_dir, exist_ok=True)
        
        opt_results_path = os.path.join(opt_dir, 'ga_optimization_results.json')
        with open(opt_results_path, 'w') as f:
            json.dump(optimization_results, f, indent=2, default=str)
        
        self.logger.info(f"GA optimization completed for {dataset_name}")
        return optimization_results
    
    def run_multi_dataset_experiment(self, dataset_names: List[str]) -> Dict:
        """
        Run experiments on multiple datasets.
        
        Args:
            dataset_names: List of dataset names
            
        Returns:
            Combined results dictionary
        """
        all_results = {}
        
        for dataset_name in dataset_names:
            try:
                # Load dataset
                X, y, info = load_dataset(dataset_name)
                
                # Run experiment
                results = self.run_single_dataset_experiment(dataset_name, X, y)
                all_results[dataset_name] = results
                
                # Optionally run optimization
                if self.config.ga.max_generations > 0:
                    opt_results = self.run_optimization_experiment(dataset_name, X, y)
                    all_results[dataset_name]['optimization'] = opt_results
                
            except Exception as e:
                self.logger.error(f"Error processing dataset {dataset_name}: {e}")
                all_results[dataset_name] = {'status': 'failed', 'error': str(e)}
        
        # Save combined results
        combined_results_path = os.path.join(self.output_dir, 'all_experiments_results.json')
        with open(combined_results_path, 'w') as f:
            json.dump(all_results, f, indent=2, default=str)
        
        return all_results


def main():
    """Main execution function."""
    parser = argparse.ArgumentParser(description='DAG-WGAN Framework Experiment Runner')
    
    parser.add_argument('--dataset', type=str, default='synthetic',
                       choices=['synthetic', 'credit', 'wisconsin', 'all'],
                       help='Dataset to use for experiments')
    
    parser.add_argument('--config', type=str, default='default',
                       choices=list(CONFIGS.keys()),
                       help='Configuration preset to use')
    
    parser.add_argument('--output', type=str, default='results',
                       help='Output directory for results')
    
    parser.add_argument('--optimize', action='store_true',
                       help='Run genetic algorithm optimization')
    
    parser.add_argument('--log-level', type=str, default='INFO',
                       choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       help='Logging level')
    
    parser.add_argument('--log-file', type=str, default=None,
                       help='Log file path (optional)')

    parser.add_argument('--visualize', action='store_true',
                       help='Generate t-SNE visualizations')

    parser.add_argument('--no-plots', action='store_true',
                       help='Disable interactive plots (for headless environments)')

    args = parser.parse_args()
    
    # Setup logging
    setup_logging(args.log_level, args.log_file)
    logger = logging.getLogger(__name__)

    # Configure matplotlib for headless environments
    if args.no_plots:
        import matplotlib
        matplotlib.use('Agg')
    
    # Load configuration
    config = CONFIGS[args.config]()
    
    # Validate configuration
    validation_messages = validate_config(config)
    for message in validation_messages:
        logger.warning(message)
    
    # Create output directory with timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = os.path.join(args.output, f"dag_wgan_experiment_{timestamp}")
    
    logger.info(f"Starting DAG-WGAN experiment with config: {args.config}")
    logger.info(f"Output directory: {output_dir}")
    
    # Initialize experiment
    experiment = DAGWGANExperiment(config, output_dir, enable_visualization=args.visualize)
    
    # Save configuration
    config_path = os.path.join(output_dir, 'config.json')
    with open(config_path, 'w') as f:
        json.dump(config.to_dict(), f, indent=2)
    
    try:
        if args.dataset == 'all':
            # Run on all available datasets
            dataset_names = ['synthetic', 'credit', 'wisconsin']
            results = experiment.run_multi_dataset_experiment(dataset_names)
        else:
            # Run on single dataset
            X, y, info = load_dataset(args.dataset)
            
            if args.optimize:
                # Run optimization experiment
                results = experiment.run_optimization_experiment(args.dataset, X, y)
            else:
                # Run standard experiment
                results = experiment.run_single_dataset_experiment(args.dataset, X, y)
        
        logger.info("Experiment completed successfully!")
        logger.info(f"Results saved to: {output_dir}")
        
        # Print summary
        if isinstance(results, dict) and 'dag_wgan' in results:
            if results['dag_wgan']['status'] == 'success':
                metrics = results['dag_wgan']['metrics']
                print(f"\nDAG-WGAN Results Summary:")
                print(f"F1-Score: {metrics['f1_score']:.4f}")
                print(f"G-mean: {metrics['gmean']:.4f}")
                print(f"AUC-ROC: {metrics.get('auc_roc', 'N/A')}")
        
    except Exception as e:
        logger.error(f"Experiment failed: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
