"""
Test script to check all imports in the DAG-WGAN framework.

This script tests all imports to identify any missing dependencies
or import errors before running the main framework.

Author: Research Team
Date: 2024-08-07
"""

import sys
import traceback

def test_import(module_name, description=""):
    """Test importing a module and report results."""
    try:
        __import__(module_name)
        print(f"✓ {module_name} {description}")
        return True
    except ImportError as e:
        print(f"✗ {module_name} {description} - ERROR: {e}")
        return False
    except Exception as e:
        print(f"✗ {module_name} {description} - UNEXPECTED ERROR: {e}")
        return False

def test_framework_imports():
    """Test all framework module imports."""
    print("=== Testing DAG-WGAN Framework Imports ===\n")
    
    # Test core scientific libraries
    print("Core Scientific Libraries:")
    test_import("numpy", "(NumPy)")
    test_import("scipy", "(SciPy)")
    test_import("pandas", "(Pandas)")
    print()
    
    # Test machine learning libraries
    print("Machine Learning Libraries:")
    test_import("sklearn", "(Scikit-learn)")
    test_import("imblearn", "(Imbalanced-learn)")
    print()
    
    # Test deep learning
    print("Deep Learning Libraries:")
    test_import("torch", "(PyTorch)")
    print()
    
    # Test visualization
    print("Visualization Libraries:")
    test_import("matplotlib", "(Matplotlib)")
    test_import("seaborn", "(Seaborn)")
    print()
    
    # Test specific scipy functions
    print("Specific Function Imports:")
    try:
        from scipy.stats import wasserstein_distance
        print("✓ scipy.stats.wasserstein_distance")
    except ImportError as e:
        print(f"✗ scipy.stats.wasserstein_distance - ERROR: {e}")
    
    try:
        from scipy.stats import gaussian_kde
        print("✓ scipy.stats.gaussian_kde")
    except ImportError as e:
        print(f"✗ scipy.stats.gaussian_kde - ERROR: {e}")
    
    try:
        from sklearn.neighbors import NearestNeighbors, KernelDensity
        print("✓ sklearn.neighbors imports")
    except ImportError as e:
        print(f"✗ sklearn.neighbors imports - ERROR: {e}")
    
    try:
        from imblearn.over_sampling import SMOTE, ADASYN, BorderlineSMOTE
        print("✓ imblearn.over_sampling imports")
    except ImportError as e:
        print(f"✗ imblearn.over_sampling imports - ERROR: {e}")
    
    print()
    
    # Test framework modules
    print("DAG-WGAN Framework Modules:")
    
    modules_to_test = [
        ("config", "Configuration module"),
        ("dag_wgan_framework", "Main framework"),
        ("density_synthesis", "Density synthesis module"),
        ("genetic_optimization", "Genetic algorithm module"),
        ("dynamic_feedback", "Dynamic feedback module"),
        ("experimental_evaluation", "Evaluation module")
    ]
    
    all_success = True
    for module, description in modules_to_test:
        try:
            __import__(module)
            print(f"✓ {module} - {description}")
        except Exception as e:
            print(f"✗ {module} - {description} - ERROR: {e}")
            print(f"  Traceback: {traceback.format_exc()}")
            all_success = False
    
    print("\n" + "="*50)
    if all_success:
        print("🎉 All framework modules imported successfully!")
    else:
        print("❌ Some modules failed to import. Check errors above.")
    
    return all_success

def test_basic_functionality():
    """Test basic functionality of key components."""
    print("\n=== Testing Basic Functionality ===\n")
    
    try:
        # Test configuration
        from config import get_default_config
        config = get_default_config()
        print("✓ Configuration system working")
        
        # Test numpy and torch
        import numpy as np
        import torch
        
        # Create test data
        X = np.random.randn(100, 10)
        y = np.random.randint(0, 2, 100)
        print("✓ Test data creation working")
        
        # Test torch tensor creation
        tensor = torch.FloatTensor(X)
        print("✓ PyTorch tensor creation working")
        
        # Test sklearn
        from sklearn.ensemble import RandomForestClassifier
        clf = RandomForestClassifier(n_estimators=10)
        print("✓ Scikit-learn classifier creation working")
        
        print("\n🎉 Basic functionality tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Basic functionality test failed: {e}")
        print(f"Traceback: {traceback.format_exc()}")
        return False

def main():
    """Main test function."""
    print("DAG-WGAN Framework Import and Functionality Test")
    print("=" * 60)
    
    # Test imports
    imports_ok = test_framework_imports()
    
    # Test basic functionality if imports are OK
    if imports_ok:
        functionality_ok = test_basic_functionality()
    else:
        functionality_ok = False
    
    # Final summary
    print("\n" + "=" * 60)
    print("FINAL SUMMARY:")
    
    if imports_ok and functionality_ok:
        print("🎉 All tests passed! The framework is ready to use.")
        print("\nYou can now run:")
        print("  python demo_dag_wgan.py")
        print("  python main_dag_wgan.py --dataset synthetic --config fast")
        return 0
    else:
        print("❌ Some tests failed. Please install missing dependencies:")
        print("  pip install -r requirements.txt")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
