"""
统一架构模块 - ResNet-18实现

该模块为DAG-WGAN框架提供统一的ResNet-18架构，
确保所有方法使用相同的网络结构和优化器配置。

实验设置:
- 架构: ResNet-18
- 优化器: Adam (lr=0.001, β₁=0.9, β₂=0.999)
- KDE: 高斯核自适应带宽
- 加速: k-d树
- 阈值: 大津法(<PERSON><PERSON>'s method)
- GA: 锦标赛选择(size=3), SBX交叉(η_c=15), 多项式变异(η_m=20)
- GA参数: 种群50, 运行50代

作者: 研究团队
日期: 2024-08-07
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
import numpy as np
from sklearn.neighbors import KDTree
from sklearn.neighbors import NearestNeighbors
from typing import Tuple, Optional
import logging

# 替代skimage的大津法实现
def threshold_otsu_simple(image):
    """
    简化的大津法阈值实现，替代skimage.filters.threshold_otsu
    """
    # 将图像转换为直方图
    hist, bin_edges = np.histogram(image, bins=256)
    bin_centers = (bin_edges[:-1] + bin_edges[1:]) / 2

    # 计算权重
    weight1 = np.cumsum(hist)
    weight2 = np.cumsum(hist[::-1])[::-1]

    # 计算均值
    mean1 = np.cumsum(hist * bin_centers) / weight1
    mean2 = (np.cumsum((hist * bin_centers)[::-1]) / weight2[::-1])[::-1]

    # 计算类间方差
    variance12 = weight1[:-1] * weight2[1:] * (mean1[:-1] - mean2[1:]) ** 2

    # 找到最大方差对应的阈值
    idx = np.argmax(variance12)
    threshold = bin_centers[idx]

    return threshold

logger = logging.getLogger(__name__)


# BasicBlock类已移除，使用简化的全连接架构


class ResNet18Classifier(nn.Module):
    """
    统一的ResNet-18分类器架构
    用于所有基线方法和DAG-WGAN的分类任务，修复维度匹配问题
    """

    def __init__(self, input_dim: int, num_classes: int = 2):
        """
        初始化ResNet-18分类器

        参数:
            input_dim: 输入特征维度
            num_classes: 分类类别数
        """
        super(ResNet18Classifier, self).__init__()
        self.input_dim = input_dim
        self.num_classes = num_classes

        # 简化的分类器架构，避免维度不匹配
        self.main = nn.Sequential(
            # 第一层: input_dim -> 128
            nn.Linear(input_dim, 128),
            nn.BatchNorm1d(128),
            nn.ReLU(True),
            nn.Dropout(0.3),

            # 第二层: 128 -> 256
            nn.Linear(128, 256),
            nn.BatchNorm1d(256),
            nn.ReLU(True),
            nn.Dropout(0.3),

            # 第三层: 256 -> 512
            nn.Linear(256, 512),
            nn.BatchNorm1d(512),
            nn.ReLU(True),
            nn.Dropout(0.3),

            # 第四层: 512 -> 256
            nn.Linear(512, 256),
            nn.BatchNorm1d(256),
            nn.ReLU(True),
            nn.Dropout(0.3),

            # 第五层: 256 -> 128
            nn.Linear(256, 128),
            nn.BatchNorm1d(128),
            nn.ReLU(True),
            nn.Dropout(0.3),

            # 输出层: 128 -> num_classes
            nn.Linear(128, num_classes)
        )

        # 初始化权重
        self._initialize_weights()

    def _initialize_weights(self):
        """初始化网络权重"""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.BatchNorm1d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)

    def forward(self, x):
        """前向传播"""
        return self.main(x)


class ResNet18Generator(nn.Module):
    """
    基于ResNet-18的生成器架构
    用于WGAN-GP生成器，修复维度匹配问题
    """

    def __init__(self, latent_dim: int, output_dim: int):
        """
        初始化ResNet-18生成器

        参数:
            latent_dim: 潜在空间维度
            output_dim: 输出特征维度
        """
        super(ResNet18Generator, self).__init__()

        self.latent_dim = latent_dim
        self.output_dim = output_dim

        # 简化的生成器架构，避免维度不匹配
        self.main = nn.Sequential(
            # 第一层: latent_dim -> 256
            nn.Linear(latent_dim, 256),
            nn.BatchNorm1d(256),
            nn.ReLU(True),

            # 第二层: 256 -> 512
            nn.Linear(256, 512),
            nn.BatchNorm1d(512),
            nn.ReLU(True),

            # 第三层: 512 -> 256
            nn.Linear(512, 256),
            nn.BatchNorm1d(256),
            nn.ReLU(True),

            # 第四层: 256 -> 128
            nn.Linear(256, 128),
            nn.BatchNorm1d(128),
            nn.ReLU(True),

            # 输出层: 128 -> output_dim
            nn.Linear(128, output_dim),
            nn.Tanh()
        )

        self._initialize_weights()

    def _initialize_weights(self):
        """初始化权重"""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.normal_(m.weight, 0, 0.02)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.BatchNorm1d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)

    def forward(self, z):
        """前向传播"""
        return self.main(z)


class ResNet18Discriminator(nn.Module):
    """
    基于ResNet-18的判别器架构
    用于WGAN-GP判别器，修复维度匹配问题
    """

    def __init__(self, input_dim: int):
        """
        初始化ResNet-18判别器

        参数:
            input_dim: 输入特征维度
        """
        super(ResNet18Discriminator, self).__init__()

        self.input_dim = input_dim

        # 简化的判别器架构，避免维度不匹配
        self.main = nn.Sequential(
            # 第一层: input_dim -> 128
            nn.Linear(input_dim, 128),
            nn.LeakyReLU(0.2, inplace=True),
            nn.Dropout(0.3),

            # 第二层: 128 -> 256
            nn.Linear(128, 256),
            nn.LeakyReLU(0.2, inplace=True),
            nn.Dropout(0.3),

            # 第三层: 256 -> 512
            nn.Linear(256, 512),
            nn.LeakyReLU(0.2, inplace=True),
            nn.Dropout(0.3),

            # 第四层: 512 -> 256
            nn.Linear(512, 256),
            nn.LeakyReLU(0.2, inplace=True),
            nn.Dropout(0.3),

            # 输出层: 256 -> 1
            nn.Linear(256, 1)
        )

        self._initialize_weights()

    def _initialize_weights(self):
        """初始化权重"""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.normal_(m.weight, 0, 0.02)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)

    def forward(self, x):
        """前向传播"""
        return self.main(x)


def create_unified_optimizer(model_parameters, lr: float = 0.001, 
                           beta1: float = 0.9, beta2: float = 0.999):
    """
    创建统一的Adam优化器
    
    参数:
        model_parameters: 模型参数
        lr: 学习率 (默认0.001)
        beta1: Adam beta1参数 (默认0.9)
        beta2: Adam beta2参数 (默认0.999)
        
    返回:
        配置好的Adam优化器
    """
    return optim.Adam(model_parameters, lr=lr, betas=(beta1, beta2))


class AdaptiveBandwidthKDE:
    """
    带有高斯核的自适应带宽核密度估计(KDE)
    使用k-d树加速和大津法动态阈值处理
    """
    
    def __init__(self, bandwidth: str = 'scott', kernel: str = 'gaussian'):
        """
        初始化自适应带宽KDE
        
        参数:
            bandwidth: 带宽选择方法
            kernel: 核函数类型
        """
        self.bandwidth = bandwidth
        self.kernel = kernel
        self.kdtree = None
        self.data = None
        
    def fit(self, X: np.ndarray):
        """
        拟合KDE模型
        
        参数:
            X: 训练数据
        """
        self.data = X
        # 使用k-d树加速
        self.kdtree = KDTree(X)
        
        # 计算自适应带宽
        self.bandwidth_value = self._compute_adaptive_bandwidth(X)
        
    def _compute_adaptive_bandwidth(self, X: np.ndarray) -> float:
        """计算自适应带宽"""
        n, d = X.shape
        
        if self.bandwidth == 'scott':
            # Scott规则
            return n ** (-1.0 / (d + 4))
        elif self.bandwidth == 'silverman':
            # Silverman规则
            return (n * (d + 2) / 4.0) ** (-1.0 / (d + 4))
        else:
            return 1.0
    
    def score_samples(self, X: np.ndarray) -> np.ndarray:
        """
        计算样本的对数密度
        
        参数:
            X: 测试样本
            
        返回:
            对数密度值
        """
        if self.kdtree is None:
            raise ValueError("模型未拟合，请先调用fit方法")
        
        # 使用k-d树查询邻近点
        distances, indices = self.kdtree.query(X, k=min(10, len(self.data)))
        
        # 计算高斯核密度
        densities = []
        for i, (dists, idxs) in enumerate(zip(distances, indices)):
            # 高斯核
            kernel_values = np.exp(-0.5 * (dists / self.bandwidth_value) ** 2)
            density = np.mean(kernel_values)
            densities.append(np.log(density + 1e-10))  # 避免log(0)
        
        return np.array(densities)
    
    def apply_otsu_threshold(self, densities: np.ndarray) -> float:
        """
        使用大津法确定动态阈值
        
        参数:
            densities: 密度值数组
            
        返回:
            最优阈值
        """
        # 将密度值转换为适合大津法的格式
        normalized_densities = ((densities - densities.min()) / 
                               (densities.max() - densities.min()) * 255).astype(np.uint8)
        
        # 应用大津法
        threshold = threshold_otsu_simple(normalized_densities)
        
        # 转换回原始尺度
        original_threshold = (threshold / 255.0) * (densities.max() - densities.min()) + densities.min()
        
        return original_threshold
