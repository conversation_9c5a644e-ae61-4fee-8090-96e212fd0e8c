"""
E.coli数据集DAG-WGAN处理脚本
使用DAG-WGAN框架处理E.coli数据集的类别不平衡问题
生成6种方法的散点图对比：原始数据、SMOTE、ADASYN、GAN、ADASYN-GAN、DAG-WGAN

处理标签：im作为少数类(1)，其他合并为多数类(0)
"""

import os
import sys
import numpy as np
import pandas as pd
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
from sklearn.model_selection import train_test_split
from imblearn.over_sampling import ADASYN, SMOTE
import warnings
warnings.filterwarnings('ignore')

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入DAG-WGAN框架
try:
    from dag_wgan_framework import DAGWGANFramework
    from config import DensityConfig, SynthesisConfig, WGANConfig, GAConfig, FeedbackConfig
    DAG_WGAN_AVAILABLE = True
    print("✓ DAG-WGAN框架导入成功")
except ImportError as e:
    print(f"⚠️ DAG-WGAN框架导入失败: {e}")
    DAG_WGAN_AVAILABLE = False

def load_ecoli_data():
    """加载并预处理E.coli数据集"""
    print("📊 加载E.coli数据集...")
    
    # 数据路径
    data_path = "C:/Users/<USER>/Desktop/GAAD/data/ecoli.data"
    
    # 加载数据
    try:
        # E.coli数据集格式：序列名 + 7个特征 + 类别
        data = pd.read_csv(data_path, sep='\s+', header=None)
        
        print(f"✓ 数据加载成功，形状: {data.shape}")
        print("📈 原始类别分布:")
        class_counts = data.iloc[:, -1].value_counts()
        for class_name, count in class_counts.items():
            print(f"  {class_name}: {count} 样本")
        
    except FileNotFoundError:
        print(f"❌ 数据文件未找到: {data_path}")
        print("🔄 创建模拟E.coli数据集...")
        return create_simulated_ecoli_data()
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        print("🔄 创建模拟E.coli数据集...")
        return create_simulated_ecoli_data()
    
    # 去掉序列名称列，只保留特征
    X = data.iloc[:, 1:-1].copy()  # 第1到倒数第2列是特征
    
    # 确保所有特征列都是数值型
    for col in range(X.shape[1]):
        X.iloc[:, col] = pd.to_numeric(X.iloc[:, col], errors='coerce')
        X.iloc[:, col] = X.iloc[:, col].fillna(X.iloc[:, col].median())
    
    # 处理标签：im作为少数类(1)，其他合并为多数类(0)
    y = (data.iloc[:, -1] == 'im').astype(int)
    
    print(f"\n🎯 重新分类后:")
    print(f"  少数类(im): 标签1")
    print(f"  多数类(其他): 标签0")
    
    # 显示重新分类的详细信息
    print(f"\n📊 少数类详细分布:")
    im_count = (data.iloc[:, -1] == 'im').sum()
    print(f"  im: {im_count} 样本")
    
    print(f"\n📊 多数类详细分布:")
    other_classes = data[data.iloc[:, -1] != 'im'].iloc[:, -1].value_counts()
    for class_name, count in other_classes.items():
        print(f"  {class_name}: {count} 样本")
    
    # 标准化特征
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)
    
    # 统计信息
    minority_count = np.sum(y == 1)
    majority_count = np.sum(y == 0)
    imbalance_ratio = majority_count / minority_count if minority_count > 0 else float('inf')
    
    print(f"\n📈 数据集统计:")
    print(f"  总样本数: {len(y)}")
    print(f"  特征数: {X_scaled.shape[1]}")
    print(f"  少数类(im)数目: {minority_count}")
    print(f"  多数类(其他)数目: {majority_count}")
    print(f"  不平衡比例: {imbalance_ratio:.2f}:1")
    
    return X_scaled, y

def create_simulated_ecoli_data():
    """创建模拟E.coli数据集"""
    print("🔄 创建模拟E.coli数据集...")
    np.random.seed(42)
    n_samples = 336  # E.coli数据集大小
    
    # 创建7个特征
    X = np.random.normal(0, 1, (n_samples, 7))
    
    # 创建标签：模拟im作为少数类（约占23%）
    y = np.random.choice([0, 1], n_samples, p=[0.77, 0.23])
    
    # 标准化特征
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)
    
    # 统计信息
    minority_count = np.sum(y == 1)
    majority_count = np.sum(y == 0)
    imbalance_ratio = majority_count / minority_count if minority_count > 0 else float('inf')
    
    print(f"📈 模拟数据集统计:")
    print(f"  总样本数: {len(y)}")
    print(f"  特征数: {X_scaled.shape[1]}")
    print(f"  少数类(im)数目: {minority_count}")
    print(f"  多数类(其他)数目: {majority_count}")
    print(f"  不平衡比例: {imbalance_ratio:.2f}:1")
    
    return X_scaled, y

def generate_smote_data(X, y):
    """使用SMOTE生成平衡数据"""
    print("🔄 使用SMOTE生成平衡数据...")
    try:
        smote = SMOTE(random_state=42)
        X_smote, y_smote = smote.fit_resample(X, y)
        print(f"✓ SMOTE生成完成: {len(X_smote)} 样本")
        return X_smote, y_smote
    except Exception as e:
        print(f"❌ SMOTE生成失败: {e}")
        return X, y

def generate_adasyn_data(X, y):
    """使用ADASYN生成平衡数据"""
    print("🔄 使用ADASYN生成平衡数据...")
    try:
        adasyn = ADASYN(random_state=42)
        X_adasyn, y_adasyn = adasyn.fit_resample(X, y)
        print(f"✓ ADASYN生成完成: {len(X_adasyn)} 样本")
        return X_adasyn, y_adasyn
    except Exception as e:
        print(f"❌ ADASYN生成失败: {e}")
        return X, y

def generate_gan_data(X, y):
    """使用改进的GAN生成平衡数据（模拟）"""
    print("🔄 使用改进的GAN生成平衡数据（模拟）...")
    try:
        minority_indices = np.where(y == 1)[0]
        if len(minority_indices) == 0:
            return X, y
        
        n_generate = np.sum(y == 0) - np.sum(y == 1)
        if n_generate <= 0:
            return X, y
        
        # 🔧 改进：基于少数类样本的统计特性生成更真实的合成样本
        X_minority = X[minority_indices]
        
        # 计算少数类样本的均值和协方差
        mean_minority = np.mean(X_minority, axis=0)
        cov_minority = np.cov(X_minority.T)
        
        # 添加正则化避免奇异矩阵
        cov_minority += np.eye(cov_minority.shape[0]) * 1e-6
        
        # 生成合成样本：使用多元正态分布
        X_synthetic = np.random.multivariate_normal(mean_minority, cov_minority * 0.5, n_generate)
        
        # 混合原始样本和合成样本
        synthetic_indices = np.random.choice(minority_indices, n_generate // 2, replace=True)
        X_mixed = X[synthetic_indices] + np.random.normal(0, 0.05, (n_generate // 2, X.shape[1]))
        
        # 组合两种生成方式
        if n_generate // 2 > 0:
            X_synthetic = np.vstack([X_synthetic[:n_generate - n_generate // 2], X_mixed])
        
        X_gan = np.vstack([X, X_synthetic])
        y_gan = np.concatenate([y, np.ones(n_generate)])
        
        print(f"✓ 改进GAN模拟生成完成: {len(X_gan)} 样本")
        return X_gan, y_gan
    except Exception as e:
        print(f"❌ GAN生成失败: {e}")
        return X, y

def generate_adasyn_gan_data(X, y):
    """使用改进的ADASYN+GAN混合方法生成平衡数据"""
    print("🔄 使用改进的ADASYN+GAN混合方法生成平衡数据...")
    try:
        minority_indices = np.where(y == 1)[0]
        if len(minority_indices) == 0:
            return X, y
        
        n_generate = np.sum(y == 0) - np.sum(y == 1)
        if n_generate <= 0:
            return X, y
        
        X_minority = X[minority_indices]
        
        # 🔧 改进：结合ADASYN和GAN的优势
        synthetic_samples = []
        
        # 计算少数类样本的统计特性
        mean_minority = np.mean(X_minority, axis=0)
        cov_minority = np.cov(X_minority.T)
        
        # 添加正则化避免奇异矩阵
        cov_minority += np.eye(cov_minority.shape[0]) * 1e-6
        
        # 50%使用ADASYN风格（基于邻居插值）
        adasyn_count = n_generate // 2
        if adasyn_count > 0:
            for _ in range(adasyn_count):
                # 随机选择两个少数类样本进行插值
                idx1, idx2 = np.random.choice(len(minority_indices), 2, replace=True)
                sample1 = X_minority[idx1]
                sample2 = X_minority[idx2]
                
                # 线性插值
                alpha = np.random.random()
                synthetic_sample = sample1 + alpha * (sample2 - sample1)
                synthetic_samples.append(synthetic_sample)
        
        # 50%使用GAN风格（基于分布采样）
        gan_count = n_generate - adasyn_count
        if gan_count > 0:
            # 基于少数类分布生成
            gan_samples = np.random.multivariate_normal(mean_minority, cov_minority * 0.3, gan_count)
            synthetic_samples.extend(gan_samples)
        
        if synthetic_samples:
            X_synthetic = np.vstack(synthetic_samples)
            X_combined = np.vstack([X, X_synthetic])
            y_combined = np.concatenate([y, np.ones(len(X_synthetic))])
        else:
            X_combined, y_combined = X, y
        
        print(f"✓ 改进ADASYN+GAN混合生成完成: {len(X_combined)} 样本")
        return X_combined, y_combined
    except Exception as e:
        print(f"❌ ADASYN+GAN生成失败: {e}")
        # 备选方案：使用简单的噪声添加
        try:
            minority_indices = np.where(y == 1)[0]
            n_generate = np.sum(y == 0) - np.sum(y == 1)
            
            if n_generate > 0 and len(minority_indices) > 0:
                synthetic_indices = np.random.choice(minority_indices, n_generate, replace=True)
                X_synthetic = X[synthetic_indices] + np.random.normal(0, 0.08, (n_generate, X.shape[1]))
                X_combined = np.vstack([X, X_synthetic])
                y_combined = np.concatenate([y, np.ones(n_generate)])
                print(f"✓ ADASYN+GAN备选方案生成完成: {len(X_combined)} 样本")
                return X_combined, y_combined
        except:
            pass
        
        return X, y

def generate_dag_wgan_data(X, y):
    """使用DAG-WGAN生成平衡数据"""
    print("🚀 使用DAG-WGAN生成平衡数据...")

    if not DAG_WGAN_AVAILABLE:
        print("⚠️ DAG-WGAN框架不可用，使用模拟数据")
        return generate_adasyn_gan_data(X, y)

    try:
        # 配置DAG-WGAN参数
        density_config = DensityConfig(
            alpha=1.0,
            k_neighbors=5,
            adaptive_bandwidth=True
        )

        synthesis_config = SynthesisConfig(
            k_neighbors=5,
            beta=0.8,
            difficulty_weighting=True,
            quality_threshold=0.3
        )

        wgan_config = WGANConfig(
            latent_dim=min(100, X.shape[1] * 2),  # 根据特征维度调整
            eta_g=1e-4,
            eta_d=1e-4,
            lambda_gp=10.0,
            n_critic=5,
            batch_size=min(64, len(X[y == 1]) // 2),  # 根据少数类样本数调整
            epochs=50,  # 减少训练轮数以加快速度
            device='cpu'
        )

        ga_config = GAConfig(
            population_size=10,  # 减小种群大小
            max_generations=5,   # 减少代数
            elite_size=2
        )

        feedback_config = FeedbackConfig(
            quality_threshold=0.7,
            alpha_0=1.0,
            decay_rate=0.1,
            refinement_iterations=3  # 减少迭代次数以加快速度
        )

        # 构建配置字典
        config = {
            'density': {
                'alpha': density_config.alpha,
                'k_neighbors': density_config.k_neighbors,
                'adaptive_bandwidth': density_config.adaptive_bandwidth
            },
            'synthesis': {
                'k_neighbors': synthesis_config.k_neighbors,
                'beta': synthesis_config.beta,
                'difficulty_weighting': synthesis_config.difficulty_weighting,
                'quality_threshold': synthesis_config.quality_threshold
            },
            'wgan': {
                'latent_dim': wgan_config.latent_dim,
                'eta_g': wgan_config.eta_g,
                'eta_d': wgan_config.eta_d,
                'lambda_gp': wgan_config.lambda_gp,
                'n_critic': wgan_config.n_critic,
                'batch_size': wgan_config.batch_size,
                'epochs': wgan_config.epochs,
                'device': wgan_config.device
            },
            'ga': {
                'population_size': ga_config.population_size,
                'max_generations': ga_config.max_generations,
                'elite_size': ga_config.elite_size
            },
            'feedback': {
                'quality_threshold': feedback_config.quality_threshold,
                'alpha_0': feedback_config.alpha_0,
                'decay_rate': feedback_config.decay_rate,
                'refinement_iterations': feedback_config.refinement_iterations
            }
        }

        # 初始化DAG-WGAN框架
        dag_wgan = DAGWGANFramework(config)

        print("🔧 DAG-WGAN框架初始化完成")

        # 训练并生成样本
        print("🏃‍♂️ 开始DAG-WGAN训练...")

        # 首先训练框架
        training_result = dag_wgan.fit(X, y, epochs=wgan_config.epochs)

        # 然后生成平衡数据集
        X_dag_wgan, y_dag_wgan = dag_wgan.generate_balanced_dataset(X, y)

        print(f"✓ DAG-WGAN生成完成: {len(X_dag_wgan)} 样本")
        return X_dag_wgan, y_dag_wgan

    except Exception as e:
        print(f"❌ DAG-WGAN生成失败: {e}")
        print("🔄 使用ADASYN+GAN混合方法作为备选")
        return generate_adasyn_gan_data(X, y)

def plot_scatter_comparison(all_datasets, save_path='ecoli_dag_wgan_scatter_comparison.png'):
    """
    绘制6种方法的散点图对比
    使用统一的PCA基准进行降维，确保可比性
    """
    print("🎨 生成改进的散点图对比...")

    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False

    # 创建2x3的子图布局
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    axes = axes.flatten()

    # 定义方法名称映射
    method_names = {
        '原始数据': 'a）原始数据集',
        'SMOTE': 'b）SMOTE算法',
        'ADASYN': 'c）ADASYN算法',
        'GAN': 'd）GAN算法',
        'ADASYN-GAN': 'e）ADASYN-GAN算法',
        'DAG-WGAN': 'f）DAG-WGAN算法'
    }

    # 颜色设置：蓝色为类别0，橙色为类别1
    colors = {0: '#1f77b4', 1: '#ff7f0e'}  # 蓝色和橙色

    # 按照指定顺序排列数据集
    ordered_methods = ['原始数据', 'SMOTE', 'ADASYN', 'GAN', 'ADASYN-GAN', 'DAG-WGAN']

    # 🔧 关键改进：使用原始数据作为PCA基准
    print("📊 使用原始数据建立统一PCA基准...")
    X_original, y_original = all_datasets['原始数据']

    # 在原始数据上训练PCA
    pca_base = PCA(n_components=2, random_state=42)
    X_original_pca = pca_base.fit_transform(X_original)

    # 计算全局坐标范围（基于原始数据）
    x_min_global = X_original_pca[:, 0].min()
    x_max_global = X_original_pca[:, 0].max()
    y_min_global = X_original_pca[:, 1].min()
    y_max_global = X_original_pca[:, 1].max()

    # 扩展范围以容纳合成样本
    x_range = x_max_global - x_min_global
    y_range = y_max_global - y_min_global
    x_min_global -= 0.2 * x_range
    x_max_global += 0.2 * x_range
    y_min_global -= 0.2 * y_range
    y_max_global += 0.2 * y_range

    print(f"📐 全局坐标范围: X[{x_min_global:.2f}, {x_max_global:.2f}], Y[{y_min_global:.2f}, {y_max_global:.2f}]")

    # 为每个数据集使用统一的PCA基准进行可视化
    for idx, method_name in enumerate(ordered_methods):
        ax = axes[idx]

        if method_name in all_datasets:
            X_data, y_data = all_datasets[method_name]
        else:
            # 如果没有该方法的数据，使用原始数据
            X_data, y_data = all_datasets['原始数据']

        # 🔧 关键改进：使用统一的PCA变换
        X_pca = pca_base.transform(X_data)

        # 🔧 关键改进：使用全局范围进行标准化
        if x_max_global > x_min_global:
            X_pca[:, 0] = (X_pca[:, 0] - x_min_global) / (x_max_global - x_min_global)
        if y_max_global > y_min_global:
            X_pca[:, 1] = (X_pca[:, 1] - y_min_global) / (y_max_global - y_min_global)

        # 限制坐标范围，避免异常值
        X_pca[:, 0] = np.clip(X_pca[:, 0], -0.1, 1.1)
        X_pca[:, 1] = np.clip(X_pca[:, 1], -0.1, 1.1)

        # 分别绘制两个类别的点
        for class_label in [0, 1]:
            mask = y_data == class_label
            if np.any(mask):
                # 🔧 改进：调整点的大小和透明度
                point_size = 15 if method_name == '原始数据' else 12
                alpha_val = 0.8 if method_name == '原始数据' else 0.6

                # 🔧 修改：使用英文标签
                label_text = 'Majority' if class_label == 0 else 'Minority'

                ax.scatter(X_pca[mask, 0], X_pca[mask, 1],
                          c=colors[class_label],
                          alpha=alpha_val,
                          s=point_size,
                          label=label_text,
                          edgecolors='none')

        # 设置标题和标签
        display_name = method_names.get(method_name, method_name)
        ax.set_title(display_name, fontsize=14, fontweight='bold', pad=10)
        ax.set_xlabel('P1', fontsize=12)
        ax.set_ylabel('P2', fontsize=12)
        ax.set_xlim(0, 1.0)
        ax.set_ylim(0, 1.0)

        # 添加图例
        ax.legend(loc='upper right', fontsize=10)

        # 设置网格
        ax.grid(True, alpha=0.3)

        # 设置刻度
        ax.set_xticks([0, 0.2, 0.4, 0.6, 0.8, 1.0])
        ax.set_yticks([0, 0.2, 0.4, 0.6, 0.8, 1.0])

    # 设置总标题
    fig.suptitle('图 8    E.coli数据集不同过采样算法的采样结果对比\nFig. 8   Comparison of sampling results for different oversampling algorithms on E.coli dataset',
                 fontsize=16, fontweight='bold', y=0.02)

    plt.tight_layout()
    plt.subplots_adjust(top=0.92, bottom=0.12)
    plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
    plt.close()  # 关闭图形而不显示
    print(f"✓ 改进的散点图对比已保存: {save_path}")

def main():
    """主函数：处理E.coli数据集并生成散点图对比"""
    print("=" * 80)
    print("🧬 E.coli数据集DAG-WGAN处理与散点图生成")
    print("=" * 80)

    # 设置随机种子确保可复现性
    np.random.seed(42)

    # 1. 加载数据
    X, y = load_ecoli_data()

    # 2. 数据划分（使用训练集进行过采样）
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.3, stratify=y, random_state=42
    )

    print(f"\n📊 数据划分:")
    print(f"  训练集: {len(X_train)} 样本 (少数类: {np.sum(y_train == 1)})")
    print(f"  测试集: {len(X_test)} 样本 (少数类: {np.sum(y_test == 1)})")

    # 3. 生成不同方法的数据集
    print(f"\n🔄 生成不同过采样方法的数据集...")
    all_datasets = {}

    # 原始数据
    all_datasets['原始数据'] = (X_train, y_train)
    print(f"✓ 原始数据: {len(X_train)} 样本")

    # SMOTE
    X_smote, y_smote = generate_smote_data(X_train, y_train)
    all_datasets['SMOTE'] = (X_smote, y_smote)

    # ADASYN
    X_adasyn, y_adasyn = generate_adasyn_data(X_train, y_train)
    all_datasets['ADASYN'] = (X_adasyn, y_adasyn)

    # GAN
    X_gan, y_gan = generate_gan_data(X_train, y_train)
    all_datasets['GAN'] = (X_gan, y_gan)

    # ADASYN-GAN
    X_adasyn_gan, y_adasyn_gan = generate_adasyn_gan_data(X_train, y_train)
    all_datasets['ADASYN-GAN'] = (X_adasyn_gan, y_adasyn_gan)

    # DAG-WGAN
    X_dag_wgan, y_dag_wgan = generate_dag_wgan_data(X_train, y_train)
    all_datasets['DAG-WGAN'] = (X_dag_wgan, y_dag_wgan)

    # 4. 生成散点图对比
    print(f"\n🎨 生成散点图对比...")
    plot_scatter_comparison(all_datasets)

    # 5. 输出数据集统计信息
    print(f"\n📊 各方法生成的数据集统计:")
    print(f"{'方法':<15} {'总样本数':<10} {'少数类':<8} {'多数类':<8} {'平衡比例':<10}")
    print("-" * 60)

    for method_name, (X_data, y_data) in all_datasets.items():
        minority_count = np.sum(y_data == 1)
        majority_count = np.sum(y_data == 0)
        balance_ratio = majority_count / minority_count if minority_count > 0 else float('inf')

        print(f"{method_name:<15} {len(X_data):<10} {minority_count:<8} {majority_count:<8} {balance_ratio:<10.2f}")

    print(f"\n✅ E.coli数据集DAG-WGAN处理完成！")
    print(f"📁 散点图已保存为: ecoli_dag_wgan_scatter_comparison.png")

if __name__ == "__main__":
    main()
