"""
测试Yeast数据集GA优化ADASYN-WGAN训练损失函数绘制功能
专门针对GA优化ADASYN-WGAN方法的损失函数可视化
"""

import os
import sys
import numpy as np
import pandas as pd
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from imblearn.over_sampling import ADASYN
import warnings
warnings.filterwarnings('ignore')

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'laborate0716'))

try:
    from wgan_gp import WGAN_GP
    from config import wgan_config
    WGAN_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ WGAN-GP模块导入失败: {e}")
    WGAN_AVAILABLE = False

def load_yeast_data():
    """加载并预处理yeast数据集"""
    print("📊 加载Yeast数据集...")
    
    # 数据路径
    data_path = "C:/Users/<USER>/Desktop/GAAD/data/yeast.data"
    
    # 列名定义（根据UCI数据集描述）
    columns = [
        'sequence_name', 'mcg', 'gvh', 'alm', 'mit', 'erl', 'pox', 'vac', 'nuc', 'class'
    ]
    
    try:
        # 加载数据
        data = pd.read_csv(data_path, sep=r'\s+', header=None, names=columns)
        print(f"数据加载成功，形状: {data.shape}")
        
        # 移除序列名称列
        data = data.drop('sequence_name', axis=1)
        
        # 处理目标变量：将GOL、POX、VAC作为少数类(1)，其他作为多数类(0)
        minority_classes = ['GOL', 'POX', 'VAC']
        y = data['class'].apply(lambda x: 1 if x in minority_classes else 0).values
        X = data.drop('class', axis=1).values
        
        # 标准化特征
        scaler = StandardScaler()
        X = scaler.fit_transform(X)
        
        # 统计信息
        minority_count = np.sum(y == 1)
        majority_count = np.sum(y == 0)
        imbalance_ratio = majority_count / minority_count if minority_count > 0 else float('inf')
        
        print(f"数据集统计:")
        print(f"总样本数: {len(y)}")
        print(f"少数类(GOL+POX+VAC)数目: {minority_count}")
        print(f"多数类数目: {majority_count}")
        print(f"不平衡比例: {imbalance_ratio:.2f}:1")
        
        return X, y
        
    except Exception as e:
        print(f"数据加载失败: {e}")
        # 创建模拟数据
        print("使用模拟数据...")
        return create_simulated_yeast_data()

def create_simulated_yeast_data():
    """创建模拟的yeast数据集"""
    print("创建模拟yeast数据集...")
    
    np.random.seed(42)
    n_samples = 1484  # 接近真实yeast数据集大小
    n_features = 8    # 8个特征
    
    # 生成多数类数据 (约90%)
    n_majority = int(n_samples * 0.9)
    X_majority = np.random.randn(n_majority, n_features)
    y_majority = np.zeros(n_majority)
    
    # 生成少数类数据 (约10%)
    n_minority = n_samples - n_majority
    X_minority = np.random.randn(n_minority, n_features) + 1.5  # 稍微偏移
    y_minority = np.ones(n_minority)
    
    # 合并数据
    X = np.vstack([X_majority, X_minority])
    y = np.concatenate([y_majority, y_minority])
    
    # 标准化
    scaler = StandardScaler()
    X = scaler.fit_transform(X)
    
    print(f"模拟数据集创建完成:")
    print(f"  总样本数: {len(y)}")
    print(f"  少数类: {np.sum(y == 1)} 样本")
    print(f"  多数类: {np.sum(y == 0)} 样本")
    print(f"  不平衡比例: {np.sum(y == 0) / np.sum(y == 1):.2f}:1")
    
    return X, y

def plot_wgan_training_losses(losses, save_path='yeast_ga_adasyn_wgan_losses.png'):
    """
    绘制GA优化ADASYN-WGAN训练损失函数图
    参照用户提供的图片样式：蓝色D损失值，橙色G损失值
    """
    print(f"\n📊 绘制GA优化ADASYN-WGAN训练损失函数图...")
    
    if not losses or 'd_losses' not in losses or 'g_losses' not in losses:
        print("❌ 没有找到损失函数数据")
        return
    
    d_losses = losses['d_losses']
    g_losses = losses['g_losses']
    
    if len(d_losses) == 0 or len(g_losses) == 0:
        print("❌ 损失函数数据为空")
        return
    
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 创建图形
    fig, ax = plt.subplots(1, 1, figsize=(12, 8))
    
    # 训练次数
    epochs = range(1, len(d_losses) + 1)
    
    # 绘制损失函数曲线，参照用户图片的颜色
    ax.plot(epochs, d_losses, color='#1f77b4', linewidth=1.5, label='D损失值', alpha=0.8)
    ax.plot(epochs, g_losses, color='#ff7f0e', linewidth=1.5, label='G损失值', alpha=0.8)
    
    # 设置坐标轴
    ax.set_xlabel('训练次数', fontsize=14, fontweight='bold')
    ax.set_ylabel('损失函数值', fontsize=14, fontweight='bold')
    ax.set_title('Yeast数据集上GA优化ADASYN-WGAN生成器和判别器训练损失函数', 
                fontsize=16, fontweight='bold', pad=20)
    
    # 设置网格
    ax.grid(True, alpha=0.3, linestyle='-', linewidth=0.5)
    
    # 设置图例
    ax.legend(fontsize=12, loc='upper right', frameon=True, fancybox=True, shadow=True)
    
    # 设置坐标轴范围
    ax.set_xlim(0, len(d_losses))
    
    # 动态设置y轴范围
    all_losses = d_losses + g_losses
    y_min = min(all_losses)
    y_max = max(all_losses)
    y_range = y_max - y_min
    ax.set_ylim(y_min - y_range * 0.1, y_max + y_range * 0.1)
    
    # 设置坐标轴刻度
    ax.tick_params(axis='both', which='major', labelsize=12)
    
    # 添加统计信息文本框
    stats_text = f'训练轮数: {len(d_losses)}\n'
    stats_text += f'D损失值范围: [{min(d_losses):.4f}, {max(d_losses):.4f}]\n'
    stats_text += f'G损失值范围: [{min(g_losses):.4f}, {max(g_losses):.4f}]\n'
    stats_text += f'最终D损失值: {d_losses[-1]:.4f}\n'
    stats_text += f'最终G损失值: {g_losses[-1]:.4f}'
    
    ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, fontsize=10,
            verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    # 保存图片
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
    plt.close()
    
    print(f"✅ GA优化ADASYN-WGAN训练损失函数图已保存: {save_path}")
    
    # 打印损失函数统计信息
    print(f"\n📈 训练损失函数统计:")
    print(f"  训练轮数: {len(d_losses)}")
    print(f"  判别器损失值:")
    print(f"    最小值: {min(d_losses):.6f}")
    print(f"    最大值: {max(d_losses):.6f}")
    print(f"    最终值: {d_losses[-1]:.6f}")
    print(f"    平均值: {np.mean(d_losses):.6f}")
    print(f"  生成器损失值:")
    print(f"    最小值: {min(g_losses):.6f}")
    print(f"    最大值: {max(g_losses):.6f}")
    print(f"    最终值: {g_losses[-1]:.6f}")
    print(f"    平均值: {np.mean(g_losses):.6f}")

def simulate_wgan_losses():
    """模拟WGAN-GP训练损失函数数据"""
    print("🎭 生成模拟的WGAN-GP训练损失函数数据...")

    np.random.seed(42)
    epochs = 2000

    # 模拟判别器损失（D损失）
    d_losses = []
    d_loss = 0.5  # 初始值

    for i in range(epochs):
        # 模拟训练过程中的损失变化
        if i < 100:
            # 初期快速下降
            d_loss += np.random.normal(-0.01, 0.05)
        elif i < 500:
            # 中期稳定下降
            d_loss += np.random.normal(-0.002, 0.02)
        else:
            # 后期稳定
            d_loss += np.random.normal(0, 0.01)

        # 限制范围
        d_loss = np.clip(d_loss, -2.0, 1.0)
        d_losses.append(d_loss)

    # 模拟生成器损失（G损失）
    g_losses = []
    g_loss = 0.1  # 初始值

    for i in range(epochs):
        # 模拟训练过程中的损失变化
        if i < 100:
            # 初期波动较大
            g_loss += np.random.normal(0.005, 0.03)
        elif i < 500:
            # 中期逐渐改善
            g_loss += np.random.normal(0.002, 0.015)
        else:
            # 后期相对稳定
            g_loss += np.random.normal(0.001, 0.008)

        # 限制范围
        g_loss = np.clip(g_loss, 0.005, 0.05)
        g_losses.append(g_loss)

    # 创建损失字典
    losses = {
        'd_losses': d_losses,
        'g_losses': g_losses
    }

    # 绘制损失函数图
    plot_wgan_training_losses(losses, 'yeast_ga_adasyn_wgan_losses_simulated.png')

def test_yeast_wgan_training():
    """测试Yeast数据集GA优化ADASYN-WGAN训练并绘制损失函数"""
    print("=" * 80)
    print("Yeast数据集GA优化ADASYN-WGAN训练损失函数测试")
    print("=" * 80)
    
    # 设置随机种子
    np.random.seed(42)
    
    # 1. 加载数据
    X, y = load_yeast_data()
    
    # 2. 数据划分
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.3, stratify=y, random_state=42
    )
    
    print(f"\n训练集: {len(X_train)} 样本")
    print(f"训练集类别分布: {np.bincount(y_train.astype(int))}")
    
    # 3. 使用GA优化的最优参数（模拟）
    print(f"\n使用GA优化的最优参数:")
    k = 5
    alpha = 0.80
    lambda_gp = 10.0
    n_critic = 5
    lr = 1e-04
    batch_size = 32
    
    print(f"  ADASYN k邻居: {k}")
    print(f"  ADASYN α平衡: {alpha:.4f}")
    print(f"  WGAN λ梯度惩罚: {lambda_gp:.4f}")
    print(f"  WGAN 判别器训练次数: {n_critic}")
    print(f"  WGAN 学习率: {lr:.2e}")
    print(f"  WGAN 批量大小: {batch_size}")
    
    # 4. 生成平衡数据集并训练WGAN-GP
    print(f"\n开始训练GA优化ADASYN-WGAN...")
    
    try:
        # 提取少数类和多数类
        X_min = X_train[y_train == 1]
        X_maj = X_train[y_train == 0]
        N_min = len(X_min)
        N_maj = len(X_maj)
        G_total = N_maj - N_min
        
        print(f"原始数据: 多数类{N_maj}, 少数类{N_min}")
        
        # ADASYN生成
        G_adasyn = int(alpha * G_total)
        X_synthetic = np.zeros((0, X_train.shape[1]))
        
        if G_adasyn > 0:
            adasyn = ADASYN(
                sampling_strategy={1: N_min + G_adasyn},
                n_neighbors=min(k, N_min-1) if N_min > 1 else 1,
                random_state=42
            )
            X_adasyn_res, y_adasyn_res = adasyn.fit_resample(X_train, y_train)
            X_adasyn_only = X_adasyn_res[len(X_train):]
            X_synthetic = X_adasyn_only
            print(f"ADASYN生成: {len(X_adasyn_only)} 个样本")
        
        # WGAN-GP生成
        G_wgan = G_total - G_adasyn
        
        if G_wgan > 0 and WGAN_AVAILABLE:
            real_minority = np.vstack([X_min, X_synthetic]) if len(X_synthetic) > 0 else X_min

            if len(real_minority) < batch_size:
                batch_size = max(1, len(real_minority) // 2)

            wgan_gp = WGAN_GP(
                latent_dim=wgan_config.LATENT_DIM,
                data_dim=X_train.shape[1],
                lambda_gp=lambda_gp,
                lr=lr,
                batch_size=batch_size,
                n_critic=n_critic,
                force_cpu=True
            )
            
            print(f"训练WGAN-GP (共2000轮)...")
            print(f"  训练数据: {len(real_minority)} 个少数类样本")
            print(f"  网络配置: 潜在维度={wgan_config.LATENT_DIM}, 数据维度={X_train.shape[1]}")
            print(f"  训练参数: λ={lambda_gp:.2f}, n_critic={n_critic}, lr={lr:.2e}, batch_size={batch_size}")
            
            # 训练2000轮并记录详细的损失函数
            wgan_gp.train_with_progress(real_minority, epochs=2000)
            
            # 获取训练损失
            losses = wgan_gp.get_training_history()
            
            # 绘制损失函数图
            plot_wgan_training_losses(losses, 'yeast_ga_adasyn_wgan_losses_test.png')
            
        elif not WGAN_AVAILABLE:
            print("⚠️ WGAN-GP模块不可用，生成模拟损失函数数据")
            # 生成模拟的损失函数数据
            simulate_wgan_losses()
        else:
            print("⚠️ 不需要WGAN-GP生成，跳过训练")
            
    except Exception as e:
        print(f"❌ 训练过程出错: {e}")
        import traceback
        traceback.print_exc()
    
    print(f"\n✅ 测试完成！")

if __name__ == "__main__":
    test_yeast_wgan_training()
