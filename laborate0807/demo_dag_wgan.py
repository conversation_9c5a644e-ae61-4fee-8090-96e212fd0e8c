"""
Demo Script for DAG-WGAN Framework

This script demonstrates the basic usage of the DAG-WGAN framework
with a simple synthetic dataset.

Author: Research Team
Date: 2024-08-07
"""

import numpy as np
import matplotlib.pyplot as plt
from sklearn.datasets import make_classification
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import classification_report, confusion_matrix
import logging
import warnings
warnings.filterwarnings('ignore')

# Import DAG-WGAN components
from config import get_fast_config
from dag_wgan_framework import DAG<PERSON>GANFramework
from experimental_evaluation import MetricsCalculator

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def create_imbalanced_dataset(n_samples=1000, imbalance_ratio=0.1, random_state=42):
    """
    Create a synthetic imbalanced dataset.
    
    Args:
        n_samples: Total number of samples
        imbalance_ratio: Ratio of minority class
        random_state: Random seed
        
    Returns:
        Tuple of (X, y)
    """
    weights = [1 - imbalance_ratio, imbalance_ratio]
    
    X, y = make_classification(
        n_samples=n_samples,
        n_features=10,
        n_informative=8,
        n_redundant=2,
        n_clusters_per_class=1,
        weights=weights,
        flip_y=0.01,
        random_state=random_state
    )
    
    return X, y


def evaluate_classifier(X_train, y_train, X_test, y_test, method_name="Method"):
    """
    Train and evaluate a classifier.
    
    Args:
        X_train: Training features
        y_train: Training labels
        X_test: Test features
        y_test: Test labels
        method_name: Name of the method for reporting
        
    Returns:
        Dictionary of metrics
    """
    # Train classifier
    clf = RandomForestClassifier(n_estimators=100, random_state=42)
    clf.fit(X_train, y_train)
    
    # Make predictions
    y_pred = clf.predict(X_test)
    y_pred_proba = clf.predict_proba(X_test)[:, 1]
    
    # Calculate metrics
    metrics = MetricsCalculator.calculate_comprehensive_metrics(y_test, y_pred, y_pred_proba)
    
    # Print results
    print(f"\n{method_name} Results:")
    print(f"F1-Score: {metrics['f1_score']:.4f}")
    print(f"G-mean: {metrics['gmean']:.4f}")
    print(f"AUC-ROC: {metrics.get('auc_roc', 'N/A')}")
    print(f"Precision: {metrics['precision']:.4f}")
    print(f"Recall: {metrics['recall']:.4f}")
    print(f"Specificity: {metrics['specificity']:.4f}")
    
    return metrics


def visualize_data_distribution(X, y, title="Data Distribution"):
    """
    Visualize 2D projection of data distribution.
    
    Args:
        X: Features
        y: Labels
        title: Plot title
    """
    plt.figure(figsize=(8, 6))
    
    # Use first two features for visualization
    X_majority = X[y == 0]
    X_minority = X[y == 1]
    
    plt.scatter(X_majority[:, 0], X_majority[:, 1], 
               c='blue', alpha=0.6, label=f'Majority ({len(X_majority)})', s=20)
    plt.scatter(X_minority[:, 0], X_minority[:, 1], 
               c='red', alpha=0.8, label=f'Minority ({len(X_minority)})', s=20)
    
    plt.xlabel('Feature 1')
    plt.ylabel('Feature 2')
    plt.title(title)
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.show()


def main():
    """Main demo function."""
    print("=== DAG-WGAN Framework Demo ===\n")
    
    # 1. Create synthetic imbalanced dataset
    logger.info("Creating synthetic imbalanced dataset...")
    X, y = create_imbalanced_dataset(n_samples=2000, imbalance_ratio=0.1)
    
    print(f"Dataset created:")
    print(f"  Total samples: {len(X)}")
    print(f"  Features: {X.shape[1]}")
    print(f"  Class distribution: {np.bincount(y)}")
    print(f"  Imbalance ratio: {np.sum(y==0)/np.sum(y==1):.1f}:1")
    
    # Visualize original data
    print("\nVisualizing original data distribution...")
    visualize_data_distribution(X, y, "Original Imbalanced Dataset")
    
    # 2. Split data
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.3, random_state=42, stratify=y
    )
    
    print(f"\nData split:")
    print(f"  Training set: {len(X_train)} samples")
    print(f"  Test set: {len(X_test)} samples")
    
    # 3. Baseline evaluation (no oversampling)
    logger.info("Evaluating baseline (no oversampling)...")
    baseline_metrics = evaluate_classifier(X_train, y_train, X_test, y_test, "Baseline (No Oversampling)")
    
    # 4. DAG-WGAN evaluation
    logger.info("Initializing DAG-WGAN framework...")
    
    # Use fast configuration for demo
    config = get_fast_config()
    
    # Convert config to dictionary format expected by framework
    framework_config = {
        'bandwidth': 1.0,
        'k_neighbors': config.density.k_neighbors,
        'lambda_param': config.synthesis.lambda_param,
        'alpha': config.synthesis.alpha,
        'lr_g': config.wgan.lr_g,
        'lr_d': config.wgan.lr_d,
        'lambda_gp': config.wgan.lambda_gp,
        'n_critic': config.wgan.n_critic,
        'device': config.wgan.device,
        'latent_dim': config.wgan.latent_dim
    }
    
    # Initialize framework
    dag_wgan = DAGWGANFramework(framework_config)
    
    try:
        # Train DAG-WGAN
        logger.info("Training DAG-WGAN framework...")
        training_result = dag_wgan.fit(X_train, y_train, epochs=50)  # Reduced epochs for demo
        
        if training_result['status'] == 'success':
            print(f"\nDAG-WGAN training completed successfully!")
            print(f"  ADASYN samples generated: {training_result['history']['adasyn_samples']}")
            
            # Generate balanced dataset
            logger.info("Generating balanced dataset...")
            X_balanced, y_balanced = dag_wgan.generate_balanced_dataset(X_train, y_train)
            
            print(f"\nBalanced dataset created:")
            print(f"  Total samples: {len(X_balanced)}")
            print(f"  Class distribution: {np.bincount(y_balanced)}")
            
            # Visualize balanced data
            print("\nVisualizing balanced data distribution...")
            visualize_data_distribution(X_balanced, y_balanced, "DAG-WGAN Balanced Dataset")
            
            # Evaluate DAG-WGAN
            logger.info("Evaluating DAG-WGAN performance...")
            dag_wgan_metrics = evaluate_classifier(X_balanced, y_balanced, X_test, y_test, "DAG-WGAN")
            
            # 5. Compare results
            print("\n" + "="*50)
            print("COMPARISON SUMMARY")
            print("="*50)
            
            comparison_metrics = ['f1_score', 'gmean', 'precision', 'recall', 'specificity']
            
            print(f"{'Metric':<15} {'Baseline':<12} {'DAG-WGAN':<12} {'Improvement':<12}")
            print("-" * 55)
            
            for metric in comparison_metrics:
                baseline_val = baseline_metrics.get(metric, 0)
                dag_wgan_val = dag_wgan_metrics.get(metric, 0)
                improvement = ((dag_wgan_val - baseline_val) / baseline_val * 100) if baseline_val > 0 else 0
                
                print(f"{metric:<15} {baseline_val:<12.4f} {dag_wgan_val:<12.4f} {improvement:<12.1f}%")
            
            # AUC comparison (if available)
            if 'auc_roc' in baseline_metrics and 'auc_roc' in dag_wgan_metrics:
                baseline_auc = baseline_metrics['auc_roc']
                dag_wgan_auc = dag_wgan_metrics['auc_roc']
                auc_improvement = ((dag_wgan_auc - baseline_auc) / baseline_auc * 100) if baseline_auc > 0 else 0
                print(f"{'auc_roc':<15} {baseline_auc:<12.4f} {dag_wgan_auc:<12.4f} {auc_improvement:<12.1f}%")
            
            print("\n" + "="*50)
            
            # Key insights
            print("\nKEY INSIGHTS:")
            if dag_wgan_metrics['f1_score'] > baseline_metrics['f1_score']:
                print("✓ DAG-WGAN improved F1-score for minority class detection")
            if dag_wgan_metrics['gmean'] > baseline_metrics['gmean']:
                print("✓ DAG-WGAN achieved better balance between sensitivity and specificity")
            if dag_wgan_metrics['recall'] > baseline_metrics['recall']:
                print("✓ DAG-WGAN improved minority class recall (reduced false negatives)")
            
        else:
            print(f"DAG-WGAN training failed: {training_result.get('reason', 'Unknown error')}")
            
    except Exception as e:
        logger.error(f"Error during DAG-WGAN evaluation: {e}")
        print(f"Demo failed with error: {e}")
    
    print("\n=== Demo Completed ===")


if __name__ == '__main__':
    main()
