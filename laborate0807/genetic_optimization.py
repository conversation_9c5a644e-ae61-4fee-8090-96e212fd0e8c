"""
多维遗传算法协同优化模块

该模块实现了遗传算法，用于同时优化DAG-WGAN框架中的
七个关键参数，如公式(11-12)中所述。

优化参数:
- h: KDE密度估计的带宽
- λ: 插值强度参数
- k: ADASYN的邻居数量
- k_neigh: 密度计算的邻居数量
- lr_g: 生成器学习率
- lr_d: 判别器学习率
- α₀: 初始平衡参数

作者: 研究团队
日期: 2024-08-07
"""

import numpy as np
import random
from typing import Dict, List, Tuple, Optional, Callable, Any
from dataclasses import dataclass
from sklearn.model_selection import cross_val_score
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import f1_score, roc_auc_score, confusion_matrix
import logging
import copy
import matplotlib.pyplot as plt
from scipy.stats import entropy
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)


@dataclass
class GAParameters:
    """Configuration parameters for the genetic algorithm."""
    population_size: int = 50
    max_generations: int = 100
    elite_size: int = 5
    crossover_rate: float = 0.8
    mutation_rate: float = 0.15
    tournament_size: int = 3
    early_stop_patience: int = 20
    early_stop_threshold: float = 0.001
    random_state: int = 42


@dataclass
class Individual:
    """Represents an individual in the genetic algorithm population."""
    genes: List[float]
    fitness: float = 0.0
    age: int = 0
    
    def copy(self) -> 'Individual':
        """Create a deep copy of the individual."""
        return Individual(
            genes=self.genes.copy(),
            fitness=self.fitness,
            age=self.age
        )


class ParameterBounds:
    """Defines the bounds and types for each parameter in the chromosome."""
    
    def __init__(self):
        # Parameter bounds: [min, max, type, log_scale]
        # Updated to match section 4.2: α, k, λ_gp, η_G, η_D, β, C
        self.bounds = {
            'alpha_bandwidth': [0.5, 2.0, 'continuous', False],   # KDE bandwidth factor α
            'k_neighbors': [3, 20, 'integer', False],             # ADASYN neighborhood size k
            'lambda_gp': [1.0, 50.0, 'continuous', False],       # WGAN-GP gradient penalty λ_gp
            'eta_g': [1e-5, 1e-2, 'continuous', True],           # Generator learning rate η_G (log scale)
            'eta_d': [1e-5, 1e-2, 'continuous', True],           # Critic learning rate η_D (log scale)
            'beta_oversample': [0.3, 1.5, 'continuous', False],  # Oversampling ratio β
            'c_regularization': [0.01, 10.0, 'continuous', True] # Classifier regularization C (log scale)
        }
        
        self.param_names = list(self.bounds.keys())
        self.n_params = len(self.param_names)
    
    def decode_individual(self, individual: Individual) -> Dict[str, Any]:
        """
        Decode individual genes to actual parameter values.
        
        Args:
            individual: Individual to decode
            
        Returns:
            Dictionary of parameter names and values
        """
        params = {}
        
        for i, param_name in enumerate(self.param_names):
            gene_value = individual.genes[i]
            min_val, max_val, param_type, log_scale = self.bounds[param_name]
            
            if log_scale:
                # Convert from log scale
                log_min, log_max = np.log10(min_val), np.log10(max_val)
                log_value = log_min + gene_value * (log_max - log_min)
                value = 10 ** log_value
            else:
                # Linear scale
                value = min_val + gene_value * (max_val - min_val)
            
            if param_type == 'integer':
                value = int(round(value))
            
            # Ensure bounds
            value = max(min_val, min(max_val, value))
            params[param_name] = value
        
        return params
    
    def encode_params(self, params: Dict[str, Any]) -> List[float]:
        """
        Encode parameter values to genes (0-1 range).
        
        Args:
            params: Dictionary of parameter values
            
        Returns:
            List of gene values
        """
        genes = []
        
        for param_name in self.param_names:
            value = params[param_name]
            min_val, max_val, param_type, log_scale = self.bounds[param_name]
            
            if log_scale:
                log_min, log_max = np.log10(min_val), np.log10(max_val)
                log_value = np.log10(max(min_val, min(max_val, value)))
                gene = (log_value - log_min) / (log_max - log_min)
            else:
                gene = (value - min_val) / (max_val - min_val)
            
            genes.append(max(0.0, min(1.0, gene)))
        
        return genes


class MultiObjectiveFitnessFunction:
    """
    Multi-objective fitness function implementing Equation (9) from section 4.2.
    Evaluates F1_minority + γ·OverlapScore - η·LossVariance
    """

    def __init__(self, gamma: float = 0.3, eta: float = 0.2):
        """
        Initialize fitness function.

        Args:
            gamma: Weight for overlap score (Jensen-Shannon divergence)
            eta: Weight for loss variance penalty
        """
        self.gamma = gamma
        self.eta = eta
        self.evaluation_cache = {}
        
    def compute_jensen_shannon_divergence(self, X_gen: np.ndarray, X_real: np.ndarray) -> float:
        """
        Compute Jensen-Shannon divergence between generated and real data distributions.

        Args:
            X_gen: Generated samples
            X_real: Real samples

        Returns:
            Jensen-Shannon divergence (lower is better)
        """
        if len(X_gen) == 0 or len(X_real) == 0:
            return float('inf')

        try:
            js_divs = []

            for dim in range(X_gen.shape[1]):
                # Create probability distributions using histograms
                min_val = min(X_gen[:, dim].min(), X_real[:, dim].min())
                max_val = max(X_gen[:, dim].max(), X_real[:, dim].max())

                if max_val == min_val:
                    js_divs.append(0.0)
                    continue

                bins = np.linspace(min_val, max_val, 20)
                hist_gen, _ = np.histogram(X_gen[:, dim], bins=bins, density=True)
                hist_real, _ = np.histogram(X_real[:, dim], bins=bins, density=True)

                # Add small epsilon to avoid log(0)
                eps = 1e-8
                hist_gen = hist_gen + eps
                hist_real = hist_real + eps

                # Normalize
                hist_gen = hist_gen / hist_gen.sum()
                hist_real = hist_real / hist_real.sum()

                # Compute Jensen-Shannon divergence
                m = 0.5 * (hist_gen + hist_real)
                js_div = 0.5 * entropy(hist_gen, m) + 0.5 * entropy(hist_real, m)
                js_divs.append(js_div)

            return np.mean(js_divs)

        except Exception as e:
            logger.warning(f"Error computing Jensen-Shannon divergence: {e}")
            return float('inf')
    
    def compute_kl_divergence(self, X_gen: np.ndarray, X_real: np.ndarray) -> float:
        """
        Compute KL divergence between generated and real data distributions.
        
        Args:
            X_gen: Generated samples
            X_real: Real samples
            
        Returns:
            KL divergence
        """
        if len(X_gen) == 0 or len(X_real) == 0:
            return float('inf')
        
        try:
            kl_divs = []
            
            for dim in range(X_gen.shape[1]):
                # Create probability distributions using histograms
                min_val = min(X_gen[:, dim].min(), X_real[:, dim].min())
                max_val = max(X_gen[:, dim].max(), X_real[:, dim].max())
                
                if max_val == min_val:
                    kl_divs.append(0.0)
                    continue
                
                bins = np.linspace(min_val, max_val, 20)
                hist_gen, _ = np.histogram(X_gen[:, dim], bins=bins, density=True)
                hist_real, _ = np.histogram(X_real[:, dim], bins=bins, density=True)
                
                # Add small epsilon to avoid log(0)
                eps = 1e-8
                hist_gen = hist_gen + eps
                hist_real = hist_real + eps
                
                # Normalize
                hist_gen = hist_gen / hist_gen.sum()
                hist_real = hist_real / hist_real.sum()
                
                # Compute KL divergence
                kl_div = entropy(hist_gen, hist_real)
                kl_divs.append(kl_div)
            
            return np.mean(kl_divs)
        
        except Exception as e:
            logger.warning(f"Error computing KL divergence: {e}")
            return float('inf')
    
    def evaluate(self, individual: Individual, X_train: np.ndarray, y_train: np.ndarray,
                X_val: np.ndarray, y_val: np.ndarray, 
                dag_wgan_framework) -> float:
        """
        Evaluate individual fitness using the multi-objective function.
        
        Args:
            individual: Individual to evaluate
            X_train: Training features
            y_train: Training labels
            X_val: Validation features
            y_val: Validation labels
            dag_wgan_framework: DAG-WGAN framework instance
            
        Returns:
            Fitness score
        """
        # Create cache key
        cache_key = tuple(individual.genes)
        if cache_key in self.evaluation_cache:
            return self.evaluation_cache[cache_key]
        
        try:
            # Decode parameters
            bounds = ParameterBounds()
            params = bounds.decode_individual(individual)
            
            # Configure framework with optimized parameters (section 4.2)
            config = {
                'alpha': params['alpha_bandwidth'],        # KDE bandwidth factor α
                'k_neighbors': params['k_neighbors'],      # ADASYN neighborhood size k
                'lambda_gp': params['lambda_gp'],          # WGAN-GP gradient penalty λ_gp
                'lr_g': params['eta_g'],                   # Generator learning rate η_G
                'lr_d': params['eta_d'],                   # Critic learning rate η_D
                'beta': params['beta_oversample'],         # Oversampling ratio β
                'c_regularization': params['c_regularization'],  # Classifier regularization C
                'n_critic': 5,  # Fixed for stability
                'device': 'cpu'
            }
            
            # Initialize and train framework
            dag_wgan_framework.config.update(config)
            dag_wgan_framework.initialize_components(X_train.shape[1])
            
            # Train with reduced epochs for GA evaluation
            training_result = dag_wgan_framework.fit(X_train, y_train, epochs=50)
            
            if training_result['status'] != 'success':
                fitness = 0.0
            else:
                # Generate balanced dataset
                X_balanced, y_balanced = dag_wgan_framework.generate_balanced_dataset(X_train, y_train)
                
                # Train classifier on balanced data with regularization
                from sklearn.svm import SVC
                clf = SVC(C=params['c_regularization'], probability=True, random_state=42)
                clf.fit(X_balanced, y_balanced)
                
                # Evaluate on validation set
                y_pred = clf.predict(X_val)
                y_pred_proba = clf.predict_proba(X_val)[:, 1] if len(np.unique(y_val)) > 1 else y_pred
                
                # Compute metrics
                f1_minority = f1_score(y_val, y_pred, pos_label=1)
                
                # Compute Jensen-Shannon divergence (OverlapScore in Equation 9)
                X_minority_real = X_train[y_train == 1]
                X_minority_gen = X_balanced[y_balanced == 1][len(X_minority_real):]  # Only synthetic samples
                js_divergence = self.compute_jensen_shannon_divergence(X_minority_gen, X_minority_real)
                overlap_score = 1.0 / (1.0 + js_divergence)  # Convert to overlap score (higher is better)

                # Compute loss variance from training history
                loss_variance = 0.0
                if hasattr(dag_wgan_framework.wgan, 'loss_variance_history') and dag_wgan_framework.wgan.loss_variance_history:
                    loss_variance = np.mean(dag_wgan_framework.wgan.loss_variance_history[-10:])  # Recent variance

                # Multi-objective fitness (Equation 9)
                fitness = f1_minority + self.gamma * overlap_score - self.eta * loss_variance
                
                # Ensure non-negative fitness
                fitness = max(0.0, fitness)
        
        except Exception as e:
            logger.warning(f"Error evaluating individual: {e}")
            fitness = 0.0
        
        # Cache result
        self.evaluation_cache[cache_key] = fitness
        individual.fitness = fitness
        
        return fitness


class MultiDimensionalGeneticAlgorithm:
    """
    Multi-dimensional genetic algorithm for DAG-WGAN parameter optimization.
    Implements tournament selection, simulated binary crossover, and polynomial mutation.
    """
    
    def __init__(self, ga_params: GAParameters, fitness_function: MultiObjectiveFitnessFunction):
        """
        Initialize genetic algorithm.
        
        Args:
            ga_params: GA configuration parameters
            fitness_function: Fitness evaluation function
        """
        self.ga_params = ga_params
        self.fitness_function = fitness_function
        self.bounds = ParameterBounds()
        
        # Set random seed
        random.seed(ga_params.random_state)
        np.random.seed(ga_params.random_state)
        
        # Evolution tracking
        self.best_fitness_history = []
        self.avg_fitness_history = []
        self.diversity_history = []
        self.best_individual = None
        
    def initialize_population(self) -> List[Individual]:
        """
        Initialize population with random individuals.
        
        Returns:
            List of initialized individuals
        """
        population = []
        
        for _ in range(self.ga_params.population_size):
            # Generate random genes in [0, 1] range
            genes = [random.random() for _ in range(self.bounds.n_params)]
            individual = Individual(genes=genes)
            population.append(individual)
        
        logger.info(f"Initialized population of {len(population)} individuals")
        return population
    
    def tournament_selection(self, population: List[Individual]) -> Individual:
        """
        Tournament selection for parent selection.
        
        Args:
            population: Current population
            
        Returns:
            Selected individual
        """
        tournament = random.sample(population, self.ga_params.tournament_size)
        return max(tournament, key=lambda x: x.fitness)
    
    def simulated_binary_crossover(self, parent1: Individual, parent2: Individual,
                                 eta_c: float = 20.0) -> Tuple[Individual, Individual]:
        """
        Simulated binary crossover (SBX) for continuous parameters.
        
        Args:
            parent1: First parent
            parent2: Second parent
            eta_c: Distribution index for crossover
            
        Returns:
            Two offspring individuals
        """
        child1_genes = []
        child2_genes = []
        
        for i in range(len(parent1.genes)):
            if random.random() <= 0.5:  # Crossover probability per gene
                p1_gene = parent1.genes[i]
                p2_gene = parent2.genes[i]
                
                # SBX crossover
                if abs(p1_gene - p2_gene) > 1e-14:
                    if p1_gene > p2_gene:
                        p1_gene, p2_gene = p2_gene, p1_gene
                    
                    # Calculate beta
                    rand = random.random()
                    if rand <= 0.5:
                        beta = (2.0 * rand) ** (1.0 / (eta_c + 1.0))
                    else:
                        beta = (1.0 / (2.0 * (1.0 - rand))) ** (1.0 / (eta_c + 1.0))
                    
                    # Generate offspring
                    c1_gene = 0.5 * ((1.0 + beta) * p1_gene + (1.0 - beta) * p2_gene)
                    c2_gene = 0.5 * ((1.0 - beta) * p1_gene + (1.0 + beta) * p2_gene)
                    
                    # Ensure bounds
                    c1_gene = max(0.0, min(1.0, c1_gene))
                    c2_gene = max(0.0, min(1.0, c2_gene))
                    
                    child1_genes.append(c1_gene)
                    child2_genes.append(c2_gene)
                else:
                    child1_genes.append(p1_gene)
                    child2_genes.append(p2_gene)
            else:
                child1_genes.append(parent1.genes[i])
                child2_genes.append(parent2.genes[i])
        
        return Individual(genes=child1_genes), Individual(genes=child2_genes)
    
    def polynomial_mutation(self, individual: Individual, eta_m: float = 15.0) -> Individual:
        """
        Polynomial mutation for continuous parameters.
        
        Args:
            individual: Individual to mutate
            eta_m: Distribution index for mutation
            
        Returns:
            Mutated individual
        """
        mutated_genes = []
        
        for gene in individual.genes:
            if random.random() <= self.ga_params.mutation_rate:
                # Polynomial mutation
                rand = random.random()
                if rand < 0.5:
                    delta = (2.0 * rand) ** (1.0 / (eta_m + 1.0)) - 1.0
                else:
                    delta = 1.0 - (2.0 * (1.0 - rand)) ** (1.0 / (eta_m + 1.0))
                
                mutated_gene = gene + delta
                mutated_gene = max(0.0, min(1.0, mutated_gene))
                mutated_genes.append(mutated_gene)
            else:
                mutated_genes.append(gene)
        
        return Individual(genes=mutated_genes)
    
    def compute_population_diversity(self, population: List[Individual]) -> float:
        """
        Compute population diversity using average pairwise distance.
        
        Args:
            population: Current population
            
        Returns:
            Diversity measure
        """
        if len(population) < 2:
            return 0.0
        
        distances = []
        for i in range(len(population)):
            for j in range(i + 1, len(population)):
                dist = np.linalg.norm(np.array(population[i].genes) - np.array(population[j].genes))
                distances.append(dist)
        
        return np.mean(distances) if distances else 0.0
    
    def optimize(self, X_train: np.ndarray, y_train: np.ndarray,
                X_val: np.ndarray, y_val: np.ndarray,
                dag_wgan_framework) -> Dict:
        """
        Execute the genetic algorithm optimization.
        
        Args:
            X_train: Training features
            y_train: Training labels
            X_val: Validation features
            y_val: Validation labels
            dag_wgan_framework: DAG-WGAN framework instance
            
        Returns:
            Optimization results dictionary
        """
        logger.info("Starting multi-dimensional genetic algorithm optimization")
        
        # Initialize population
        population = self.initialize_population()
        
        # Evaluate initial population
        logger.info("Evaluating initial population...")
        for individual in population:
            self.fitness_function.evaluate(individual, X_train, y_train, X_val, y_val, dag_wgan_framework)
        
        # Sort population by fitness
        population.sort(key=lambda x: x.fitness, reverse=True)
        self.best_individual = population[0].copy()
        
        # Evolution loop
        stagnation_count = 0
        
        for generation in range(self.ga_params.max_generations):
            # Track statistics
            fitnesses = [ind.fitness for ind in population]
            best_fitness = max(fitnesses)
            avg_fitness = np.mean(fitnesses)
            diversity = self.compute_population_diversity(population)
            
            self.best_fitness_history.append(best_fitness)
            self.avg_fitness_history.append(avg_fitness)
            self.diversity_history.append(diversity)
            
            # Check for improvement
            if best_fitness > self.best_individual.fitness:
                self.best_individual = population[0].copy()
                stagnation_count = 0
            else:
                stagnation_count += 1
            
            # Early stopping
            if stagnation_count >= self.ga_params.early_stop_patience:
                logger.info(f"Early stopping at generation {generation} due to stagnation")
                break
            
            # Log progress
            if generation % 10 == 0:
                logger.info(f"Generation {generation}: Best={best_fitness:.4f}, Avg={avg_fitness:.4f}, Diversity={diversity:.4f}")
            
            # Create next generation
            new_population = []
            
            # Elitism: keep best individuals
            elite_size = min(self.ga_params.elite_size, len(population))
            new_population.extend([ind.copy() for ind in population[:elite_size]])
            
            # Generate offspring
            while len(new_population) < self.ga_params.population_size:
                # Selection
                parent1 = self.tournament_selection(population)
                parent2 = self.tournament_selection(population)
                
                # Crossover
                if random.random() < self.ga_params.crossover_rate:
                    child1, child2 = self.simulated_binary_crossover(parent1, parent2)
                else:
                    child1, child2 = parent1.copy(), parent2.copy()
                
                # Mutation
                child1 = self.polynomial_mutation(child1)
                child2 = self.polynomial_mutation(child2)
                
                new_population.extend([child1, child2])
            
            # Trim to population size
            new_population = new_population[:self.ga_params.population_size]
            
            # Evaluate new individuals
            for individual in new_population:
                if individual.fitness == 0.0:  # Only evaluate new individuals
                    self.fitness_function.evaluate(individual, X_train, y_train, X_val, y_val, dag_wgan_framework)
            
            # Sort and update population
            new_population.sort(key=lambda x: x.fitness, reverse=True)
            population = new_population
        
        # Final results
        best_params = self.bounds.decode_individual(self.best_individual)
        
        optimization_results = {
            'best_individual': self.best_individual,
            'best_parameters': best_params,
            'best_fitness': self.best_individual.fitness,
            'fitness_history': self.best_fitness_history,
            'avg_fitness_history': self.avg_fitness_history,
            'diversity_history': self.diversity_history,
            'generations_run': len(self.best_fitness_history),
            'final_population': population
        }
        
        logger.info(f"Optimization completed. Best fitness: {self.best_individual.fitness:.4f}")
        logger.info(f"Best parameters: {best_params}")
        
        return optimization_results
