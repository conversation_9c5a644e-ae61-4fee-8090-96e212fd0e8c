"""
DAG-WGAN: 动态密度引导的ADASYN-WGAN-GP与GA协同优化

该模块实现了研究论文中描述的核心DAG-WGAN框架。
框架集成了密度感知、自适应合成和动态反馈机制，
用于解决机器学习中的类别不平衡问题。

作者: 研究团队
日期: 2024-08-07
"""

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from sklearn.neighbors import NearestNeighbors
from sklearn.metrics import f1_score, roc_auc_score
from scipy.stats import gaussian_kde
from scipy.stats import wasserstein_distance
import logging
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

# 导入统一架构模块 - 使用绝对导入避免相对导入问题
try:
    from unified_architecture import (
        ResNet18Generator, ResNet18Discriminator, ResNet18Classifier,
        create_unified_optimizer, AdaptiveBandwidthKDE
    )
    from unified_metrics import UnifiedMetricsCalculator, MetricsTracker
    from unified_genetic_algorithm import UnifiedGeneticAlgorithm, UnifiedGAConfig
    UNIFIED_MODULES_AVAILABLE = True
except ImportError as e:
    print(f"警告: 统一架构模块导入失败: {e}")
    print("将使用原始架构运行...")
    UNIFIED_MODULES_AVAILABLE = False

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DensityPerceptionLayer:
    """
    统一密度感知层，实现动态热图生成
    以及公式(6-7)中描述的自适应带宽选择。
    """

    def __init__(self, alpha: float = 1.0, k_neighbors: int = 5):
        """
        初始化密度感知层，使用统一的自适应带宽KDE。

        参数:
            alpha: 自适应带宽选择的带宽因子
            k_neighbors: 考虑的最近邻数量
        """
        self.alpha = alpha
        self.k_neighbors = k_neighbors
        self.nn_model = None

        # 使用统一的自适应带宽KDE (如果可用)
        if UNIFIED_MODULES_AVAILABLE:
            self.kde_model = AdaptiveBandwidthKDE(bandwidth='scott', kernel='gaussian')
        else:
            self.kde_model = None
        self.adaptive_bandwidths = {}

    def compute_adaptive_bandwidth(self, X: np.ndarray, x_i: np.ndarray, x_i_idx: int) -> float:
        """
        使用公式(7)计算样本x_i的自适应带宽h_i。

        参数:
            X: 训练数据(仅少数类)
            x_i: 样本点
            x_i_idx: 样本点索引

        返回:
            自适应带宽h_i
        """
        if self.nn_model is None:
            self.nn_model = NearestNeighbors(n_neighbors=self.k_neighbors + 1)
            self.nn_model.fit(X)

        # Find k nearest neighbors (excluding self)
        distances, indices = self.nn_model.kneighbors([x_i])
        neighbor_distances = distances[0][1:]  # Exclude self (distance 0)

        # Compute adaptive bandwidth using Equation (7)
        h_i = self.alpha * np.median(neighbor_distances)

        return h_i

    def gaussian_kernel(self, x_i: np.ndarray, x_j: np.ndarray, h: float) -> float:
        """
        Compute Gaussian kernel K_h(x_i, x_j).

        Args:
            x_i: First sample
            x_j: Second sample
            h: Bandwidth

        Returns:
            Kernel value
        """
        distance_sq = np.sum((x_i - x_j) ** 2)
        return np.exp(-distance_sq / (2 * h ** 2))

    def compute_local_density_weight(self, X: np.ndarray, x_i: np.ndarray, x_i_idx: int) -> float:
        """
        Compute local density weight ρ(x_i) using Equation (6).

        Args:
            X: Training data (minority class only)
            x_i: Sample point
            x_i_idx: Index of sample point

        Returns:
            Local density weight ρ(x_i)
        """
        if self.nn_model is None:
            self.nn_model = NearestNeighbors(n_neighbors=self.k_neighbors + 1)
            self.nn_model.fit(X)

        # Find k nearest neighbors (excluding self)
        distances, indices = self.nn_model.kneighbors([x_i])
        neighbor_indices = indices[0][1:]  # Exclude self
        neighbors = X[neighbor_indices]

        # Compute adaptive bandwidth
        h_i = self.compute_adaptive_bandwidth(X, x_i, x_i_idx)

        # Compute density weight using Equation (6)
        kernel_sum = 0.0
        for x_j in neighbors:
            kernel_sum += self.gaussian_kernel(x_i, x_j, h_i)

        # ρ(x_i) = (1/|N_k(x_i)|) * Σ K_h(x_i, x_j)
        rho_i = kernel_sum / len(neighbors) if len(neighbors) > 0 else 0.0

        # Ensure reasonable range for density weights
        rho_i = max(0.01, min(1.0, rho_i))  # Clamp to [0.01, 1.0]

        return rho_i
    
    def compute_difficulty_scores(self, X_minority: np.ndarray, X_majority: np.ndarray) -> np.ndarray:
        """
        Compute local difficulty D(x_j) for each minority sample.

        Args:
            X_minority: Minority class samples
            X_majority: Majority class samples

        Returns:
            Array of difficulty scores
        """
        difficulty_scores = []

        # Combine data for neighborhood analysis
        X_combined = np.vstack([X_minority, X_majority])
        y_combined = np.hstack([np.ones(len(X_minority)), np.zeros(len(X_majority))])

        # Fit nearest neighbors on combined data
        nn_combined = NearestNeighbors(n_neighbors=self.k_neighbors + 1)
        nn_combined.fit(X_combined)

        for i, x_i in enumerate(X_minority):
            # Find k nearest neighbors in combined dataset
            distances, indices = nn_combined.kneighbors([x_i])
            neighbor_labels = y_combined[indices[0][1:]]  # Exclude self

            # Difficulty is proportion of majority class neighbors
            difficulty = 1.0 - neighbor_labels.mean()
            difficulty_scores.append(difficulty)

        return np.array(difficulty_scores)

    def generate_density_heatmap(self, X_minority: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        Generate dynamic heatmap using unified adaptive bandwidth KDE.
        使用统一的自适应带宽KDE (高斯核 + k-d树加速 + 大津法阈值)

        Args:
            X_minority: Minority class samples

        Returns:
            Tuple of (density_weights, heatmap_values)
        """
        if len(X_minority) < 2:
            return np.ones(len(X_minority)), np.ones(len(X_minority))

        if UNIFIED_MODULES_AVAILABLE and self.kde_model is not None:
            # 使用统一的自适应带宽KDE (高斯核 + k-d树加速)
            self.kde_model.fit(X_minority)

            # 计算密度值
            log_densities = self.kde_model.score_samples(X_minority)
            densities = np.exp(log_densities)

            # 使用大津法确定动态阈值
            threshold = self.kde_model.apply_otsu_threshold(densities)

            # 计算密度权重 (基于阈值的相对密度)
            density_weights = np.maximum(0.01, 1.0 - (densities / (threshold + 1e-10)))

            # 归一化密度权重
            if np.sum(density_weights) > 0:
                density_weights = density_weights / np.sum(density_weights)

            # 热图值用于可视化
            heatmap_values = densities

            logger.info(f"Generated density heatmap with unified KDE: {len(density_weights)} samples")
            logger.info(f"Density weight range: [{np.min(density_weights):.4f}, {np.max(density_weights):.4f}]")
            logger.info(f"Otsu threshold: {threshold:.4f}")

            return density_weights, heatmap_values
        else:
            # 回退到原始方法
            density_weights = []
            adaptive_bandwidths = []

            for i, x_i in enumerate(X_minority):
                # Compute density weight using Equation (6)
                rho_i = self.compute_local_density_weight(X_minority, x_i, i)
                density_weights.append(rho_i)

                # Compute adaptive bandwidth using Equation (7)
                h_i = self.compute_adaptive_bandwidth(X_minority, x_i, i)
                adaptive_bandwidths.append(h_i)

            return np.array(density_weights), np.array(adaptive_bandwidths)


class AdaptiveSynthesisLayer:
    """
    Unified Adaptive Synthesis Layer implementing density-guided ADASYN
    with modified generation formula as described in Equation (8).
    """

    def __init__(self, density_layer: DensityPerceptionLayer):
        """
        Initialize adaptive synthesis layer.

        Args:
            density_layer: Density perception layer instance
        """
        self.density_layer = density_layer

    def compute_generation_counts(self, X_minority: np.ndarray, X_majority: np.ndarray,
                                 density_weights: np.ndarray, difficulty_scores: np.ndarray,
                                 beta: float = 0.8) -> np.ndarray:
        """
        Compute number of synthetic samples g_i for each minority sample using Equation (8).

        Args:
            X_minority: Minority class samples
            X_majority: Majority class samples
            density_weights: Density weights ρ(x_i) for each sample
            difficulty_scores: Difficulty scores D(x_j) for each sample
            beta: Oversampling ratio control parameter

        Returns:
            Array of generation counts for each minority sample
        """
        N_m = len(X_majority)  # Number of majority samples
        N_min = len(X_minority)  # Number of minority samples

        # Total samples needed for balancing
        total_needed = int(beta * (N_m - N_min))

        if total_needed <= 0:
            return np.zeros(N_min, dtype=int)

        # Compute generation weights using Equation (8) logic
        # Use (1 - ρ(x_i)) * D(x_i) as the weight for each sample
        generation_weights = (1 - density_weights) * difficulty_scores

        # Normalize weights to sum to 1
        if np.sum(generation_weights) > 0:
            generation_weights = generation_weights / np.sum(generation_weights)
        else:
            # If all weights are zero, distribute equally
            generation_weights = np.ones(N_min) / N_min

        # Distribute total_needed samples according to weights
        generation_counts = np.floor(generation_weights * total_needed).astype(int)

        # Distribute remaining samples randomly to ensure we get exactly total_needed
        remaining = total_needed - np.sum(generation_counts)
        if remaining > 0:
            # Add remaining samples to highest weighted samples
            sorted_indices = np.argsort(generation_weights)[::-1]
            for i in range(remaining):
                generation_counts[sorted_indices[i % N_min]] += 1

        logger.info(f"Generation counts: total_needed={total_needed}, "
                   f"actual_total={np.sum(generation_counts)}, "
                   f"per_sample={generation_counts}")

        return generation_counts

    def density_guided_adasyn(self, X: np.ndarray, y: np.ndarray, beta: float = 0.8) -> np.ndarray:
        """
        Generate synthetic samples using unified density-guided ADASYN.

        Args:
            X: Feature data
            y: Labels
            beta: Oversampling ratio control parameter

        Returns:
            Generated synthetic samples
        """
        X_minority = X[y == 1]
        X_majority = X[y == 0]

        if len(X_minority) < 2:
            logger.warning("Insufficient minority samples for ADASYN")
            return np.empty((0, X.shape[1]))

        # Generate density heatmap and compute difficulty scores
        density_weights, _ = self.density_layer.generate_density_heatmap(X_minority)
        difficulty_scores = self.density_layer.compute_difficulty_scores(X_minority, X_majority)

        logger.info(f"Density weights: min={np.min(density_weights):.4f}, "
                   f"max={np.max(density_weights):.4f}, mean={np.mean(density_weights):.4f}")
        logger.info(f"Difficulty scores: min={np.min(difficulty_scores):.4f}, "
                   f"max={np.max(difficulty_scores):.4f}, mean={np.mean(difficulty_scores):.4f}")

        # Compute generation counts for each sample
        generation_counts = self.compute_generation_counts(
            X_minority, X_majority, density_weights, difficulty_scores, beta
        )

        # Generate synthetic samples
        synthetic_samples = []
        nn_model = NearestNeighbors(n_neighbors=min(self.density_layer.k_neighbors, len(X_minority)-1))
        nn_model.fit(X_minority)

        for i, x_i in enumerate(X_minority):
            g_i = generation_counts[i]

            if g_i > 0:
                # Find nearest neighbors
                _, indices = nn_model.kneighbors([x_i])
                neighbor_indices = indices[0][1:]  # Exclude self

                for _ in range(g_i):
                    if len(neighbor_indices) > 0:
                        # Select random neighbor
                        neighbor_idx = np.random.choice(neighbor_indices)
                        x_j = X_minority[neighbor_idx]

                        # Generate synthetic sample with random interpolation
                        lambda_val = np.random.uniform(0, 1)
                        x_syn = x_i + lambda_val * (x_j - x_i)
                        synthetic_samples.append(x_syn)

        logger.info(f"Generated {len(synthetic_samples)} samples using density-guided ADASYN")
        return np.array(synthetic_samples) if synthetic_samples else np.empty((0, X.shape[1]))


class BoundaryAwareWGAN:
    """
    Boundary-Aware WGAN-GP implementing density-weighted generation and
    gradient penalty as described in Equations (12-13).
    """

    def __init__(self, input_dim: int, latent_dim: int = 100,
                 lr_g: float = 0.001, lr_d: float = 0.001,
                 lambda_gp: float = 10.0, device: str = 'cpu'):
        """
        Initialize boundary-aware WGAN-GP with unified ResNet-18 architecture.

        Args:
            input_dim: Input feature dimension
            latent_dim: Latent space dimension
            lr_g: Generator learning rate (统一设置为0.001)
            lr_d: Discriminator learning rate (统一设置为0.001)
            lambda_gp: Gradient penalty coefficient
            device: Computing device
        """
        self.input_dim = input_dim
        self.latent_dim = latent_dim
        self.lambda_gp = lambda_gp
        self.device = torch.device(device)

        # 使用统一的ResNet-18架构（如果可用）
        if UNIFIED_MODULES_AVAILABLE:
            self.generator = ResNet18Generator(latent_dim, input_dim).to(self.device)
            self.discriminator = ResNet18Discriminator(input_dim).to(self.device)

            # 使用统一的Adam优化器配置 (lr=0.001, β₁=0.9, β₂=0.999)
            self.optimizer_g = create_unified_optimizer(self.generator.parameters(), lr=lr_g)
            self.optimizer_d = create_unified_optimizer(self.discriminator.parameters(), lr=lr_d)
        else:
            # 回退到原始架构
            self.generator = self._build_generator().to(self.device)
            self.discriminator = self._build_discriminator().to(self.device)

            # 使用标准Adam优化器
            self.optimizer_g = optim.Adam(self.generator.parameters(), lr=lr_g, betas=(0.9, 0.999))
            self.optimizer_d = optim.Adam(self.discriminator.parameters(), lr=lr_d, betas=(0.9, 0.999))

        # Dynamic adaptation parameters
        self.lambda_gp_history = [lambda_gp]
        self.loss_variance_history = []
        
    def _build_generator(self) -> nn.Module:
        """构建生成器网络（回退方法）"""
        return nn.Sequential(
            nn.Linear(self.latent_dim, 256),
            nn.BatchNorm1d(256),
            nn.ReLU(True),
            nn.Linear(256, 512),
            nn.BatchNorm1d(512),
            nn.ReLU(True),
            nn.Linear(512, 256),
            nn.BatchNorm1d(256),
            nn.ReLU(True),
            nn.Linear(256, 128),
            nn.BatchNorm1d(128),
            nn.ReLU(True),
            nn.Linear(128, self.input_dim),
            nn.Tanh()
        )

    def _build_discriminator(self) -> nn.Module:
        """构建判别器网络（回退方法）"""
        return nn.Sequential(
            nn.Linear(self.input_dim, 128),
            nn.LeakyReLU(0.2, inplace=True),
            nn.Dropout(0.3),
            nn.Linear(128, 256),
            nn.LeakyReLU(0.2, inplace=True),
            nn.Dropout(0.3),
            nn.Linear(256, 512),
            nn.LeakyReLU(0.2, inplace=True),
            nn.Dropout(0.3),
            nn.Linear(512, 256),
            nn.LeakyReLU(0.2, inplace=True),
            nn.Dropout(0.3),
            nn.Linear(256, 1)
        )
    
    def compute_boundary_aware_generator_loss(self, z: torch.Tensor,
                                            density_weights: torch.Tensor) -> torch.Tensor:
        """
        Compute boundary-aware generator loss using Equation (12).

        Args:
            z: Latent noise vectors
            density_weights: Density weights for generated samples

        Returns:
            Generator loss
        """
        # Generate samples
        generated_samples = self.generator(z)

        # Discriminator output for generated samples
        d_output = self.discriminator(generated_samples)

        # Boundary-aware generator loss (Equation 12, first term)
        # L_G = -E[ρ(G(z)) · D(G(z))]
        weighted_d_output = density_weights * d_output.squeeze()
        generator_loss = -weighted_d_output.mean()

        return generator_loss

    def compute_density_weighted_gradient_penalty(self, real_data: torch.Tensor,
                                                fake_data: torch.Tensor,
                                                density_weights: torch.Tensor) -> torch.Tensor:
        """
        Compute density-weighted gradient penalty as in Equation (12).

        Args:
            real_data: Real data samples
            fake_data: Generated fake samples
            density_weights: Density weights for interpolated samples

        Returns:
            Density-weighted gradient penalty
        """
        batch_size = real_data.size(0)
        alpha = torch.rand(batch_size, 1).to(self.device)

        # Interpolated samples
        interpolated = alpha * real_data + (1 - alpha) * fake_data
        interpolated.requires_grad_(True)

        # Discriminator output for interpolated samples
        d_interpolated = self.discriminator(interpolated)

        # Compute gradients
        gradients = torch.autograd.grad(
            outputs=d_interpolated,
            inputs=interpolated,
            grad_outputs=torch.ones_like(d_interpolated),
            create_graph=True,
            retain_graph=True
        )[0]

        # Apply density weighting to gradients (Equation 13)
        gradient_norm = gradients.norm(2, dim=1)
        density_weighted_penalty = density_weights * (gradient_norm - 1) ** 2

        return density_weighted_penalty.mean()

    def update_lambda_gp_dynamically(self, loss_variance: float, nu: float = 0.01, tau: float = 0.1):
        """
        Update gradient penalty coefficient dynamically using Equation (11).

        Args:
            loss_variance: Current loss variance
            nu: Adjustment sensitivity parameter
            tau: Target stability threshold
        """
        # λ_gp^{t+1} = λ_gp^t * exp(ν * (LossVariance - τ))
        current_lambda = self.lambda_gp_history[-1]
        adjustment = nu * (loss_variance - tau)
        new_lambda = current_lambda * np.exp(adjustment)

        # Clamp to reasonable bounds
        new_lambda = max(1.0, min(50.0, new_lambda))

        self.lambda_gp = new_lambda
        self.lambda_gp_history.append(new_lambda)
    
    def train_step(self, real_data: torch.Tensor, density_weights: torch.Tensor,
                   n_critic: int = 5) -> Tuple[float, float, float]:
        """
        Perform one boundary-aware training step.

        Args:
            real_data: Real training data
            density_weights: Density weights for samples
            n_critic: Number of discriminator updates per generator update

        Returns:
            Tuple of (discriminator_loss, generator_loss, loss_variance)
        """
        batch_size = real_data.size(0)
        d_losses = []

        # Train discriminator with density-weighted updates
        for _ in range(n_critic):
            self.optimizer_d.zero_grad()

            # Generate fake data
            z = torch.randn(batch_size, self.latent_dim).to(self.device)
            fake_data = self.generator(z).detach()

            # Discriminator outputs
            d_real = self.discriminator(real_data)
            d_fake = self.discriminator(fake_data)

            # Wasserstein loss
            w_distance = d_real.mean() - d_fake.mean()

            # Density-weighted gradient penalty (Equation 12)
            gp = self.compute_density_weighted_gradient_penalty(real_data, fake_data, density_weights)

            # Total discriminator loss
            d_loss = -w_distance + self.lambda_gp * gp
            d_loss.backward()
            self.optimizer_d.step()

            d_losses.append(d_loss.item())

        # Train generator with boundary-aware loss
        self.optimizer_g.zero_grad()
        z = torch.randn(batch_size, self.latent_dim).to(self.device)

        # Compute density weights for generated samples (approximation)
        gen_density_weights = density_weights  # Use same weights for simplicity

        # Boundary-aware generator loss (Equation 12)
        g_loss = self.compute_boundary_aware_generator_loss(z, gen_density_weights)
        g_loss.backward()
        self.optimizer_g.step()

        # Compute loss variance for dynamic adaptation
        loss_variance = np.var(d_losses) if len(d_losses) > 1 else 0.0
        self.loss_variance_history.append(loss_variance)

        # Update λ_gp dynamically (Equation 11)
        if len(self.loss_variance_history) > 5:  # Wait for some history
            self.update_lambda_gp_dynamically(loss_variance)

        return np.mean(d_losses), g_loss.item(), loss_variance
    
    def generate_samples(self, n_samples: int) -> np.ndarray:
        """
        Generate synthetic samples.
        
        Args:
            n_samples: Number of samples to generate
            
        Returns:
            Generated samples
        """
        self.generator.eval()
        with torch.no_grad():
            z = torch.randn(n_samples, self.latent_dim).to(self.device)
            samples = self.generator(z).cpu().numpy()
        self.generator.train()
        return samples


class DAGWGANFramework:
    """
    Main DAG-WGAN Framework integrating all components.
    """
    
    def __init__(self, config: Dict):
        """
        Initialize DAG-WGAN framework with unified components.

        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.density_layer = None
        self.synthesis_layer = None
        self.wgan = None

        # 初始化统一的评估指标计算器和跟踪器（如果可用）
        if UNIFIED_MODULES_AVAILABLE:
            self.metrics_calculator = UnifiedMetricsCalculator()
            self.metrics_tracker = MetricsTracker()

            # 初始化统一的遗传算法配置
            self.ga_config = UnifiedGAConfig(
                population_size=50,      # 种群规模50
                max_generations=50,      # 运行50代
                tournament_size=3,       # 锦标赛选择规模3
                eta_c=15.0,             # SBX交叉η_c=15
                eta_m=20.0              # 多项式变异η_m=20
            )
        else:
            self.metrics_calculator = None
            self.metrics_tracker = None
            self.ga_config = None

        # Training history with unified metrics
        self.training_history = {
            'generator_losses': [],
            'discriminator_losses': [],
            'adasyn_samples': 0,
            'training_time': 0.0,
            'unified_metrics': {}
        }
        
    def initialize_components(self, input_dim: int):
        """Initialize all framework components."""
        # Initialize density perception layer with adaptive bandwidth
        self.density_layer = DensityPerceptionLayer(
            alpha=self.config.get('alpha', 1.0),
            k_neighbors=self.config.get('k_neighbors', 5)
        )

        # Initialize adaptive synthesis layer
        self.synthesis_layer = AdaptiveSynthesisLayer(self.density_layer)

        # Initialize boundary-aware WGAN with unified ResNet-18 architecture
        # 使用统一的学习率配置 (lr=0.001, β₁=0.9, β₂=0.999)
        self.wgan = BoundaryAwareWGAN(
            input_dim=input_dim,
            latent_dim=self.config.get('latent_dim', 100),
            lr_g=self.config.get('lr_g', 0.001),  # 统一学习率0.001
            lr_d=self.config.get('lr_d', 0.001),  # 统一学习率0.001
            lambda_gp=self.config.get('lambda_gp', 10.0),
            device=self.config.get('device', 'cpu')
        )

        logger.info("DAG-WGAN components initialized successfully")
    
    def fit(self, X: np.ndarray, y: np.ndarray, epochs: int = 100) -> Dict:
        """
        Train the DAG-WGAN framework.
        
        Args:
            X: Training features
            y: Training labels
            epochs: Number of training epochs
            
        Returns:
            Training results dictionary
        """
        if self.density_layer is None:
            self.initialize_components(X.shape[1])
        
        logger.info(f"Starting DAG-WGAN training for {epochs} epochs")
        logger.info("使用统一架构: ResNet-18, Adam优化器(lr=0.001, β₁=0.9, β₂=0.999)")

        # 开始计时（如果可用）
        if self.metrics_tracker:
            self.metrics_tracker.start_timing()
        
        # Generate ADASYN samples using unified density-guided approach
        X_adasyn = self.synthesis_layer.density_guided_adasyn(
            X, y,
            beta=self.config.get('beta', 0.8)
        )
        
        if len(X_adasyn) == 0:
            logger.warning("No ADASYN samples generated")
            return {'status': 'failed', 'reason': 'No ADASYN samples'}
        
        # Prepare data for WGAN training
        X_minority = X[y == 1]
        X_combined = np.vstack([X_minority, X_adasyn])
        
        # Convert to tensors
        X_tensor = torch.FloatTensor(X_combined).to(self.wgan.device)
        
        # Generate density heatmap for training
        density_weights, adaptive_bandwidths = self.density_layer.generate_density_heatmap(X_combined)
        density_tensor = torch.FloatTensor(density_weights).to(self.wgan.device)
        
        # Training loop
        d_losses, g_losses = [], []
        
        for epoch in range(epochs):
            d_loss, g_loss, loss_variance = self.wgan.train_step(
                X_tensor, density_tensor,
                n_critic=self.config.get('n_critic', 5)
            )

            d_losses.append(d_loss)
            g_losses.append(g_loss)

            if epoch % 20 == 0:
                logger.info(f"Epoch {epoch}: D_loss={d_loss:.4f}, G_loss={g_loss:.4f}, "
                           f"LossVar={loss_variance:.4f}, λ_gp={self.wgan.lambda_gp:.2f}")
        
        # 更新训练历史，保留已有的键
        self.training_history.update({
            'discriminator_losses': d_losses,
            'generator_losses': g_losses,
            'adasyn_samples': len(X_adasyn)
        })
        
        logger.info("DAG-WGAN training completed successfully")
        # 停止计时并记录训练时间（如果可用）
        if self.metrics_tracker:
            training_time = self.metrics_tracker.stop_timing()
            self.training_history['training_time'] = training_time

            # 计算损失波动方差
            if self.metrics_calculator:
                loss_metrics = self.metrics_calculator.compute_loss_fluctuation_variance(
                    self.training_history['generator_losses'],
                    self.training_history['discriminator_losses']
                )
                self.training_history['unified_metrics'].update(loss_metrics)

                logger.info(f"训练完成，耗时: {training_time:.2f}秒")
                logger.info(f"损失波动方差: G={loss_metrics.get('generator_loss_variance', 0):.6f}, "
                           f"D={loss_metrics.get('discriminator_loss_variance', 0):.6f}")
            else:
                logger.info(f"训练完成，耗时: {training_time:.2f}秒")
        else:
            logger.info("训练完成")

        return {'status': 'success', 'history': self.training_history}
    
    def generate_balanced_dataset(self, X: np.ndarray, y: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        Generate a balanced dataset using the trained framework.
        
        Args:
            X: Original features
            y: Original labels
            
        Returns:
            Balanced features and labels
        """
        X_minority = X[y == 1]
        X_majority = X[y == 0]
        
        # Generate ADASYN samples using unified approach
        X_adasyn = self.synthesis_layer.density_guided_adasyn(X, y)
        
        # Generate WGAN samples
        n_wgan_samples = len(X_majority) - len(X_minority) - len(X_adasyn)
        if n_wgan_samples > 0:
            X_wgan = self.wgan.generate_samples(n_wgan_samples)
        else:
            X_wgan = np.empty((0, X.shape[1]))
        
        # Combine all samples
        X_balanced = np.vstack([X_majority, X_minority, X_adasyn, X_wgan])
        y_balanced = np.hstack([
            np.zeros(len(X_majority), dtype=int),
            np.ones(len(X_minority) + len(X_adasyn) + len(X_wgan), dtype=int)
        ])
        
        return X_balanced, y_balanced
