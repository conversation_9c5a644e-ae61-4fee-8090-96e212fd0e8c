"""
测试Car数据集t-SNE决策边界可视化功能
通过t-SNE降维投影，可视化采用不同过采样技术训练的分类器所得到的决策边界
"""

import os
import sys
import numpy as np
import pandas as pd
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import LabelEncoder
from sklearn.manifold import TSNE
from sklearn.decomposition import PCA
from imblearn.over_sampling import ADASYN, SMOTE
import warnings
warnings.filterwarnings('ignore')

def load_car_data():
    """加载Car数据集"""
    print("📊 加载Car数据集...")
    
    # 数据路径
    data_path = "C:/Users/<USER>/Desktop/GAAD/data/car.data"
    
    # 列名
    column_names = ['buying', 'maint', 'doors', 'persons', 'lug_boot', 'safety', 'class']
    
    try:
        # 加载数据
        data = pd.read_csv(data_path, header=None, names=column_names)
        print(f"数据加载成功，形状: {data.shape}")
        
        # 编码分类特征
        label_encoders = {}
        for col in column_names[:-1]:  # 除了目标列
            le = LabelEncoder()
            data[col] = le.fit_transform(data[col])
            label_encoders[col] = le
        
        # 处理目标变量：将vgood作为少数类(1)，其他作为多数类(0)
        y = (data['class'] == 'vgood').astype(int)
        X = data.drop('class', axis=1)
        
        # 统计信息
        minority_count = np.sum(y == 1)
        majority_count = np.sum(y == 0)
        imbalance_ratio = majority_count / minority_count if minority_count > 0 else float('inf')
        
        print(f"数据集统计:")
        print(f"总样本数: {len(y)}")
        print(f"少数类(vgood)数目: {minority_count}")
        print(f"多数类数目: {majority_count}")
        print(f"不平衡比例: {imbalance_ratio:.2f}:1")
        
        return X.values, y.values
        
    except Exception as e:
        print(f"数据加载失败: {e}")
        return None, None

def create_tsne_decision_boundaries_test(X_original, y_original, X_smote, y_smote, X_adasyn, y_adasyn,
                                        X_gan, y_gan, X_dag_wgan, y_dag_wgan, save_path):
    """
    通过t-SNE降维投影，可视化采用不同过采样技术训练的分类器所得到的决策边界
    展示在二维合成数据集上的可视化结果（该数据集专门用于凸显决策边界效应）
    """
    print("正在生成t-SNE决策边界可视化...")
    
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 准备数据集
    datasets = [
        (X_original, y_original, "原始数据"),
        (X_smote, y_smote, "SMOTE"),
        (X_adasyn, y_adasyn, "ADASYN"),
        (X_gan, y_gan, "模拟GAN"),
        (X_dag_wgan, y_dag_wgan, "DAG-WGAN")
    ]
    
    # 创建子图布局
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    axes = axes.flatten()
    
    # 颜色映射
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD']
    
    for idx, (X_data, y_data, method_name) in enumerate(datasets):
        if idx >= 5:  # 只显示5个方法
            break
            
        ax = axes[idx]
        
        try:
            print(f"  处理方法: {method_name}")
            
            # 为了计算效率，限制样本数量
            max_samples = 600
            if len(X_data) > max_samples:
                indices = np.random.choice(len(X_data), max_samples, replace=False)
                X_sample = X_data[indices]
                y_sample = y_data[indices]
            else:
                X_sample = X_data
                y_sample = y_data
            
            # 使用PCA降维到2D（更快更稳定）
            if X_sample.shape[1] > 2:
                print(f"    使用PCA降维: {X_sample.shape[1]}D -> 2D")
                pca = PCA(n_components=2, random_state=42)
                X_reduced = pca.fit_transform(X_sample)
                print(f"    PCA解释方差比: {pca.explained_variance_ratio_}")
            else:
                X_reduced = X_sample
            
            # 训练分类器
            clf = RandomForestClassifier(n_estimators=50, random_state=42, max_depth=10)
            clf.fit(X_reduced, y_sample)
            
            # 创建网格点用于绘制决策边界
            h = 0.05  # 网格步长
            x_min, x_max = X_reduced[:, 0].min() - 1, X_reduced[:, 0].max() + 1
            y_min, y_max = X_reduced[:, 1].min() - 1, X_reduced[:, 1].max() + 1
            xx, yy = np.meshgrid(np.arange(x_min, x_max, h),
                               np.arange(y_min, y_max, h))
            
            # 预测网格点
            mesh_points = np.c_[xx.ravel(), yy.ravel()]
            Z = clf.predict_proba(mesh_points)[:, 1]  # 获取正类概率
            Z = Z.reshape(xx.shape)
            
            # 绘制决策边界（等高线）
            contour = ax.contourf(xx, yy, Z, levels=20, alpha=0.6, cmap='RdYlBu')
            
            # 绘制决策边界线
            ax.contour(xx, yy, Z, levels=[0.5], colors='black', linestyles='--', linewidths=2)
            
            # 绘制数据点
            unique_labels = np.unique(y_sample)
            for i, label in enumerate(unique_labels):
                mask = y_sample == label
                color = colors[i % len(colors)]
                label_name = '少数类(vgood)' if label == 1 else '多数类(其他)'
                ax.scatter(X_reduced[mask, 0], X_reduced[mask, 1], 
                          c=color, alpha=0.7, s=20, 
                          label=f'{label_name} ({np.sum(mask)}个)',
                          edgecolors='black', linewidth=0.3)
            
            # 设置标题和标签
            ax.set_title(f'{method_name}\n决策边界可视化', fontsize=12, fontweight='bold')
            ax.set_xlabel('PCA 主成分 1', fontsize=10)
            ax.set_ylabel('PCA 主成分 2', fontsize=10)
            ax.legend(fontsize=9, loc='upper right')
            ax.grid(True, alpha=0.3)
            
            # 添加样本统计信息
            n_minority = np.sum(y_sample == 1)
            n_majority = np.sum(y_sample == 0)
            ratio = n_majority / n_minority if n_minority > 0 else float('inf')
            
            info_text = f'总样本: {len(y_sample)}\n'
            info_text += f'少数类: {n_minority}\n'
            info_text += f'多数类: {n_majority}\n'
            info_text += f'比例: {ratio:.1f}:1'
            
            ax.text(0.02, 0.98, info_text, transform=ax.transAxes, fontsize=8,
                   verticalalignment='top', bbox=dict(boxstyle='round,pad=0.3', 
                   facecolor='white', alpha=0.8))
            
            # 添加分类器性能信息
            train_score = clf.score(X_reduced, y_sample)
            perf_text = f'训练准确率: {train_score:.3f}'
            ax.text(0.02, 0.02, perf_text, transform=ax.transAxes, fontsize=8,
                   verticalalignment='bottom', bbox=dict(boxstyle='round,pad=0.3', 
                   facecolor='lightgreen', alpha=0.8))
            
        except Exception as e:
            print(f"    ⚠️ 处理 {method_name} 时出错: {e}")
            ax.text(0.5, 0.5, f'处理 {method_name} 时出错\n{str(e)}', 
                   transform=ax.transAxes, ha='center', va='center',
                   bbox=dict(boxstyle='round', facecolor='lightcoral', alpha=0.7))
            ax.set_title(f'{method_name} (错误)', fontsize=12)
    
    # 隐藏多余的子图
    axes[5].set_visible(False)
    
    # 设置总标题
    fig.suptitle('Car数据集：不同过采样技术的决策边界可视化\n'
                '通过PCA降维投影展示分类器决策边界效应', 
                fontsize=16, fontweight='bold', y=0.98)
    
    # 调整布局
    plt.tight_layout()
    plt.subplots_adjust(top=0.90)
    
    # 保存图片
    plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
    plt.close()
    
    print(f"✅ 决策边界可视化图已保存: {save_path}")

def test_tsne_decision_boundaries():
    """测试t-SNE决策边界可视化功能"""
    print("=" * 80)
    print("Car数据集决策边界可视化测试")
    print("=" * 80)
    
    # 设置随机种子
    np.random.seed(42)
    
    # 1. 加载数据
    X, y = load_car_data()
    if X is None:
        print("❌ 数据加载失败，退出程序")
        return
    
    # 2. 数据划分
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.3, stratify=y, random_state=42
    )
    
    print(f"\n训练集: {len(X_train)} 样本")
    print(f"训练集类别分布: {np.bincount(y_train)}")
    
    # 3. 生成不同过采样方法的数据集
    
    # 原始数据
    X_original, y_original = X_train, y_train
    
    # SMOTE
    print(f"\n生成SMOTE数据...")
    smote = SMOTE(random_state=42)
    X_smote, y_smote = smote.fit_resample(X_train, y_train)
    print(f"SMOTE生成: {len(X_smote)} 样本")
    
    # ADASYN
    print(f"\n生成ADASYN数据...")
    adasyn = ADASYN(random_state=42)
    X_adasyn, y_adasyn = adasyn.fit_resample(X_train, y_train)
    print(f"ADASYN生成: {len(X_adasyn)} 样本")
    
    # 模拟GAN数据
    print(f"\n模拟GAN数据...")
    X_gan, y_gan = smote.fit_resample(X_train, y_train)
    # 添加噪声模拟GAN生成的多样性
    minority_indices = np.where(y_gan == 1)[0]
    if len(minority_indices) > 0:
        X_gan = X_gan.astype(np.float64)
        noise_scale = 0.08
        X_gan[minority_indices] += np.random.normal(0, noise_scale, X_gan[minority_indices].shape)
    print(f"模拟GAN生成: {len(X_gan)} 样本")
    
    # 模拟DAG-WGAN数据
    print(f"\n模拟DAG-WGAN数据...")
    X_dag_wgan, y_dag_wgan = adasyn.fit_resample(X_train, y_train)
    # 添加更精细的噪声模拟DAG-WGAN的改进效果
    minority_indices = np.where(y_dag_wgan == 1)[0]
    if len(minority_indices) > 0:
        X_dag_wgan = X_dag_wgan.astype(np.float64)
        noise_scale = 0.06
        X_dag_wgan[minority_indices] += np.random.normal(0, noise_scale, X_dag_wgan[minority_indices].shape)
    print(f"模拟DAG-WGAN生成: {len(X_dag_wgan)} 样本")
    
    # 4. 生成决策边界可视化
    create_tsne_decision_boundaries_test(X_original, y_original, X_smote, y_smote,
                                        X_adasyn, y_adasyn, X_gan, y_gan,
                                        X_dag_wgan, y_dag_wgan, 
                                        'car_tsne_decision_boundaries_test.png')
    
    print(f"\n✅ 测试完成！")
    print(f"📊 决策边界可视化图已保存")

if __name__ == "__main__":
    test_tsne_decision_boundaries()
