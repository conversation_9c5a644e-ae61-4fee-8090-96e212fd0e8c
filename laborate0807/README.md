# DAG-WGAN: Dynamic Density-Guided ADASYN-WGAN-GP with GA Co-Optimization

## Overview

This repository implements the DAG-WGAN framework, a novel approach to address class imbalance in machine learning through systematic enhancement of minority class representation and boundary clarity. The framework integrates four key components:

1. **Density Perception Layer**: Identifies critical regions using KDE-based density estimation
2. **Adaptive Synthesis Layer**: Combines ADASYN and WGAN-GP for strategic oversampling
3. **Multi-Dimensional GA Co-Optimization**: Optimizes seven key parameters simultaneously
4. **Dynamic Feedback Iteration**: Continuously refines synthetic samples and density weights

## Key Features

### 🎯 Advanced Density-Guided Synthesis
- KDE-based density estimation with adaptive bandwidth selection
- Density-weighted ADASYN interpolation (Equation 9)
- Density-weighted gradient penalty for WGAN-GP (Equation 10)

### 🧬 Multi-Dimensional Genetic Algorithm
- Simultaneous optimization of 7 critical parameters
- Multi-objective fitness function (Equation 12)
- Tournament selection with simulated binary crossover
- Polynomial mutation with elitism

### 🔄 Dynamic Feedback Mechanisms
- Quality assessment for synthetic samples (Equation 13)
- Adaptive weight decay system (Equation 14)
- Progressive refinement with Lipschitz continuity maintenance

### 📊 Comprehensive Evaluation
- Multi-metric evaluation (F1-Score, G-mean, AUC-ROC)
- Cross-validation framework with statistical significance testing
- Baseline method comparisons (SMOTE, ADASYN, BorderlineSMOTE, etc.)
- Visualization and detailed reporting

## Installation

### Prerequisites
- Python 3.8 or higher
- CUDA-compatible GPU (optional, for accelerated training)

### Setup
```bash
# Clone the repository
git clone <repository-url>
cd laborate0807

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

## Quick Start

### Basic Usage
```bash
# Run with synthetic dataset and default configuration
python main_dag_wgan.py --dataset synthetic --config default

# Run with credit card dataset and fast configuration
python main_dag_wgan.py --dataset credit --config fast

# Run with genetic algorithm optimization
python main_dag_wgan.py --dataset wisconsin --config research --optimize
```

### Configuration Options
- `default`: Balanced performance and speed
- `fast`: Quick execution for testing/debugging
- `high_performance`: Extended training for better results
- `research`: Publication-quality results with comprehensive evaluation
- `cpu`: Optimized for CPU-only execution
- `gpu`: Optimized for GPU acceleration

## Framework Architecture

```
DAG-WGAN Framework
├── Density Perception Layer
│   ├── KDE-based density estimation
│   ├── Adaptive bandwidth selection
│   └── Critical region identification
├── Adaptive Synthesis Layer
│   ├── Weighted ADASYN generation
│   ├── Difficulty-based weighting
│   └── Quality-controlled sampling
├── WGAN-GP Component
│   ├── Density-weighted gradient penalty
│   ├── Enhanced training stability
│   └── Configurable network architecture
├── Genetic Algorithm Optimizer
│   ├── Multi-dimensional parameter space
│   ├── Multi-objective fitness function
│   └── Advanced selection operators
└── Dynamic Feedback System
    ├── Sample quality assessment
    ├── Adaptive weight decay
    └── Progressive refinement
```

## Core Algorithms

### Density-Weighted ADASYN (Equation 9)
```
x_syn = x_i + λ · (x_j - x_i) · (1-ρ(x_i)) / Σ(1-ρ(x_i))
```

### Density-Weighted Gradient Penalty (Equation 10)
```
L_D = E[D(x_gen)] - E[D(x_real)] + γE[ρ(x̂)||∇D(x̂)||²]
```

### Multi-Objective Fitness (Equation 12)
```
f(c_i) = F1_minority + β·OverlapScore - η·KL(p_gen||p_real)
```

### Adaptive Weight Decay (Equation 14)
```
α(t) = α₀ · exp(-t/T)
```

## Parameter Optimization

The genetic algorithm optimizes seven critical parameters:

| Parameter | Range | Type | Description |
|-----------|-------|------|-------------|
| h | [0.1, 5.0] | Continuous | KDE bandwidth |
| λ | [0.1, 1.0] | Continuous | Interpolation strength |
| k | [3, 20] | Integer | ADASYN neighbors |
| k_neigh | [3, 15] | Integer | Density neighbors |
| lr_g | [1e-5, 1e-2] | Log-scale | Generator learning rate |
| lr_d | [1e-5, 1e-2] | Log-scale | Discriminator learning rate |
| α₀ | [0.5, 1.0] | Continuous | Balance parameter |

## Experimental Results

### Performance Metrics
- **F1-Score**: Harmonic mean of precision and recall
- **G-mean**: Geometric mean of sensitivity and specificity
- **AUC-ROC**: Area under the receiver operating characteristic curve
- **Precision/Recall**: Standard classification metrics

### Baseline Comparisons
The framework is compared against:
- SMOTE (Synthetic Minority Oversampling Technique)
- ADASYN (Adaptive Synthetic Sampling)
- BorderlineSMOTE
- SMOTEENN (SMOTE + Edited Nearest Neighbours)
- SMOTETomek (SMOTE + Tomek Links)

## File Structure

```
laborate0807/
├── dag_wgan_framework.py      # Main framework implementation
├── density_synthesis.py       # Density-guided synthesis components
├── genetic_optimization.py    # Multi-dimensional GA optimizer
├── dynamic_feedback.py        # Dynamic feedback mechanisms
├── experimental_evaluation.py # Comprehensive evaluation system
├── config.py                  # Configuration management
├── main_dag_wgan.py          # Main execution script
├── requirements.txt           # Python dependencies
└── README.md                 # This file
```

## Configuration Examples

### Custom Configuration
```python
from config import DAGWGANConfig, DensityConfig, GAConfig

# Create custom configuration
config = DAGWGANConfig()
config.density.bandwidth = 'adaptive'
config.ga.population_size = 100
config.wgan.device = 'cuda'
config.feedback.enable_progressive_refinement = True
```

### Programmatic Usage
```python
from dag_wgan_framework import DAGWGANFramework
from config import get_default_config

# Initialize framework
config = get_default_config()
framework = DAGWGANFramework(config.to_dict())

# Train on your data
framework.fit(X_train, y_train)

# Generate balanced dataset
X_balanced, y_balanced = framework.generate_balanced_dataset(X_train, y_train)
```

## Advanced Features

### Dynamic Feedback Control
```python
from dynamic_feedback import DynamicFeedbackController, FeedbackConfig

# Configure dynamic feedback
feedback_config = FeedbackConfig(
    quality_threshold=0.3,
    refinement_iterations=5,
    enable_progressive_refinement=True
)

controller = DynamicFeedbackController(feedback_config)
```

### Custom Fitness Function
```python
from genetic_optimization import MultiObjectiveFitnessFunction

# Custom fitness with different weights
fitness_func = MultiObjectiveFitnessFunction(
    beta=0.4,  # Overlap score weight
    eta=0.3    # KL divergence penalty
)
```

## Performance Optimization

### GPU Acceleration
```bash
# Use GPU configuration for faster training
python main_dag_wgan.py --dataset credit --config gpu
```

### Memory Management
- Use smaller batch sizes for limited memory
- Enable gradient checkpointing for large models
- Use CPU configuration for memory-constrained environments

### Parallel Processing
The framework supports parallel evaluation during genetic algorithm optimization.

## Troubleshooting

### Common Issues

1. **CUDA Out of Memory**
   - Reduce batch size in WGAN configuration
   - Use CPU configuration instead

2. **Slow Convergence**
   - Increase population size in GA configuration
   - Extend maximum generations
   - Use high-performance configuration

3. **Poor Quality Samples**
   - Adjust quality threshold in feedback configuration
   - Increase refinement iterations
   - Tune density estimation parameters

### Debug Mode
```bash
# Run with debug logging
python main_dag_wgan.py --dataset synthetic --log-level DEBUG
```

## Citation

If you use this code in your research, please cite:

```bibtex
@article{dag_wgan_2024,
  title={DAG-WGAN: Dynamic Density-Guided ADASYN-WGAN-GP with GA Co-Optimization for Imbalanced Classification},
  author={Research Team},
  journal={Journal Name},
  year={2024},
  note={Available at: https://github.com/your-repo}
}
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## Support

For questions and support:
- Open an issue on GitHub
- Check the troubleshooting section
- Review the configuration documentation

---

**Note**: This implementation represents a research-grade framework for addressing class imbalance through advanced density-guided synthesis and genetic algorithm optimization. The framework is designed for both academic research and practical applications in domains where minority class detection is critical.
