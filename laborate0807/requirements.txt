# DAG-WGAN Framework Requirements
# Core scientific computing
numpy>=1.21.0
scipy>=1.7.0
pandas>=1.3.0

# Machine learning
scikit-learn>=1.0.0
imbalanced-learn>=0.8.0

# Deep learning
torch>=1.9.0

# Visualization
matplotlib>=3.4.0
seaborn>=0.11.0

# Dimensionality reduction for t-SNE
scikit-learn>=1.0.0  # Already included above, but t-SNE is part of sklearn

# Statistical analysis
statsmodels>=0.12.0

# Data processing
tqdm>=4.62.0

# Utilities
joblib>=1.0.0

# Optional: GPU support (uncomment if using CUDA)
# torchvision>=0.10.0

# Development and testing (optional)
# pytest>=6.2.0
# pytest-cov>=2.12.0
# black>=21.0.0
# flake8>=3.9.0

# Documentation (optional)
# sphinx>=4.0.0
# sphinx-rtd-theme>=0.5.0
