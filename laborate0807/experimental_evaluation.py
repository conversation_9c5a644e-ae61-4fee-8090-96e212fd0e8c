"""
实验设置和评估模块

该模块实现了全面的评估系统，包含F1-Score、G-mean、AUC指标
以及DAG-WGAN框架的基线方法比较。

主要特性:
- 多指标评估(F1-Score, G-mean, AUC-ROC)
- 交叉验证框架
- 基线方法比较
- 统计显著性检验
- 可视化和报告

作者: 研究团队
日期: 2024-08-07
"""

import numpy as np
import pandas as pd
from sklearn.model_selection import StratifiedKFold, cross_val_score
from sklearn.ensemble import RandomForestClassifier
from sklearn.svm import SVC
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import (
    f1_score, precision_score, recall_score, roc_auc_score,
    confusion_matrix, classification_report, roc_curve, precision_recall_curve
)
from sklearn.preprocessing import StandardScaler
from imblearn.over_sampling import SMOTE, ADASYN, BorderlineSMOTE
from imblearn.combine import SMOTEENN, SMOTETomek
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from typing import Dict, List, Tuple, Optional, Any
import logging
import os
import json
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)


class MetricsCalculator:
    """
    Comprehensive metrics calculator for imbalanced classification evaluation.
    """
    
    @staticmethod
    def calculate_gmean(y_true: np.ndarray, y_pred: np.ndarray) -> float:
        """
        Calculate G-mean (Geometric Mean) for imbalanced classification.
        
        Args:
            y_true: True labels
            y_pred: Predicted labels
            
        Returns:
            G-mean score
        """
        tn, fp, fn, tp = confusion_matrix(y_true, y_pred).ravel()
        
        if tp + fn == 0 or tn + fp == 0:
            return 0.0
        
        sensitivity = tp / (tp + fn)  # Recall for positive class
        specificity = tn / (tn + fp)  # Recall for negative class
        
        return np.sqrt(sensitivity * specificity)
    
    @staticmethod
    def calculate_comprehensive_metrics(y_true: np.ndarray, y_pred: np.ndarray, 
                                      y_pred_proba: Optional[np.ndarray] = None) -> Dict[str, float]:
        """
        Calculate comprehensive evaluation metrics.
        
        Args:
            y_true: True labels
            y_pred: Predicted labels
            y_pred_proba: Predicted probabilities (optional)
            
        Returns:
            Dictionary of metrics
        """
        metrics = {}
        
        # Basic classification metrics
        metrics['f1_score'] = f1_score(y_true, y_pred)
        metrics['f1_macro'] = f1_score(y_true, y_pred, average='macro')
        metrics['f1_weighted'] = f1_score(y_true, y_pred, average='weighted')
        metrics['precision'] = precision_score(y_true, y_pred)
        metrics['recall'] = recall_score(y_true, y_pred)
        metrics['gmean'] = MetricsCalculator.calculate_gmean(y_true, y_pred)
        
        # Confusion matrix metrics
        tn, fp, fn, tp = confusion_matrix(y_true, y_pred).ravel()
        metrics['true_positive'] = tp
        metrics['true_negative'] = tn
        metrics['false_positive'] = fp
        metrics['false_negative'] = fn
        metrics['sensitivity'] = tp / (tp + fn) if (tp + fn) > 0 else 0.0
        metrics['specificity'] = tn / (tn + fp) if (tn + fp) > 0 else 0.0
        
        # AUC metrics (if probabilities available)
        if y_pred_proba is not None:
            try:
                metrics['auc_roc'] = roc_auc_score(y_true, y_pred_proba)
            except ValueError:
                metrics['auc_roc'] = 0.5  # Random classifier performance
        
        return metrics


class BaselineComparator:
    """
    Comparison with baseline oversampling methods.
    """
    
    def __init__(self, random_state: int = 42):
        """
        Initialize baseline comparator.
        
        Args:
            random_state: Random seed for reproducibility
        """
        self.random_state = random_state
        self.baseline_methods = {
            'SMOTE': SMOTE(random_state=random_state),
            'ADASYN': ADASYN(random_state=random_state),
            'BorderlineSMOTE': BorderlineSMOTE(random_state=random_state),
            'SMOTEENN': SMOTEENN(random_state=random_state),
            'SMOTETomek': SMOTETomek(random_state=random_state)
        }
        
    def evaluate_baseline_method(self, method_name: str, X_train: np.ndarray, y_train: np.ndarray,
                                X_test: np.ndarray, y_test: np.ndarray,
                                classifier_type: str = 'rf') -> Dict[str, Any]:
        """
        Evaluate a baseline oversampling method.
        
        Args:
            method_name: Name of the baseline method
            X_train: Training features
            y_train: Training labels
            X_test: Test features
            y_test: Test labels
            classifier_type: Type of classifier ('rf', 'svm', 'lr')
            
        Returns:
            Evaluation results dictionary
        """
        if method_name not in self.baseline_methods:
            raise ValueError(f"Unknown baseline method: {method_name}")
        
        try:
            # Apply oversampling
            oversampler = self.baseline_methods[method_name]
            X_resampled, y_resampled = oversampler.fit_resample(X_train, y_train)
            
            # Train classifier
            if classifier_type == 'rf':
                classifier = RandomForestClassifier(n_estimators=100, random_state=self.random_state)
            elif classifier_type == 'svm':
                classifier = SVC(probability=True, random_state=self.random_state)
            elif classifier_type == 'lr':
                classifier = LogisticRegression(random_state=self.random_state)
            else:
                raise ValueError(f"Unknown classifier type: {classifier_type}")
            
            classifier.fit(X_resampled, y_resampled)
            
            # Make predictions
            y_pred = classifier.predict(X_test)
            y_pred_proba = classifier.predict_proba(X_test)[:, 1] if hasattr(classifier, 'predict_proba') else None
            
            # Calculate metrics
            metrics = MetricsCalculator.calculate_comprehensive_metrics(y_test, y_pred, y_pred_proba)
            
            # Additional information
            results = {
                'method': method_name,
                'classifier': classifier_type,
                'original_samples': len(X_train),
                'resampled_samples': len(X_resampled),
                'sampling_ratio': len(X_resampled) / len(X_train),
                'metrics': metrics,
                'status': 'success'
            }
            
            return results
            
        except Exception as e:
            logger.error(f"Error evaluating {method_name}: {e}")
            return {
                'method': method_name,
                'classifier': classifier_type,
                'status': 'failed',
                'error': str(e)
            }
    
    def compare_all_baselines(self, X_train: np.ndarray, y_train: np.ndarray,
                            X_test: np.ndarray, y_test: np.ndarray,
                            classifier_types: List[str] = ['rf']) -> Dict[str, Any]:
        """
        Compare all baseline methods.
        
        Args:
            X_train: Training features
            y_train: Training labels
            X_test: Test features
            y_test: Test labels
            classifier_types: List of classifier types to test
            
        Returns:
            Comparison results dictionary
        """
        all_results = {}
        
        for method_name in self.baseline_methods.keys():
            method_results = {}
            
            for classifier_type in classifier_types:
                result = self.evaluate_baseline_method(
                    method_name, X_train, y_train, X_test, y_test, classifier_type
                )
                method_results[classifier_type] = result
            
            all_results[method_name] = method_results
        
        return all_results


class CrossValidationEvaluator:
    """
    Cross-validation evaluation framework for robust performance assessment.
    """
    
    def __init__(self, n_splits: int = 5, n_repeats: int = 3, random_state: int = 42):
        """
        Initialize cross-validation evaluator.
        
        Args:
            n_splits: Number of CV folds
            n_repeats: Number of CV repetitions
            random_state: Random seed
        """
        self.n_splits = n_splits
        self.n_repeats = n_repeats
        self.random_state = random_state
        
    def evaluate_with_cv(self, dag_wgan_framework, X: np.ndarray, y: np.ndarray,
                        classifier_type: str = 'rf') -> Dict[str, Any]:
        """
        Evaluate DAG-WGAN framework using cross-validation.
        
        Args:
            dag_wgan_framework: DAG-WGAN framework instance
            X: Features
            y: Labels
            classifier_type: Type of classifier
            
        Returns:
            Cross-validation results
        """
        cv_results = {
            'fold_results': [],
            'mean_metrics': {},
            'std_metrics': {},
            'all_metrics': []
        }
        
        # Perform repeated stratified k-fold CV
        for repeat in range(self.n_repeats):
            skf = StratifiedKFold(n_splits=self.n_splits, shuffle=True, 
                                random_state=self.random_state + repeat)
            
            for fold, (train_idx, test_idx) in enumerate(skf.split(X, y)):
                X_train, X_test = X[train_idx], X[test_idx]
                y_train, y_test = y[train_idx], y[test_idx]
                
                try:
                    # Train DAG-WGAN framework
                    dag_wgan_framework.fit(X_train, y_train)
                    
                    # Generate balanced dataset
                    X_balanced, y_balanced = dag_wgan_framework.generate_balanced_dataset(X_train, y_train)
                    
                    # Train classifier
                    if classifier_type == 'rf':
                        classifier = RandomForestClassifier(n_estimators=100, random_state=self.random_state)
                    elif classifier_type == 'svm':
                        classifier = SVC(probability=True, random_state=self.random_state)
                    elif classifier_type == 'lr':
                        classifier = LogisticRegression(random_state=self.random_state)
                    
                    classifier.fit(X_balanced, y_balanced)
                    
                    # Evaluate
                    y_pred = classifier.predict(X_test)
                    y_pred_proba = classifier.predict_proba(X_test)[:, 1]
                    
                    # Calculate metrics
                    metrics = MetricsCalculator.calculate_comprehensive_metrics(y_test, y_pred, y_pred_proba)
                    
                    fold_result = {
                        'repeat': repeat,
                        'fold': fold,
                        'metrics': metrics,
                        'train_size': len(X_train),
                        'test_size': len(X_test),
                        'balanced_size': len(X_balanced)
                    }
                    
                    cv_results['fold_results'].append(fold_result)
                    cv_results['all_metrics'].append(metrics)
                    
                except Exception as e:
                    logger.error(f"Error in CV fold {repeat}-{fold}: {e}")
                    continue
        
        # Calculate mean and std metrics
        if cv_results['all_metrics']:
            metric_names = cv_results['all_metrics'][0].keys()
            
            for metric_name in metric_names:
                values = [m[metric_name] for m in cv_results['all_metrics']]
                cv_results['mean_metrics'][metric_name] = np.mean(values)
                cv_results['std_metrics'][metric_name] = np.std(values)
        
        return cv_results


class StatisticalSignificanceTester:
    """
    Statistical significance testing for method comparisons.
    """
    
    @staticmethod
    def paired_t_test(results1: List[float], results2: List[float], 
                     alpha: float = 0.05) -> Dict[str, Any]:
        """
        Perform paired t-test between two sets of results.
        
        Args:
            results1: First set of results
            results2: Second set of results
            alpha: Significance level
            
        Returns:
            Test results dictionary
        """
        if len(results1) != len(results2):
            raise ValueError("Result sets must have the same length")
        
        # Perform paired t-test
        statistic, p_value = stats.ttest_rel(results1, results2)
        
        # Determine significance
        is_significant = p_value < alpha
        
        # Effect size (Cohen's d)
        differences = np.array(results1) - np.array(results2)
        effect_size = np.mean(differences) / np.std(differences) if np.std(differences) > 0 else 0.0
        
        return {
            'statistic': statistic,
            'p_value': p_value,
            'is_significant': is_significant,
            'alpha': alpha,
            'effect_size': effect_size,
            'mean_difference': np.mean(differences),
            'interpretation': 'significant' if is_significant else 'not significant'
        }
    
    @staticmethod
    def wilcoxon_signed_rank_test(results1: List[float], results2: List[float],
                                 alpha: float = 0.05) -> Dict[str, Any]:
        """
        Perform Wilcoxon signed-rank test (non-parametric alternative to paired t-test).
        
        Args:
            results1: First set of results
            results2: Second set of results
            alpha: Significance level
            
        Returns:
            Test results dictionary
        """
        if len(results1) != len(results2):
            raise ValueError("Result sets must have the same length")
        
        # Perform Wilcoxon signed-rank test
        statistic, p_value = stats.wilcoxon(results1, results2)
        
        # Determine significance
        is_significant = p_value < alpha
        
        return {
            'statistic': statistic,
            'p_value': p_value,
            'is_significant': is_significant,
            'alpha': alpha,
            'interpretation': 'significant' if is_significant else 'not significant'
        }


class ExperimentalEvaluator:
    """
    Main experimental evaluation coordinator.
    """
    
    def __init__(self, output_dir: str = 'results', random_state: int = 42):
        """
        Initialize experimental evaluator.
        
        Args:
            output_dir: Directory for saving results
            random_state: Random seed
        """
        self.output_dir = output_dir
        self.random_state = random_state
        self.metrics_calculator = MetricsCalculator()
        self.baseline_comparator = BaselineComparator(random_state)
        self.cv_evaluator = CrossValidationEvaluator(random_state=random_state)
        self.significance_tester = StatisticalSignificanceTester()
        
        # Create output directory
        os.makedirs(output_dir, exist_ok=True)
        
    def comprehensive_evaluation(self, dag_wgan_framework, X: np.ndarray, y: np.ndarray,
                               dataset_name: str = 'dataset') -> Dict[str, Any]:
        """
        Perform comprehensive evaluation of DAG-WGAN framework.
        
        Args:
            dag_wgan_framework: DAG-WGAN framework instance
            X: Features
            y: Labels
            dataset_name: Name of the dataset
            
        Returns:
            Comprehensive evaluation results
        """
        logger.info(f"Starting comprehensive evaluation for {dataset_name}")
        
        # Split data for evaluation
        from sklearn.model_selection import train_test_split
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.3, random_state=self.random_state, stratify=y
        )
        
        evaluation_results = {
            'dataset_name': dataset_name,
            'dataset_info': {
                'total_samples': len(X),
                'features': X.shape[1],
                'train_samples': len(X_train),
                'test_samples': len(X_test),
                'minority_ratio': np.sum(y) / len(y),
                'imbalance_ratio': np.sum(y == 0) / np.sum(y == 1)
            },
            'timestamp': datetime.now().isoformat()
        }
        
        # 1. DAG-WGAN evaluation
        logger.info("Evaluating DAG-WGAN framework...")
        try:
            dag_wgan_framework.fit(X_train, y_train)
            X_balanced, y_balanced = dag_wgan_framework.generate_balanced_dataset(X_train, y_train)
            
            # Train classifier on balanced data
            classifier = RandomForestClassifier(n_estimators=100, random_state=self.random_state)
            classifier.fit(X_balanced, y_balanced)
            
            # Evaluate
            y_pred = classifier.predict(X_test)
            y_pred_proba = classifier.predict_proba(X_test)[:, 1]
            
            dag_wgan_metrics = self.metrics_calculator.calculate_comprehensive_metrics(
                y_test, y_pred, y_pred_proba
            )
            
            evaluation_results['dag_wgan'] = {
                'metrics': dag_wgan_metrics,
                'balanced_samples': len(X_balanced),
                'status': 'success'
            }
            
        except Exception as e:
            logger.error(f"Error evaluating DAG-WGAN: {e}")
            evaluation_results['dag_wgan'] = {'status': 'failed', 'error': str(e)}
        
        # 2. Baseline comparisons
        logger.info("Evaluating baseline methods...")
        baseline_results = self.baseline_comparator.compare_all_baselines(
            X_train, y_train, X_test, y_test, ['rf']
        )
        evaluation_results['baselines'] = baseline_results
        
        # 3. Cross-validation evaluation
        logger.info("Performing cross-validation...")
        try:
            cv_results = self.cv_evaluator.evaluate_with_cv(dag_wgan_framework, X, y)
            evaluation_results['cross_validation'] = cv_results
        except Exception as e:
            logger.error(f"Error in cross-validation: {e}")
            evaluation_results['cross_validation'] = {'status': 'failed', 'error': str(e)}
        
        # 4. Statistical significance testing
        logger.info("Performing statistical significance tests...")
        if 'dag_wgan' in evaluation_results and evaluation_results['dag_wgan']['status'] == 'success':
            significance_results = {}
            
            # Compare with each baseline
            for method_name, method_results in baseline_results.items():
                if 'rf' in method_results and method_results['rf']['status'] == 'success':
                    try:
                        # Use F1-score for comparison
                        dag_f1 = evaluation_results['dag_wgan']['metrics']['f1_score']
                        baseline_f1 = method_results['rf']['metrics']['f1_score']
                        
                        # For single comparison, create artificial repeated measurements
                        # In practice, you would use CV results
                        dag_scores = [dag_f1] * 10  # Placeholder
                        baseline_scores = [baseline_f1] * 10  # Placeholder
                        
                        sig_test = self.significance_tester.paired_t_test(dag_scores, baseline_scores)
                        significance_results[method_name] = sig_test
                        
                    except Exception as e:
                        logger.warning(f"Error in significance test for {method_name}: {e}")
            
            evaluation_results['significance_tests'] = significance_results
        
        # Save results
        self.save_evaluation_results(evaluation_results, dataset_name)
        
        logger.info(f"Comprehensive evaluation completed for {dataset_name}")
        return evaluation_results
    
    def save_evaluation_results(self, results: Dict[str, Any], dataset_name: str):
        """
        Save evaluation results to files.
        
        Args:
            results: Evaluation results dictionary
            dataset_name: Name of the dataset
        """
        # Create dataset-specific directory
        dataset_dir = os.path.join(self.output_dir, dataset_name)
        os.makedirs(dataset_dir, exist_ok=True)
        
        # Save JSON results
        json_path = os.path.join(dataset_dir, 'evaluation_results.json')
        with open(json_path, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        logger.info(f"Results saved to {json_path}")
    
    def generate_comparison_report(self, results: Dict[str, Any]) -> str:
        """
        Generate a formatted comparison report.
        
        Args:
            results: Evaluation results
            
        Returns:
            Formatted report string
        """
        report = []
        report.append(f"=== DAG-WGAN Evaluation Report ===")
        report.append(f"Dataset: {results['dataset_name']}")
        report.append(f"Timestamp: {results['timestamp']}")
        report.append("")
        
        # Dataset info
        info = results['dataset_info']
        report.append("Dataset Information:")
        report.append(f"  Total samples: {info['total_samples']}")
        report.append(f"  Features: {info['features']}")
        report.append(f"  Minority ratio: {info['minority_ratio']:.3f}")
        report.append(f"  Imbalance ratio: {info['imbalance_ratio']:.1f}:1")
        report.append("")
        
        # DAG-WGAN results
        if 'dag_wgan' in results and results['dag_wgan']['status'] == 'success':
            metrics = results['dag_wgan']['metrics']
            report.append("DAG-WGAN Results:")
            report.append(f"  F1-Score: {metrics['f1_score']:.4f}")
            report.append(f"  G-mean: {metrics['gmean']:.4f}")
            report.append(f"  AUC-ROC: {metrics.get('auc_roc', 'N/A')}")
            report.append(f"  Precision: {metrics['precision']:.4f}")
            report.append(f"  Recall: {metrics['recall']:.4f}")
            report.append("")
        
        # Baseline comparisons
        if 'baselines' in results:
            report.append("Baseline Comparisons (F1-Score):")
            for method, method_results in results['baselines'].items():
                if 'rf' in method_results and method_results['rf']['status'] == 'success':
                    f1 = method_results['rf']['metrics']['f1_score']
                    report.append(f"  {method}: {f1:.4f}")
            report.append("")
        
        return "\n".join(report)
