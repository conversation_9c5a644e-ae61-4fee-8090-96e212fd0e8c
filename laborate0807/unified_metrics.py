"""
统一评估指标模块

该模块实现了论文5.2节中描述的评估指标:

核心指标:
1. 少数类F1分数: 少数类精确率与召回率的调和均值
2. 边界混淆指数(BCI): 衡量边界区域的分类模糊程度  
3. 高密度重叠率(HDOR): 高密度区域中真实样本与生成样本之间的海林格距离

辅助指标:
4. 训练时间: 框架完整执行的实际耗时
5. 损失波动方差: 生成器和判别器损失的标准差

作者: 研究团队
日期: 2024-08-07
"""

import numpy as np
import time
from typing import Dict, List, Tuple, Optional
from sklearn.metrics import f1_score, precision_score, recall_score, confusion_matrix
from sklearn.neighbors import NearestNeighbors
from sklearn.cluster import KMeans
from scipy.spatial.distance import cdist
from scipy.stats import entropy
import logging

logger = logging.getLogger(__name__)


class UnifiedMetricsCalculator:
    """
    统一评估指标计算器
    实现论文5.2节中的所有评估指标
    """
    
    def __init__(self, n_neighbors: int = 5, n_clusters: int = 3):
        """
        初始化指标计算器
        
        参数:
            n_neighbors: 边界分析的邻居数量
            n_clusters: 高密度区域聚类数量
        """
        self.n_neighbors = n_neighbors
        self.n_clusters = n_clusters
        
    def compute_minority_f1_score(self, y_true: np.ndarray, y_pred: np.ndarray) -> float:
        """
        计算少数类F1分数
        
        参数:
            y_true: 真实标签
            y_pred: 预测标签
            
        返回:
            少数类F1分数
        """
        # 假设少数类标签为1
        minority_class = 1
        f1 = f1_score(y_true, y_pred, pos_label=minority_class, average='binary')
        
        logger.info(f"少数类F1分数: {f1:.4f}")
        return f1
    
    def compute_boundary_confusion_index(self, X: np.ndarray, y_true: np.ndarray, 
                                       y_pred: np.ndarray, y_proba: Optional[np.ndarray] = None) -> float:
        """
        计算边界混淆指数(BCI)
        衡量边界区域的分类模糊程度
        
        参数:
            X: 特征数据
            y_true: 真实标签
            y_pred: 预测标签
            y_proba: 预测概率(可选)
            
        返回:
            边界混淆指数
        """
        # 识别边界样本
        boundary_samples = self._identify_boundary_samples(X, y_true)
        
        if len(boundary_samples) == 0:
            return 0.0
        
        # 计算边界区域的分类错误率
        boundary_y_true = y_true[boundary_samples]
        boundary_y_pred = y_pred[boundary_samples]
        
        # 基本混淆度: 错误分类率
        misclassification_rate = np.mean(boundary_y_true != boundary_y_pred)
        
        # 如果有概率信息，加入概率不确定性
        if y_proba is not None:
            boundary_proba = y_proba[boundary_samples]
            # 计算预测不确定性 (接近0.5的概率表示高不确定性)
            uncertainty = 1 - 2 * np.abs(boundary_proba - 0.5)
            avg_uncertainty = np.mean(uncertainty)
            
            # 综合混淆指数
            bci = 0.7 * misclassification_rate + 0.3 * avg_uncertainty
        else:
            bci = misclassification_rate
        
        logger.info(f"边界混淆指数(BCI): {bci:.4f}")
        return bci
    
    def _identify_boundary_samples(self, X: np.ndarray, y: np.ndarray) -> np.ndarray:
        """
        识别边界样本
        边界样本定义为在其k近邻中包含不同类别样本的样本
        
        参数:
            X: 特征数据
            y: 标签
            
        返回:
            边界样本的索引
        """
        nn = NearestNeighbors(n_neighbors=self.n_neighbors + 1)
        nn.fit(X)
        
        boundary_indices = []
        
        for i in range(len(X)):
            # 找到k+1个最近邻(包括自己)
            distances, indices = nn.kneighbors([X[i]])
            neighbor_indices = indices[0][1:]  # 排除自己
            neighbor_labels = y[neighbor_indices]
            
            # 如果邻居中有不同类别，则为边界样本
            if len(np.unique(neighbor_labels)) > 1:
                boundary_indices.append(i)
        
        return np.array(boundary_indices)
    
    def compute_high_density_overlap_rate(self, X_real: np.ndarray, X_generated: np.ndarray) -> float:
        """
        计算高密度重叠率(HDOR)
        使用海林格距离衡量高密度区域中真实样本与生成样本的重叠程度
        
        参数:
            X_real: 真实样本
            X_generated: 生成样本
            
        返回:
            高密度重叠率
        """
        if len(X_generated) == 0:
            return 0.0
        
        # 识别高密度区域
        high_density_real = self._identify_high_density_regions(X_real)
        high_density_generated = self._identify_high_density_regions(X_generated)
        
        # 计算海林格距离
        hellinger_distance = self._compute_hellinger_distance(
            high_density_real, high_density_generated
        )
        
        # 转换为重叠率 (距离越小，重叠率越高)
        hdor = 1.0 - hellinger_distance
        
        logger.info(f"高密度重叠率(HDOR): {hdor:.4f}")
        return hdor
    
    def _identify_high_density_regions(self, X: np.ndarray) -> np.ndarray:
        """
        识别高密度区域
        使用K-means聚类识别数据的高密度区域
        
        参数:
            X: 输入数据
            
        返回:
            高密度区域的代表点
        """
        if len(X) < self.n_clusters:
            return X
        
        # 使用K-means找到聚类中心
        kmeans = KMeans(n_clusters=self.n_clusters, random_state=42, n_init=10)
        kmeans.fit(X)
        
        # 计算每个聚类的密度(样本数量)
        cluster_labels = kmeans.labels_
        cluster_densities = []
        
        for i in range(self.n_clusters):
            cluster_size = np.sum(cluster_labels == i)
            cluster_densities.append(cluster_size)
        
        # 选择密度最高的聚类
        high_density_cluster = np.argmax(cluster_densities)
        high_density_samples = X[cluster_labels == high_density_cluster]
        
        return high_density_samples
    
    def _compute_hellinger_distance(self, X1: np.ndarray, X2: np.ndarray) -> float:
        """
        计算两个数据集之间的海林格距离
        
        参数:
            X1: 第一个数据集
            X2: 第二个数据集
            
        返回:
            海林格距离
        """
        if len(X1) == 0 or len(X2) == 0:
            return 1.0
        
        # 为每个特征维度计算直方图
        hellinger_distances = []
        
        for dim in range(X1.shape[1]):
            # 确定共同的bin范围
            min_val = min(X1[:, dim].min(), X2[:, dim].min())
            max_val = max(X1[:, dim].max(), X2[:, dim].max())
            
            if max_val == min_val:
                hellinger_distances.append(0.0)
                continue
            
            bins = np.linspace(min_val, max_val, 20)
            
            # 计算归一化直方图
            hist1, _ = np.histogram(X1[:, dim], bins=bins, density=True)
            hist2, _ = np.histogram(X2[:, dim], bins=bins, density=True)
            
            # 归一化为概率分布
            hist1 = hist1 / (np.sum(hist1) + 1e-10)
            hist2 = hist2 / (np.sum(hist2) + 1e-10)
            
            # 计算海林格距离
            hellinger_dist = np.sqrt(0.5 * np.sum((np.sqrt(hist1) - np.sqrt(hist2)) ** 2))
            hellinger_distances.append(hellinger_dist)
        
        # 返回平均海林格距离
        return np.mean(hellinger_distances)
    
    def compute_training_time(self, start_time: float, end_time: float) -> float:
        """
        计算训练时间
        
        参数:
            start_time: 开始时间戳
            end_time: 结束时间戳
            
        返回:
            训练时间(秒)
        """
        training_time = end_time - start_time
        logger.info(f"训练时间: {training_time:.2f}秒")
        return training_time
    
    def compute_loss_fluctuation_variance(self, generator_losses: List[float], 
                                        discriminator_losses: List[float]) -> Dict[str, float]:
        """
        计算损失波动方差
        
        参数:
            generator_losses: 生成器损失历史
            discriminator_losses: 判别器损失历史
            
        返回:
            包含各种方差指标的字典
        """
        results = {}
        
        if len(generator_losses) > 1:
            g_variance = np.var(generator_losses)
            g_std = np.std(generator_losses)
            results['generator_loss_variance'] = g_variance
            results['generator_loss_std'] = g_std
        else:
            results['generator_loss_variance'] = 0.0
            results['generator_loss_std'] = 0.0
        
        if len(discriminator_losses) > 1:
            d_variance = np.var(discriminator_losses)
            d_std = np.std(discriminator_losses)
            results['discriminator_loss_variance'] = d_variance
            results['discriminator_loss_std'] = d_std
        else:
            results['discriminator_loss_variance'] = 0.0
            results['discriminator_loss_std'] = 0.0
        
        # 综合损失波动方差
        total_variance = results['generator_loss_variance'] + results['discriminator_loss_variance']
        results['total_loss_variance'] = total_variance
        
        logger.info(f"生成器损失方差: {results['generator_loss_variance']:.6f}")
        logger.info(f"判别器损失方差: {results['discriminator_loss_variance']:.6f}")
        logger.info(f"总损失波动方差: {total_variance:.6f}")
        
        return results
    
    def compute_comprehensive_metrics(self, X_real: np.ndarray, X_generated: np.ndarray,
                                    y_true: np.ndarray, y_pred: np.ndarray,
                                    y_proba: Optional[np.ndarray] = None,
                                    generator_losses: Optional[List[float]] = None,
                                    discriminator_losses: Optional[List[float]] = None,
                                    training_time: Optional[float] = None) -> Dict[str, float]:
        """
        计算所有评估指标的综合结果
        
        参数:
            X_real: 真实样本
            X_generated: 生成样本
            y_true: 真实标签
            y_pred: 预测标签
            y_proba: 预测概率
            generator_losses: 生成器损失历史
            discriminator_losses: 判别器损失历史
            training_time: 训练时间
            
        返回:
            包含所有指标的字典
        """
        metrics = {}
        
        # 核心指标
        metrics['minority_f1_score'] = self.compute_minority_f1_score(y_true, y_pred)
        metrics['boundary_confusion_index'] = self.compute_boundary_confusion_index(
            X_real, y_true, y_pred, y_proba
        )
        metrics['high_density_overlap_rate'] = self.compute_high_density_overlap_rate(
            X_real, X_generated
        )
        
        # 辅助指标
        if training_time is not None:
            metrics['training_time'] = training_time
        
        if generator_losses is not None and discriminator_losses is not None:
            loss_metrics = self.compute_loss_fluctuation_variance(
                generator_losses, discriminator_losses
            )
            metrics.update(loss_metrics)
        
        # 额外的标准指标
        metrics['precision'] = precision_score(y_true, y_pred, pos_label=1, average='binary')
        metrics['recall'] = recall_score(y_true, y_pred, pos_label=1, average='binary')
        
        # 计算G-mean
        tn, fp, fn, tp = confusion_matrix(y_true, y_pred).ravel()
        sensitivity = tp / (tp + fn) if (tp + fn) > 0 else 0
        specificity = tn / (tn + fp) if (tn + fp) > 0 else 0
        metrics['gmean'] = np.sqrt(sensitivity * specificity)
        
        return metrics


class MetricsTracker:
    """
    指标跟踪器
    用于在训练过程中跟踪和记录各种指标
    """
    
    def __init__(self):
        """初始化指标跟踪器"""
        self.metrics_history = {}
        self.start_time = None
        
    def start_timing(self):
        """开始计时"""
        self.start_time = time.time()
        
    def stop_timing(self) -> float:
        """停止计时并返回耗时"""
        if self.start_time is None:
            return 0.0
        end_time = time.time()
        elapsed = end_time - self.start_time
        self.start_time = None
        return elapsed
        
    def record_metric(self, name: str, value: float):
        """记录单个指标"""
        if name not in self.metrics_history:
            self.metrics_history[name] = []
        self.metrics_history[name].append(value)
        
    def record_metrics(self, metrics_dict: Dict[str, float]):
        """记录多个指标"""
        for name, value in metrics_dict.items():
            self.record_metric(name, value)
            
    def get_metric_history(self, name: str) -> List[float]:
        """获取指标历史"""
        return self.metrics_history.get(name, [])
        
    def get_latest_metric(self, name: str) -> Optional[float]:
        """获取最新指标值"""
        history = self.get_metric_history(name)
        return history[-1] if history else None
        
    def compute_metric_statistics(self, name: str) -> Dict[str, float]:
        """计算指标统计信息"""
        history = self.get_metric_history(name)
        if not history:
            return {}
            
        return {
            'mean': np.mean(history),
            'std': np.std(history),
            'min': np.min(history),
            'max': np.max(history),
            'latest': history[-1]
        }
