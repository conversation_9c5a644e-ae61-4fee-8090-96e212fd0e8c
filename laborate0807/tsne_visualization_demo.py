"""
Standalone t-SNE Visualization Demo for DAG-WGAN

This script demonstrates t-SNE visualization comparing:
- Real majority samples
- Real minority samples  
- ADASYN generated samples
- WGAN generated samples
- DAG-WGAN combined samples

Author: Research Team
Date: 2024-08-07
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend
from sklearn.datasets import make_classification
from sklearn.model_selection import train_test_split
from sklearn.manifold import TSNE
import logging
import os
import warnings
warnings.filterwarnings('ignore')

# Import DAG-WGAN components
from config import get_fast_config
from dag_wgan_framework import DAGWGANFramework
from main_dag_wgan import TSNEVisualizer

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def create_imbalanced_dataset(n_samples=2000, imbalance_ratio=0.1, random_state=42):
    """Create a synthetic imbalanced dataset for visualization."""
    weights = [1 - imbalance_ratio, imbalance_ratio]
    
    X, y = make_classification(
        n_samples=n_samples,
        n_features=10,
        n_informative=8,
        n_redundant=2,
        n_clusters_per_class=2,  # More clusters for interesting visualization
        weights=weights,
        flip_y=0.01,
        random_state=random_state
    )
    
    return X, y


def main():
    """Main t-SNE visualization demo."""
    print("=== DAG-WGAN t-SNE Visualization Demo ===\n")
    
    # Create output directory
    output_dir = "tsne_visualizations"
    os.makedirs(output_dir, exist_ok=True)
    
    # 1. Create synthetic imbalanced dataset
    logger.info("Creating synthetic imbalanced dataset...")
    X, y = create_imbalanced_dataset(n_samples=2000, imbalance_ratio=0.08)  # More imbalanced
    
    print(f"Dataset created:")
    print(f"  Total samples: {len(X)}")
    print(f"  Features: {X.shape[1]}")
    print(f"  Class distribution: {np.bincount(y)}")
    print(f"  Imbalance ratio: {np.sum(y==0)/np.sum(y==1):.1f}:1")
    
    # 2. Split data
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.3, random_state=42, stratify=y
    )
    
    print(f"\nData split:")
    print(f"  Training set: {len(X_train)} samples")
    print(f"  Test set: {len(X_test)} samples")
    
    # 3. Initialize DAG-WGAN framework
    logger.info("Initializing DAG-WGAN framework...")
    config = get_fast_config()
    
    framework_config = {
        'alpha': config.density.alpha,
        'k_neighbors': config.density.k_neighbors,
        'beta': config.synthesis.beta,
        'lr_g': config.wgan.eta_g,
        'lr_d': config.wgan.eta_d,
        'lambda_gp': config.wgan.lambda_gp,
        'n_critic': config.wgan.n_critic,
        'device': config.wgan.device,
        'latent_dim': config.wgan.latent_dim,
        'nu': config.wgan.nu,
        'tau': config.wgan.tau
    }
    
    dag_wgan = DAGWGANFramework(framework_config)
    
    try:
        # 4. Train DAG-WGAN
        logger.info("Training DAG-WGAN framework...")
        training_result = dag_wgan.fit(X_train, y_train, epochs=50)
        
        if training_result['status'] == 'success':
            print(f"\nDAG-WGAN training completed successfully!")
            print(f"  ADASYN samples generated: {training_result['history']['adasyn_samples']}")
            
            # 5. Generate samples for visualization
            logger.info("Generating samples for t-SNE visualization...")
            
            # Generate ADASYN samples only
            X_adasyn = dag_wgan.synthesis_layer.density_guided_adasyn(X_train, y_train)
            
            # Generate WGAN samples only
            n_wgan_samples = min(len(X_adasyn), 300)  # Limit for better visualization
            X_wgan = dag_wgan.wgan.generate_samples(n_wgan_samples)
            
            print(f"\nSample generation summary:")
            print(f"  ADASYN samples: {len(X_adasyn)}")
            print(f"  WGAN samples: {len(X_wgan)}")
            
            # 6. Create t-SNE visualizations
            logger.info("Creating t-SNE visualizations...")
            visualizer = TSNEVisualizer(random_state=42, figsize=(16, 12))
            
            # Main t-SNE comparison
            tsne_path = os.path.join(output_dir, 'dag_wgan_tsne_comparison.png')
            fig_tsne = visualizer.create_tsne_visualization(
                X_train, y_train, X_adasyn, X_wgan,
                title='DAG-WGAN t-SNE Visualization: Real vs Generated Samples',
                save_path=tsne_path
            )
            
            # Distribution analysis
            dist_path = os.path.join(output_dir, 'feature_distribution_analysis.png')
            fig_dist = visualizer.create_distribution_analysis(
                X_train, y_train, X_adasyn, X_wgan,
                save_path=dist_path
            )
            
            # 7. Create additional detailed visualizations
            logger.info("Creating detailed comparison visualizations...")
            
            # Separate visualization for each comparison
            create_detailed_comparisons(X_train, y_train, X_adasyn, X_wgan, output_dir)
            
            # 8. Generate comprehensive statistics
            stats_path = os.path.join(output_dir, 'visualization_statistics.txt')
            generate_detailed_statistics(X_train, y_train, X_adasyn, X_wgan, stats_path)
            
            print(f"\n🎉 t-SNE visualizations completed successfully!")
            print(f"📊 All visualizations saved to: {output_dir}")
            print(f"\nGenerated files:")
            print(f"  - dag_wgan_tsne_comparison.png: Main t-SNE comparison")
            print(f"  - feature_distribution_analysis.png: Feature distributions")
            print(f"  - detailed_comparisons/: Individual comparison plots")
            print(f"  - visualization_statistics.txt: Detailed statistics")
            
        else:
            print(f"DAG-WGAN training failed: {training_result.get('reason', 'Unknown error')}")
            
    except Exception as e:
        logger.error(f"Error during visualization demo: {e}")
        import traceback
        traceback.print_exc()


def create_detailed_comparisons(X_train, y_train, X_adasyn, X_wgan, output_dir):
    """Create detailed individual comparison plots."""
    detail_dir = os.path.join(output_dir, 'detailed_comparisons')
    os.makedirs(detail_dir, exist_ok=True)
    
    X_minority = X_train[y_train == 1]
    X_majority = X_train[y_train == 0]
    
    # Prepare data for t-SNE
    max_samples_per_type = 400  # Limit for better visualization
    
    def sample_data(X, max_size):
        if len(X) > max_size:
            indices = np.random.choice(len(X), max_size, replace=False)
            return X[indices]
        return X
    
    X_minority_sampled = sample_data(X_minority, max_samples_per_type)
    X_majority_sampled = sample_data(X_majority, max_samples_per_type)
    X_adasyn_sampled = sample_data(X_adasyn, max_samples_per_type)
    X_wgan_sampled = sample_data(X_wgan, max_samples_per_type)
    
    # Individual comparison plots
    comparisons = [
        ("real_vs_adasyn", X_minority_sampled, X_adasyn_sampled, "Real Minority", "ADASYN Generated"),
        ("real_vs_wgan", X_minority_sampled, X_wgan_sampled, "Real Minority", "WGAN Generated"),
        ("adasyn_vs_wgan", X_adasyn_sampled, X_wgan_sampled, "ADASYN Generated", "WGAN Generated"),
        ("majority_vs_minority", X_majority_sampled, X_minority_sampled, "Real Majority", "Real Minority")
    ]
    
    for comp_name, X1, X2, label1, label2 in comparisons:
        if len(X1) > 0 and len(X2) > 0:
            # Combine data
            X_combined = np.vstack([X1, X2])
            labels = np.hstack([np.zeros(len(X1)), np.ones(len(X2))])
            
            # Apply t-SNE
            tsne = TSNE(n_components=2, random_state=42, perplexity=min(30, len(X_combined)//4))
            X_tsne = tsne.fit_transform(X_combined)
            
            # Create plot
            plt.figure(figsize=(10, 8))
            
            # Plot first group
            mask1 = labels == 0
            plt.scatter(X_tsne[mask1, 0], X_tsne[mask1, 1], 
                       c='blue', alpha=0.7, s=30, label=label1)
            
            # Plot second group
            mask2 = labels == 1
            plt.scatter(X_tsne[mask2, 0], X_tsne[mask2, 1], 
                       c='red', alpha=0.7, s=30, label=label2)
            
            plt.title(f't-SNE Comparison: {label1} vs {label2}', fontsize=14, fontweight='bold')
            plt.xlabel('t-SNE Component 1')
            plt.ylabel('t-SNE Component 2')
            plt.legend()
            plt.grid(True, alpha=0.3)
            
            # Save plot
            save_path = os.path.join(detail_dir, f'{comp_name}_tsne.png')
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            print(f"  ✓ Saved: {comp_name}_tsne.png")


def generate_detailed_statistics(X_train, y_train, X_adasyn, X_wgan, save_path):
    """Generate detailed statistics about the visualization."""
    X_minority = X_train[y_train == 1]
    X_majority = X_train[y_train == 0]
    
    stats = []
    stats.append("=== DAG-WGAN t-SNE Visualization Statistics ===\n")
    stats.append(f"Generated on: {np.datetime64('now')}\n")
    
    # Dataset statistics
    stats.append("Dataset Statistics:")
    stats.append(f"  Total training samples: {len(X_train)}")
    stats.append(f"  Features: {X_train.shape[1]}")
    stats.append(f"  Majority class: {len(X_majority)} ({len(X_majority)/len(X_train)*100:.1f}%)")
    stats.append(f"  Minority class: {len(X_minority)} ({len(X_minority)/len(X_train)*100:.1f}%)")
    stats.append(f"  Imbalance ratio: {len(X_majority)/len(X_minority):.2f}:1\n")
    
    # Generation statistics
    stats.append("Sample Generation:")
    stats.append(f"  ADASYN samples: {len(X_adasyn)}")
    stats.append(f"  WGAN samples: {len(X_wgan)}")
    stats.append(f"  Total synthetic: {len(X_adasyn) + len(X_wgan)}")
    stats.append(f"  Augmentation ratio: {(len(X_adasyn) + len(X_wgan))/len(X_minority):.2f}x\n")
    
    # Quality metrics
    if len(X_adasyn) > 0 and len(X_minority) > 0:
        from scipy.spatial.distance import cdist
        
        # ADASYN quality
        distances_adasyn = cdist(X_adasyn, X_minority, metric='euclidean')
        min_dist_adasyn = np.min(distances_adasyn, axis=1)
        
        stats.append("ADASYN Quality Metrics:")
        stats.append(f"  Mean distance to nearest real sample: {np.mean(min_dist_adasyn):.4f}")
        stats.append(f"  Std distance to nearest real sample: {np.std(min_dist_adasyn):.4f}")
        stats.append(f"  Min distance: {np.min(min_dist_adasyn):.4f}")
        stats.append(f"  Max distance: {np.max(min_dist_adasyn):.4f}\n")
    
    if len(X_wgan) > 0 and len(X_minority) > 0:
        # WGAN quality
        distances_wgan = cdist(X_wgan, X_minority, metric='euclidean')
        min_dist_wgan = np.min(distances_wgan, axis=1)
        
        stats.append("WGAN Quality Metrics:")
        stats.append(f"  Mean distance to nearest real sample: {np.mean(min_dist_wgan):.4f}")
        stats.append(f"  Std distance to nearest real sample: {np.std(min_dist_wgan):.4f}")
        stats.append(f"  Min distance: {np.min(min_dist_wgan):.4f}")
        stats.append(f"  Max distance: {np.max(min_dist_wgan):.4f}\n")
    
    # Feature statistics
    stats.append("Feature Statistics (first 5 features):")
    stats.append("Real Minority:")
    stats.append(f"  Mean: {np.mean(X_minority, axis=0)[:5]}")
    stats.append(f"  Std:  {np.std(X_minority, axis=0)[:5]}")
    
    if len(X_adasyn) > 0:
        stats.append("ADASYN Generated:")
        stats.append(f"  Mean: {np.mean(X_adasyn, axis=0)[:5]}")
        stats.append(f"  Std:  {np.std(X_adasyn, axis=0)[:5]}")
    
    if len(X_wgan) > 0:
        stats.append("WGAN Generated:")
        stats.append(f"  Mean: {np.mean(X_wgan, axis=0)[:5]}")
        stats.append(f"  Std:  {np.std(X_wgan, axis=0)[:5]}")
    
    # Save statistics
    with open(save_path, 'w') as f:
        f.write('\n'.join(stats))
    
    print(f"  ✓ Saved: visualization_statistics.txt")


if __name__ == '__main__':
    main()
