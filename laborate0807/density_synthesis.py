"""
Density-Guided Synthesis-Generation Synergy Module

This module implements the advanced density estimation and synthesis generation
components of the DAG-WGAN framework, including KDE-based density computation,
weighted ADASYN interpolation, and density-weighted gradient penalty mechanisms.

Author: Research Team
Date: 2024-08-07
"""

import numpy as np
import torch
import torch.nn as nn
from sklearn.neighbors import NearestNeighbors, KernelDensity
from sklearn.preprocessing import StandardScaler
from scipy.stats import gaussian_kde
from scipy.spatial.distance import cdist
from scipy.stats import wasserstein_distance
import logging
from typing import Dict, List, Tuple, Optional, Union
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)


class AdvancedDensityEstimator:
    """
    Advanced density estimation using multiple methods for robust density computation.
    Implements KDE with adaptive bandwidth selection and boundary correction.
    """
    
    def __init__(self, method: str = 'kde', bandwidth: str = 'scott'):
        """
        Initialize density estimator.
        
        Args:
            method: Density estimation method ('kde', 'knn', 'hybrid')
            bandwidth: Bandwidth selection method ('scott', 'silverman', 'adaptive')
        """
        self.method = method
        self.bandwidth = bandwidth
        self.kde_model = None
        self.scaler = StandardScaler()
        self.fitted = False
        
    def fit(self, X: np.ndarray) -> 'AdvancedDensityEstimator':
        """
        Fit the density estimator to data.
        
        Args:
            X: Training data
            
        Returns:
            Self for method chaining
        """
        # Standardize data
        X_scaled = self.scaler.fit_transform(X)
        
        if self.method == 'kde':
            self.kde_model = gaussian_kde(X_scaled.T, bw_method=self.bandwidth)
        elif self.method == 'sklearn_kde':
            # Use sklearn's KernelDensity for more control
            if self.bandwidth == 'scott':
                bw = len(X) ** (-1.0 / (X.shape[1] + 4))
            elif self.bandwidth == 'silverman':
                bw = (len(X) * (X.shape[1] + 2) / 4.0) ** (-1.0 / (X.shape[1] + 4))
            else:
                bw = 1.0
                
            self.kde_model = KernelDensity(kernel='gaussian', bandwidth=bw)
            self.kde_model.fit(X_scaled)
        
        self.fitted = True
        logger.info(f"Density estimator fitted with method: {self.method}")
        return self
    
    def compute_density(self, X: np.ndarray) -> np.ndarray:
        """
        Compute density values for given points.
        
        Args:
            X: Points to compute density for
            
        Returns:
            Density values
        """
        if not self.fitted:
            raise ValueError("Density estimator must be fitted first")
        
        X_scaled = self.scaler.transform(X)
        
        if self.method == 'kde':
            densities = self.kde_model(X_scaled.T)
        elif self.method == 'sklearn_kde':
            log_densities = self.kde_model.score_samples(X_scaled)
            densities = np.exp(log_densities)
        else:
            # Fallback to simple KDE
            densities = gaussian_kde(X_scaled.T)(X_scaled.T)
        
        return densities
    
    def adaptive_bandwidth_selection(self, X: np.ndarray) -> float:
        """
        Select adaptive bandwidth based on local data density.
        
        Args:
            X: Data points
            
        Returns:
            Optimal bandwidth
        """
        n, d = X.shape
        
        # Scott's rule with adaptive correction
        scott_bw = n ** (-1.0 / (d + 4))
        
        # Compute local density variation
        nn_model = NearestNeighbors(n_neighbors=min(10, n-1))
        nn_model.fit(X)
        distances, _ = nn_model.kneighbors(X)
        
        # Adaptive factor based on local density variation
        local_density_var = np.var(distances.mean(axis=1))
        adaptive_factor = 1.0 + 0.5 * local_density_var
        
        return scott_bw * adaptive_factor


class WeightedADASYNGenerator:
    """
    Enhanced ADASYN generator with density-weighted interpolation.
    Implements Equation (9) from the research paper.
    """
    
    def __init__(self, density_estimator: AdvancedDensityEstimator, 
                 k_neighbors: int = 5, random_state: int = 42):
        """
        Initialize weighted ADASYN generator.
        
        Args:
            density_estimator: Fitted density estimator
            k_neighbors: Number of nearest neighbors
            random_state: Random seed for reproducibility
        """
        self.density_estimator = density_estimator
        self.k_neighbors = k_neighbors
        self.random_state = random_state
        np.random.seed(random_state)
        
    def compute_difficulty_weights(self, X_minority: np.ndarray, 
                                 X_majority: np.ndarray) -> np.ndarray:
        """
        Compute difficulty weights for minority samples based on local neighborhood.
        
        Args:
            X_minority: Minority class samples
            X_majority: Majority class samples
            
        Returns:
            Difficulty weights for each minority sample
        """
        X_combined = np.vstack([X_minority, X_majority])
        y_combined = np.hstack([np.ones(len(X_minority)), np.zeros(len(X_majority))])
        
        # Fit nearest neighbors on combined data
        nn_model = NearestNeighbors(n_neighbors=self.k_neighbors + 1)
        nn_model.fit(X_combined)
        
        difficulty_weights = []
        
        for i, x_i in enumerate(X_minority):
            # Find k+1 nearest neighbors (including self)
            distances, indices = nn_model.kneighbors([x_i])
            neighbor_labels = y_combined[indices[0][1:]]  # Exclude self
            
            # Difficulty is proportion of majority class neighbors
            difficulty = 1.0 - neighbor_labels.mean()
            difficulty_weights.append(difficulty)
        
        return np.array(difficulty_weights)
    
    def density_weighted_interpolation(self, x_i: np.ndarray, x_j: np.ndarray,
                                     density_weight: float, lambda_param: float) -> np.ndarray:
        """
        Perform density-weighted interpolation as in Equation (9).
        
        Args:
            x_i: First sample point
            x_j: Second sample point (neighbor)
            density_weight: Density weight for the interpolation
            lambda_param: Interpolation parameter
            
        Returns:
            Synthetic sample
        """
        # Random interpolation factor
        lambda_val = np.random.uniform(0, lambda_param)
        
        # Density-weighted interpolation (Equation 9)
        x_syn = x_i + lambda_val * (x_j - x_i) * density_weight
        
        return x_syn
    
    def generate_samples(self, X_minority: np.ndarray, X_majority: np.ndarray,
                        alpha: float = 0.8, lambda_param: float = 0.5) -> np.ndarray:
        """
        Generate synthetic minority samples using density-weighted ADASYN.
        
        Args:
            X_minority: Minority class samples
            X_majority: Majority class samples
            alpha: Balance parameter (proportion of imbalance to address)
            lambda_param: Interpolation strength parameter
            
        Returns:
            Generated synthetic samples
        """
        if len(X_minority) < 2:
            logger.warning("Insufficient minority samples for ADASYN generation")
            return np.empty((0, X_minority.shape[1]))
        
        # Compute density weights
        densities = self.density_estimator.compute_density(X_minority)
        # Invert densities to focus on low-density regions
        density_weights = 1.0 / (densities + 1e-8)
        density_weights = density_weights / density_weights.sum()
        
        # Compute difficulty weights
        difficulty_weights = self.compute_difficulty_weights(X_minority, X_majority)
        
        # Combine density and difficulty weights
        combined_weights = density_weights * difficulty_weights
        combined_weights = combined_weights / (combined_weights.sum() + 1e-8)
        
        # Calculate number of samples to generate
        n_majority = len(X_majority)
        n_minority = len(X_minority)
        n_generate = int(alpha * (n_majority - n_minority))
        
        if n_generate <= 0:
            logger.info("No samples needed for generation")
            return np.empty((0, X_minority.shape[1]))
        
        # Fit nearest neighbors on minority samples
        k_actual = min(self.k_neighbors, len(X_minority) - 1)
        nn_model = NearestNeighbors(n_neighbors=k_actual + 1)
        nn_model.fit(X_minority)
        
        synthetic_samples = []
        
        for _ in range(n_generate):
            # Select sample based on combined weights
            if combined_weights.sum() > 0:
                sample_idx = np.random.choice(len(X_minority), p=combined_weights)
            else:
                sample_idx = np.random.choice(len(X_minority))
            
            x_i = X_minority[sample_idx]
            
            # Find nearest neighbors
            distances, indices = nn_model.kneighbors([x_i])
            neighbor_indices = indices[0][1:]  # Exclude self
            
            if len(neighbor_indices) > 0:
                # Select random neighbor
                neighbor_idx = np.random.choice(neighbor_indices)
                x_j = X_minority[neighbor_idx]
                
                # Generate synthetic sample with density weighting
                x_syn = self.density_weighted_interpolation(
                    x_i, x_j, combined_weights[sample_idx], lambda_param
                )
                
                synthetic_samples.append(x_syn)
        
        logger.info(f"Generated {len(synthetic_samples)} synthetic samples using weighted ADASYN")
        return np.array(synthetic_samples) if synthetic_samples else np.empty((0, X_minority.shape[1]))


class DensityGuidedWGANGP:
    """
    WGAN-GP with density-guided gradient penalty and enhanced training stability.
    Implements Equation (10) from the research paper.
    """
    
    def __init__(self, input_dim: int, latent_dim: int = 100,
                 generator_dims: List[int] = [128, 256, 128],
                 discriminator_dims: List[int] = [128, 256, 128],
                 lr_g: float = 1e-4, lr_d: float = 1e-4,
                 lambda_gp: float = 10.0, beta1: float = 0.0, beta2: float = 0.9,
                 device: str = 'cpu'):
        """
        Initialize density-guided WGAN-GP.
        
        Args:
            input_dim: Input feature dimension
            latent_dim: Latent space dimension
            generator_dims: Generator hidden layer dimensions
            discriminator_dims: Discriminator hidden layer dimensions
            lr_g: Generator learning rate
            lr_d: Discriminator learning rate
            lambda_gp: Gradient penalty coefficient
            beta1, beta2: Adam optimizer parameters
            device: Computing device
        """
        self.input_dim = input_dim
        self.latent_dim = latent_dim
        self.lambda_gp = lambda_gp
        self.device = torch.device(device)
        
        # Build networks
        self.generator = self._build_generator(generator_dims).to(self.device)
        self.discriminator = self._build_discriminator(discriminator_dims).to(self.device)
        
        # Initialize optimizers
        self.optimizer_g = torch.optim.Adam(
            self.generator.parameters(), lr=lr_g, betas=(beta1, beta2)
        )
        self.optimizer_d = torch.optim.Adam(
            self.discriminator.parameters(), lr=lr_d, betas=(beta1, beta2)
        )
        
        # Training statistics
        self.training_stats = {
            'g_losses': [],
            'd_losses': [],
            'w_distances': [],
            'gradient_penalties': []
        }
        
    def _build_generator(self, hidden_dims: List[int]) -> nn.Module:
        """Build generator network with configurable architecture."""
        layers = []
        prev_dim = self.latent_dim
        
        for dim in hidden_dims:
            layers.extend([
                nn.Linear(prev_dim, dim),
                nn.BatchNorm1d(dim),
                nn.ReLU(inplace=True)
            ])
            prev_dim = dim
        
        # Output layer
        layers.extend([
            nn.Linear(prev_dim, self.input_dim),
            nn.Tanh()
        ])
        
        return nn.Sequential(*layers)
    
    def _build_discriminator(self, hidden_dims: List[int]) -> nn.Module:
        """Build discriminator network with configurable architecture."""
        layers = []
        prev_dim = self.input_dim
        
        for dim in hidden_dims:
            layers.extend([
                nn.Linear(prev_dim, dim),
                nn.LeakyReLU(0.2, inplace=True),
                nn.Dropout(0.3)
            ])
            prev_dim = dim
        
        # Output layer (no activation for WGAN)
        layers.append(nn.Linear(prev_dim, 1))
        
        return nn.Sequential(*layers)
    
    def compute_density_weighted_gradient_penalty(self, real_data: torch.Tensor,
                                                fake_data: torch.Tensor,
                                                density_weights: torch.Tensor) -> torch.Tensor:
        """
        Compute density-weighted gradient penalty as in Equation (10).
        
        Args:
            real_data: Real data batch
            fake_data: Generated fake data batch
            density_weights: Density weights for gradient penalty weighting
            
        Returns:
            Density-weighted gradient penalty
        """
        batch_size = real_data.size(0)
        
        # Random interpolation coefficients
        alpha = torch.rand(batch_size, 1).to(self.device)
        alpha = alpha.expand_as(real_data)
        
        # Interpolated samples
        interpolated = alpha * real_data + (1 - alpha) * fake_data
        interpolated.requires_grad_(True)
        
        # Discriminator output for interpolated samples
        d_interpolated = self.discriminator(interpolated)
        
        # Compute gradients w.r.t. interpolated samples
        gradients = torch.autograd.grad(
            outputs=d_interpolated,
            inputs=interpolated,
            grad_outputs=torch.ones_like(d_interpolated),
            create_graph=True,
            retain_graph=True,
            only_inputs=True
        )[0]
        
        # Gradient penalty with density weighting (Equation 10)
        gradient_norm = gradients.norm(2, dim=1)
        
        # Apply density weighting
        if density_weights is not None:
            density_weights = density_weights.view(-1)
            penalty = density_weights * (gradient_norm - 1) ** 2
        else:
            penalty = (gradient_norm - 1) ** 2
        
        return penalty.mean()
    
    def train_discriminator(self, real_data: torch.Tensor, 
                          density_weights: Optional[torch.Tensor] = None) -> Tuple[float, float, float]:
        """
        Train discriminator for one step.
        
        Args:
            real_data: Real data batch
            density_weights: Optional density weights
            
        Returns:
            Tuple of (discriminator_loss, wasserstein_distance, gradient_penalty)
        """
        self.optimizer_d.zero_grad()
        
        batch_size = real_data.size(0)
        
        # Real data discriminator output
        d_real = self.discriminator(real_data)
        
        # Generate fake data
        z = torch.randn(batch_size, self.latent_dim).to(self.device)
        fake_data = self.generator(z).detach()
        d_fake = self.discriminator(fake_data)
        
        # Wasserstein distance
        w_distance = d_real.mean() - d_fake.mean()
        
        # Density-weighted gradient penalty
        gp = self.compute_density_weighted_gradient_penalty(real_data, fake_data, density_weights)
        
        # Total discriminator loss (Equation 10)
        d_loss = -w_distance + self.lambda_gp * gp
        
        d_loss.backward()
        self.optimizer_d.step()
        
        return d_loss.item(), w_distance.item(), gp.item()
    
    def train_generator(self) -> float:
        """
        Train generator for one step.
        
        Returns:
            Generator loss
        """
        self.optimizer_g.zero_grad()
        
        # Generate fake data
        z = torch.randn(64, self.latent_dim).to(self.device)  # Fixed batch size for stability
        fake_data = self.generator(z)
        
        # Generator loss (maximize discriminator output for fake data)
        g_loss = -self.discriminator(fake_data).mean()
        
        g_loss.backward()
        self.optimizer_g.step()
        
        return g_loss.item()
    
    def generate_samples(self, n_samples: int) -> np.ndarray:
        """
        Generate synthetic samples using the trained generator.
        
        Args:
            n_samples: Number of samples to generate
            
        Returns:
            Generated samples as numpy array
        """
        self.generator.eval()
        
        with torch.no_grad():
            # Generate in batches to avoid memory issues
            batch_size = min(n_samples, 1000)
            generated_samples = []
            
            for i in range(0, n_samples, batch_size):
                current_batch_size = min(batch_size, n_samples - i)
                z = torch.randn(current_batch_size, self.latent_dim).to(self.device)
                samples = self.generator(z).cpu().numpy()
                generated_samples.append(samples)
        
        self.generator.train()
        
        return np.vstack(generated_samples) if generated_samples else np.empty((0, self.input_dim))
