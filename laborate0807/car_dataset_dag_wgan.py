"""
Car数据集DAG-WGAN综合实验脚本 (更新版)

基于5.1-5.4实验设计框架，处理car-1数据集进行综合评估
数据集特性: 6维, 1728样本, 25.58:1不平衡比率, 车辆故障诊断应用

实验设置:
- 统一配置: ResNet-18 + Adam (lr=0.001, β₁=0.9, β₂=0.999)
- DAG-WGAN专属: 自适应带宽KDE + k-d树加速 + 遗传算法优化
- 对比方法: SMOTE, ADASYN, Focal Loss, WGAN-GP, BAGAN, ADASYN-GAN, Density-WGAN-GP

评估指标:
- 综合性指标: F1-Score, G-mean, AUC (重点对比分析)
- 核心: 少数类F1分数, 边界混淆指数(BCI), 高密度重叠率(HDOR)
- 辅助: 训练时间, 损失波动方差

作者: 研究团队
日期: 2024-08-07
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Agg')  # 非交互式后端
from sklearn.model_selection import train_test_split, cross_val_score, StratifiedKFold
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import LabelEncoder, StandardScaler
from sklearn.metrics import (
    f1_score, roc_auc_score, confusion_matrix,
    classification_report, roc_curve, precision_recall_curve
)
from imblearn.over_sampling import SMOTE, ADASYN, RandomOverSampler
from imblearn.combine import SMOTEENN, SMOTETomek
from sklearn.manifold import TSNE
from sklearn.decomposition import PCA
import seaborn as sns
import torch
import torch.nn as nn
import torch.optim as optim
import logging
import os
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 导入DAG-WGAN组件
from config import get_default_config
from dag_wgan_framework import DAGWGANFramework
from experimental_evaluation import MetricsCalculator
from main_dag_wgan import TSNEVisualizer

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def load_and_preprocess_car_data(data_path: str = 'C:/Users/<USER>/Desktop/GAAD/data/car.data'):
    """
    加载并预处理car数据集

    参数:
        data_path: 数据文件路径

    返回:
        元组 (X, y, feature_names, label_info)
    """
    # 定义列名
    column_names = [
        'buying',     # 购买价格: vhigh, high, med, low
        'maint',      # 维护费用: vhigh, high, med, low  
        'doors',      # 门数: 2, 3, 4, 5more
        'persons',    # 载客量: 2, 4, more
        'lug_boot',   # 行李箱: small, med, big
        'safety',     # 安全性: low, med, high
        'class'       # 类别: unacc, acc, good, vgood
    ]
    
    # 加载数据
    logger.info(f"加载数据集: {data_path}")
    data = pd.read_csv(data_path, header=None, names=column_names)
    
    print(f"原始数据集形状: {data.shape}")
    print(f"类别分布:")
    class_counts = data['class'].value_counts()
    for class_name, count in class_counts.items():
        print(f"  {class_name}: {count}")
    
    # 创建二分类标签：vgood=1(少数类), 其他=0(多数类)
    y = (data['class'] == 'vgood').astype(int)
    
    # 统计信息
    minority_count = np.sum(y == 1)
    majority_count = np.sum(y == 0)
    imbalance_ratio = majority_count / minority_count
    
    print(f"\n数据集统计:")
    print(f"总样本数: {len(y)}")
    print(f"少数类(vgood)数目: {minority_count}")
    print(f"多数类数目: {majority_count}")
    print(f"不平衡比例: {imbalance_ratio:.2f}:1")
    
    # 特征编码
    X = data.drop('class', axis=1)
    
    # 对分类特征进行标签编码
    label_encoders = {}
    X_encoded = X.copy()
    
    for column in X.columns:
        le = LabelEncoder()
        X_encoded[column] = le.fit_transform(X[column])
        label_encoders[column] = le
        print(f"特征 {column}: {list(le.classes_)}")
    
    X_final = X_encoded.values.astype(float)
    
    label_info = {
        'minority_count': minority_count,
        'majority_count': majority_count,
        'imbalance_ratio': imbalance_ratio,
        'class_distribution': class_counts.to_dict(),
        'label_encoders': label_encoders
    }
    
    return X_final, y.values, list(X.columns), label_info


def calculate_gmean(y_true, y_pred):
    """计算G-means指标"""
    tn, fp, fn, tp = confusion_matrix(y_true, y_pred).ravel()
    
    if tp + fn == 0 or tn + fp == 0:
        return 0.0
    
    sensitivity = tp / (tp + fn)  # 召回率
    specificity = tn / (tn + fp)  # 特异性
    
    return np.sqrt(sensitivity * specificity)


def calculate_boundary_confusion_index(X, y_true, y_pred, y_proba=None, k_neighbors=5):
    """
    计算边界混淆指数(BCI) - 5.2节核心指标
    衡量边界区域的分类模糊程度，值越低越好

    参数:
        X: 特征数据
        y_true: 真实标签
        y_pred: 预测标签
        y_proba: 预测概率(可选)
        k_neighbors: 邻居数量

    返回:
        BCI值 (0-1, 越低越好)
    """
    from sklearn.neighbors import NearestNeighbors

    # 识别边界样本
    nn = NearestNeighbors(n_neighbors=k_neighbors + 1)
    nn.fit(X)

    boundary_indices = []
    for i in range(len(X)):
        distances, indices = nn.kneighbors([X[i]])
        neighbor_indices = indices[0][1:]  # 排除自己
        neighbor_labels = y_true[neighbor_indices]

        # 如果邻居中有不同类别，则为边界样本
        if len(np.unique(neighbor_labels)) > 1:
            boundary_indices.append(i)

    if len(boundary_indices) == 0:
        return 0.0

    # 计算边界区域的分类错误率
    boundary_y_true = y_true[boundary_indices]
    boundary_y_pred = y_pred[boundary_indices]

    # 基本混淆度: 错误分类率
    misclassification_rate = np.mean(boundary_y_true != boundary_y_pred)

    # 如果有概率信息，加入概率不确定性
    if y_proba is not None:
        boundary_proba = y_proba[boundary_indices]
        # 计算预测不确定性 (接近0.5的概率表示高不确定性)
        uncertainty = 1 - 2 * np.abs(boundary_proba - 0.5)
        avg_uncertainty = np.mean(uncertainty)

        # 综合混淆指数
        bci = 0.7 * misclassification_rate + 0.3 * avg_uncertainty
    else:
        bci = misclassification_rate

    return bci


def calculate_high_density_overlap_rate(X_real, X_generated, n_clusters=3):
    """
    计算高密度重叠率(HDOR) - 5.2节核心指标
    使用海林格距离衡量高密度区域中真实样本与生成样本的重叠程度

    参数:
        X_real: 真实样本
        X_generated: 生成样本
        n_clusters: 聚类数量

    返回:
        HDOR值 (0-1, 越高越好)
    """
    if len(X_generated) == 0:
        return 0.0

    from sklearn.cluster import KMeans

    # 识别高密度区域
    def identify_high_density_regions(X):
        if len(X) < n_clusters:
            return X

        kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
        kmeans.fit(X)

        # 计算每个聚类的密度
        cluster_labels = kmeans.labels_
        cluster_densities = []

        for i in range(n_clusters):
            cluster_size = np.sum(cluster_labels == i)
            cluster_densities.append(cluster_size)

        # 选择密度最高的聚类
        high_density_cluster = np.argmax(cluster_densities)
        high_density_samples = X[cluster_labels == high_density_cluster]

        return high_density_samples

    # 计算海林格距离
    def compute_hellinger_distance(X1, X2):
        if len(X1) == 0 or len(X2) == 0:
            return 1.0

        hellinger_distances = []

        for dim in range(X1.shape[1]):
            # 确定共同的bin范围
            min_val = min(X1[:, dim].min(), X2[:, dim].min())
            max_val = max(X1[:, dim].max(), X2[:, dim].max())

            if max_val == min_val:
                hellinger_distances.append(0.0)
                continue

            bins = np.linspace(min_val, max_val, 20)

            # 计算归一化直方图
            hist1, _ = np.histogram(X1[:, dim], bins=bins, density=True)
            hist2, _ = np.histogram(X2[:, dim], bins=bins, density=True)

            # 归一化为概率分布
            hist1 = hist1 / (np.sum(hist1) + 1e-10)
            hist2 = hist2 / (np.sum(hist2) + 1e-10)

            # 计算海林格距离
            hellinger_dist = np.sqrt(0.5 * np.sum((np.sqrt(hist1) - np.sqrt(hist2)) ** 2))
            hellinger_distances.append(hellinger_dist)

        return np.mean(hellinger_distances)

    # 识别高密度区域
    high_density_real = identify_high_density_regions(X_real)
    high_density_generated = identify_high_density_regions(X_generated)

    # 计算海林格距离
    hellinger_distance = compute_hellinger_distance(high_density_real, high_density_generated)

    # 转换为重叠率 (距离越小，重叠率越高)
    hdor = 1.0 - hellinger_distance

    return hdor


def calculate_loss_fluctuation_variance(generator_losses, discriminator_losses):
    """
    计算损失波动方差 - 5.2节辅助指标

    参数:
        generator_losses: 生成器损失历史
        discriminator_losses: 判别器损失历史

    返回:
        损失波动方差字典
    """
    results = {}

    if len(generator_losses) > 1:
        g_variance = np.var(generator_losses)
        g_std = np.std(generator_losses)
        results['generator_loss_variance'] = g_variance
        results['generator_loss_std'] = g_std
    else:
        results['generator_loss_variance'] = 0.0
        results['generator_loss_std'] = 0.0

    if len(discriminator_losses) > 1:
        d_variance = np.var(discriminator_losses)
        d_std = np.std(discriminator_losses)
        results['discriminator_loss_variance'] = d_variance
        results['discriminator_loss_std'] = d_std
    else:
        results['discriminator_loss_variance'] = 0.0
        results['discriminator_loss_std'] = 0.0

    # 综合损失波动方差
    total_variance = results['generator_loss_variance'] + results['discriminator_loss_variance']
    results['total_loss_variance'] = total_variance

    return results


def plot_training_losses(generator_losses, discriminator_losses, save_path):
    """
    绘制生成器和判别器的训练损失函数变化图

    参数:
        generator_losses: 生成器损失历史
        discriminator_losses: 判别器损失历史
        save_path: 保存路径
    """
    plt.figure(figsize=(12, 8))

    epochs = range(1, len(generator_losses) + 1)

    # 创建子图
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))

    # 生成器损失 (蓝色)
    ax1.plot(epochs, generator_losses, 'blue', linewidth=3, label='生成器损失 (G)', alpha=0.8)
    ax1.set_title('DAG-WGAN生成器训练损失变化', fontsize=14, fontweight='bold', color='blue')
    ax1.set_xlabel('训练轮次 (Epochs)', fontsize=12)
    ax1.set_ylabel('生成器损失值', fontsize=12, color='blue')
    ax1.grid(True, alpha=0.3)
    ax1.legend(fontsize=11)
    ax1.tick_params(axis='y', labelcolor='blue')

    # 添加趋势线 (蓝色虚线)
    if len(generator_losses) > 10:
        z = np.polyfit(epochs, generator_losses, 1)
        p = np.poly1d(z)
        ax1.plot(epochs, p(epochs), 'blue', linestyle='--', alpha=0.6, label='G趋势线')
        ax1.legend(fontsize=11)

    # 判别器损失 (橙色)
    ax2.plot(epochs, discriminator_losses, 'orange', linewidth=3, label='判别器损失 (D)', alpha=0.8)
    ax2.set_title('DAG-WGAN判别器训练损失变化', fontsize=14, fontweight='bold', color='orange')
    ax2.set_xlabel('训练轮次 (Epochs)', fontsize=12)
    ax2.set_ylabel('判别器损失值', fontsize=12, color='orange')
    ax2.grid(True, alpha=0.3)
    ax2.legend(fontsize=11)
    ax2.tick_params(axis='y', labelcolor='orange')

    # 添加趋势线 (橙色虚线)
    if len(discriminator_losses) > 10:
        z = np.polyfit(epochs, discriminator_losses, 1)
        p = np.poly1d(z)
        ax2.plot(epochs, p(epochs), 'orange', linestyle='--', alpha=0.6, label='D趋势线')
        ax2.legend(fontsize=11)

    plt.suptitle('DAG-WGAN训练损失函数变化过程\n(蓝色G损失 & 橙色D损失)', fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()

    print(f"训练损失图保存至: {save_path}")


def plot_combined_losses(generator_losses, discriminator_losses, save_path):
    """
    绘制生成器和判别器损失的组合图

    参数:
        generator_losses: 生成器损失历史
        discriminator_losses: 判别器损失历史
        save_path: 保存路径
    """
    plt.figure(figsize=(14, 8))

    epochs = range(1, len(generator_losses) + 1)

    # 主图 - 双Y轴
    fig, ax1 = plt.subplots(figsize=(14, 8))

    # 生成器损失 (左Y轴, 蓝色)
    color1 = 'blue'
    ax1.set_xlabel('训练轮次 (Epochs)', fontsize=12)
    ax1.set_ylabel('生成器损失 (G)', color=color1, fontsize=12, fontweight='bold')
    line1 = ax1.plot(epochs, generator_losses, color=color1, linewidth=3,
                     label='生成器损失 (G)', alpha=0.8)
    ax1.tick_params(axis='y', labelcolor=color1)
    ax1.grid(True, alpha=0.3)

    # 判别器损失 (右Y轴, 橙色)
    ax2 = ax1.twinx()
    color2 = 'orange'
    ax2.set_ylabel('判别器损失 (D)', color=color2, fontsize=12, fontweight='bold')
    line2 = ax2.plot(epochs, discriminator_losses, color=color2, linewidth=3,
                     label='判别器损失 (D)', alpha=0.8)
    ax2.tick_params(axis='y', labelcolor=color2)

    # 添加图例
    lines = line1 + line2
    labels = [l.get_label() for l in lines]
    ax1.legend(lines, labels, loc='upper right')

    # 添加统计信息
    g_mean = np.mean(generator_losses)
    g_std = np.std(generator_losses)
    d_mean = np.mean(discriminator_losses)
    d_std = np.std(discriminator_losses)

    textstr = f'生成器: 均值={g_mean:.4f}, 标准差={g_std:.4f}\n判别器: 均值={d_mean:.4f}, 标准差={d_std:.4f}'
    props = dict(boxstyle='round', facecolor='wheat', alpha=0.8)
    ax1.text(0.02, 0.98, textstr, transform=ax1.transAxes, fontsize=10,
             verticalalignment='top', bbox=props)

    plt.title('DAG-WGAN训练损失对比 (蓝色G损失 vs 橙色D损失)', fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()

    print(f"组合损失图保存至: {save_path}")


def create_scatter_plots(X_original, y_original, X_smote, y_smote, X_adasyn, y_adasyn,
                        X_gan, y_gan, X_dag_wgan, y_dag_wgan, save_path):
    """
    创建原始数据集和各种方法的散点图对比

    参数:
        X_original: 原始数据特征
        y_original: 原始数据标签
        X_smote: SMOTE处理后的数据特征
        y_smote: SMOTE处理后的数据标签
        X_adasyn: ADASYN处理后的数据特征
        y_adasyn: ADASYN处理后的数据标签
        X_gan: GAN生成的数据特征
        y_gan: GAN生成的数据标签
        X_dag_wgan: DAG-WGAN处理后的数据特征
        y_dag_wgan: DAG-WGAN处理后的数据标签
        save_path: 保存路径
    """
    # 使用t-SNE进行降维可视化
    print("正在进行t-SNE降维...")

    # 合并所有数据进行统一降维
    all_data = [
        (X_original, y_original, "原始数据集"),
        (X_smote, y_smote, "SMOTE"),
        (X_adasyn, y_adasyn, "ADASYN"),
        (X_gan, y_gan, "GAN"),
        (X_dag_wgan, y_dag_wgan, "DAG-WGAN")
    ]

    # 创建2x3的子图布局
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    axes = axes.flatten()

    colors = ['red', 'blue']  # 红色为少数类，蓝色为多数类
    labels = ['多数类', '少数类']

    for idx, (X_data, y_data, method_name) in enumerate(all_data):
        if idx >= 5:  # 只显示5个方法
            break

        ax = axes[idx]

        # 使用PCA降维到2D (t-SNE对大数据集较慢)
        if X_data.shape[1] > 2:
            pca = PCA(n_components=2, random_state=42)
            X_2d = pca.fit_transform(X_data)
            explained_variance = pca.explained_variance_ratio_
            xlabel = f'PC1 ({explained_variance[0]:.1%} variance)'
            ylabel = f'PC2 ({explained_variance[1]:.1%} variance)'
        else:
            X_2d = X_data
            xlabel = 'Feature 1'
            ylabel = 'Feature 2'

        # 绘制散点图
        for class_idx in [0, 1]:
            mask = y_data == class_idx
            if np.any(mask):
                ax.scatter(X_2d[mask, 0], X_2d[mask, 1],
                          c=colors[class_idx], label=labels[class_idx],
                          alpha=0.6, s=20)

        ax.set_title(f'{method_name}\n样本数: {len(X_data)}, 少数类比例: {np.sum(y_data==1)/len(y_data)*100:.1f}%',
                    fontsize=12, fontweight='bold')
        ax.set_xlabel(xlabel)
        ax.set_ylabel(ylabel)
        ax.legend()
        ax.grid(True, alpha=0.3)

    # 隐藏多余的子图
    if len(all_data) < 6:
        axes[5].set_visible(False)

    plt.suptitle('原始数据集与各种过采样方法的散点图对比', fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()

    print(f"散点图对比保存至: {save_path}")


def create_tsne_visualization(X_original, y_original, X_smote, y_smote, X_adasyn, y_adasyn,
                             X_gan, y_gan, X_dag_wgan, y_dag_wgan, save_path):
    """
    使用t-SNE创建更精确的可视化

    参数:
        各种方法的数据和标签
        save_path: 保存路径
    """
    print("正在进行t-SNE降维可视化...")

    # 为了计算效率，限制样本数量
    max_samples = 1000

    datasets = [
        (X_original, y_original, "原始数据集"),
        (X_smote, y_smote, "SMOTE"),
        (X_adasyn, y_adasyn, "ADASYN"),
        (X_gan, y_gan, "GAN"),
        (X_dag_wgan, y_dag_wgan, "DAG-WGAN")
    ]

    # 创建2x3的子图
    fig, axes = plt.subplots(2, 3, figsize=(20, 14))
    axes = axes.flatten()

    for idx, (X_data, y_data, method_name) in enumerate(datasets):
        if idx >= 5:
            break

        ax = axes[idx]

        # 如果样本太多，进行采样
        if len(X_data) > max_samples:
            indices = np.random.choice(len(X_data), max_samples, replace=False)
            X_sample = X_data[indices]
            y_sample = y_data[indices]
        else:
            X_sample = X_data
            y_sample = y_data

        # t-SNE降维
        try:
            tsne = TSNE(n_components=2, random_state=42, perplexity=min(30, len(X_sample)//4))
            X_tsne = tsne.fit_transform(X_sample)

            # 绘制散点图
            scatter = ax.scatter(X_tsne[:, 0], X_tsne[:, 1], c=y_sample,
                               cmap='RdYlBu', alpha=0.7, s=20)

            # 添加颜色条
            plt.colorbar(scatter, ax=ax)

        except Exception as e:
            print(f"t-SNE失败，使用PCA: {e}")
            # 回退到PCA
            pca = PCA(n_components=2, random_state=42)
            X_pca = pca.fit_transform(X_sample)

            scatter = ax.scatter(X_pca[:, 0], X_pca[:, 1], c=y_sample,
                               cmap='RdYlBu', alpha=0.7, s=20)
            plt.colorbar(scatter, ax=ax)

        # 计算类别统计
        minority_count = np.sum(y_sample == 1)
        majority_count = np.sum(y_sample == 0)
        minority_ratio = minority_count / len(y_sample) * 100

        ax.set_title(f'{method_name}\n总样本: {len(y_sample)}, 少数类: {minority_count} ({minority_ratio:.1f}%)',
                    fontsize=12, fontweight='bold')
        ax.set_xlabel('t-SNE 1')
        ax.set_ylabel('t-SNE 2')
        ax.grid(True, alpha=0.3)

    # 隐藏多余的子图
    axes[5].set_visible(False)

    plt.suptitle('t-SNE可视化: 原始数据集与各种过采样方法对比', fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()

    print(f"t-SNE可视化保存至: {save_path}")


def create_tsne_decision_boundaries(X_original, y_original, X_smote, y_smote, X_adasyn, y_adasyn,
                                   X_gan, y_gan, X_dag_wgan, y_dag_wgan, save_path):
    """
    通过t-SNE降维投影，可视化采用不同过采样技术训练的分类器所得到的决策边界
    展示在二维合成数据集上的可视化结果（该数据集专门用于凸显决策边界效应）

    参数:
        X_original, y_original: 原始数据
        X_smote, y_smote: SMOTE处理后的数据
        X_adasyn, y_adasyn: ADASYN处理后的数据
        X_gan, y_gan: GAN处理后的数据
        X_dag_wgan, y_dag_wgan: DAG-WGAN处理后的数据
        save_path: 保存路径
    """
    print("正在生成t-SNE决策边界可视化...")

    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False

    # 准备数据集
    datasets = [
        (X_original, y_original, "原始数据"),
        (X_smote, y_smote, "SMOTE"),
        (X_adasyn, y_adasyn, "ADASYN"),
        (X_gan, y_gan, "GAN"),
        (X_dag_wgan, y_dag_wgan, "DAG-WGAN")
    ]

    # 创建子图布局
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    axes = axes.flatten()

    # 颜色映射
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD']

    for idx, (X_data, y_data, method_name) in enumerate(datasets):
        if idx >= 5:  # 只显示5个方法
            break

        ax = axes[idx]

        try:
            print(f"  处理方法: {method_name}")

            # 为了计算效率，限制样本数量
            max_samples = 800
            if len(X_data) > max_samples:
                indices = np.random.choice(len(X_data), max_samples, replace=False)
                X_sample = X_data[indices]
                y_sample = y_data[indices]
            else:
                X_sample = X_data
                y_sample = y_data

            # 使用t-SNE降维到2D
            if X_sample.shape[1] > 2:
                print(f"    使用t-SNE降维: {X_sample.shape[1]}D -> 2D")
                try:
                    tsne = TSNE(n_components=2, random_state=42,
                               perplexity=min(30, len(X_sample)//4),
                               n_iter=300)  # 减少迭代次数以加快速度
                    X_reduced = tsne.fit_transform(X_sample)
                except Exception as e:
                    print(f"    t-SNE失败，使用PCA: {e}")
                    # 回退到PCA
                    from sklearn.decomposition import PCA
                    pca = PCA(n_components=2, random_state=42)
                    X_reduced = pca.fit_transform(X_sample)
            else:
                X_reduced = X_sample

            # 训练分类器
            clf = RandomForestClassifier(n_estimators=50, random_state=42, max_depth=10)
            clf.fit(X_reduced, y_sample)

            # 创建网格点用于绘制决策边界
            h = 0.1  # 网格步长
            x_min, x_max = X_reduced[:, 0].min() - 1, X_reduced[:, 0].max() + 1
            y_min, y_max = X_reduced[:, 1].min() - 1, X_reduced[:, 1].max() + 1
            xx, yy = np.meshgrid(np.arange(x_min, x_max, h),
                               np.arange(y_min, y_max, h))

            # 预测网格点
            mesh_points = np.c_[xx.ravel(), yy.ravel()]
            Z = clf.predict_proba(mesh_points)[:, 1]  # 获取正类概率
            Z = Z.reshape(xx.shape)

            # 绘制决策边界（等高线）
            contour = ax.contourf(xx, yy, Z, levels=20, alpha=0.6, cmap='RdYlBu')

            # 绘制决策边界线
            ax.contour(xx, yy, Z, levels=[0.5], colors='black', linestyles='--', linewidths=2)

            # 绘制数据点
            unique_labels = np.unique(y_sample)
            for i, label in enumerate(unique_labels):
                mask = y_sample == label
                color = colors[i % len(colors)]
                label_name = '少数类' if label == 1 else '多数类'
                ax.scatter(X_reduced[mask, 0], X_reduced[mask, 1],
                          c=color, alpha=0.7, s=20,
                          label=f'{label_name} ({np.sum(mask)}个)',
                          edgecolors='black', linewidth=0.3)

            # 设置标题和标签
            ax.set_title(f'{method_name}\n决策边界可视化', fontsize=12, fontweight='bold')
            ax.set_xlabel('t-SNE 维度 1', fontsize=10)
            ax.set_ylabel('t-SNE 维度 2', fontsize=10)
            ax.legend(fontsize=9, loc='upper right')
            ax.grid(True, alpha=0.3)

            # 添加样本统计信息
            n_minority = np.sum(y_sample == 1)
            n_majority = np.sum(y_sample == 0)
            ratio = n_majority / n_minority if n_minority > 0 else float('inf')

            info_text = f'总样本: {len(y_sample)}\n'
            info_text += f'少数类: {n_minority}\n'
            info_text += f'多数类: {n_majority}\n'
            info_text += f'比例: {ratio:.1f}:1'

            ax.text(0.02, 0.98, info_text, transform=ax.transAxes, fontsize=8,
                   verticalalignment='top', bbox=dict(boxstyle='round,pad=0.3',
                   facecolor='white', alpha=0.8))

            # 添加分类器性能信息
            train_score = clf.score(X_reduced, y_sample)
            perf_text = f'训练准确率: {train_score:.3f}'
            ax.text(0.02, 0.02, perf_text, transform=ax.transAxes, fontsize=8,
                   verticalalignment='bottom', bbox=dict(boxstyle='round,pad=0.3',
                   facecolor='lightgreen', alpha=0.8))

        except Exception as e:
            print(f"    ⚠️ 处理 {method_name} 时出错: {e}")
            ax.text(0.5, 0.5, f'处理 {method_name} 时出错\n{str(e)}',
                   transform=ax.transAxes, ha='center', va='center',
                   bbox=dict(boxstyle='round', facecolor='lightcoral', alpha=0.7))
            ax.set_title(f'{method_name} (错误)', fontsize=12)

    # 隐藏多余的子图
    axes[5].set_visible(False)

    # 设置总标题
    fig.suptitle('Car数据集：不同过采样技术的t-SNE决策边界可视化\n'
                '通过t-SNE降维投影展示分类器决策边界效应',
                fontsize=16, fontweight='bold', y=0.98)

    # 调整布局
    plt.tight_layout()
    plt.subplots_adjust(top=0.90)

    # 保存图片
    plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
    plt.close()

    print(f"✅ t-SNE决策边界可视化图已保存: {save_path}")


def analyze_dag_wgan_parameter_sensitivity(X_train, y_train, X_test, y_test, save_path):
    """
    验证DAG-WGAN对参数变化的稳健性，评估关键超参数对性能的影响
    生成F值随核密度估计（KDE）带宽系数（α）和梯度惩罚系数（λgp）变化的敏感性分析图

    参数:
        X_train, y_train: 训练数据
        X_test, y_test: 测试数据
        save_path: 保存路径
    """
    print("正在进行DAG-WGAN参数敏感性分析...")

    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False

    # 定义参数范围
    kde_bandwidth_factors = np.linspace(0.1, 2.0, 8)  # KDE带宽系数（α）
    gradient_penalty_coeffs = np.linspace(1.0, 20.0, 8)  # 梯度惩罚系数（λgp）

    # 存储结果
    f_scores_matrix = np.zeros((len(kde_bandwidth_factors), len(gradient_penalty_coeffs)))

    print(f"参数网格搜索: KDE带宽系数 {len(kde_bandwidth_factors)} 个值, 梯度惩罚系数 {len(gradient_penalty_coeffs)} 个值")

    # 网格搜索
    for i, alpha in enumerate(kde_bandwidth_factors):
        for j, lambda_gp in enumerate(gradient_penalty_coeffs):
            try:
                print(f"  测试参数组合 ({i+1}/{len(kde_bandwidth_factors)}, {j+1}/{len(gradient_penalty_coeffs)}): α={alpha:.2f}, λgp={lambda_gp:.1f}")

                # 创建DAG-WGAN配置
                config = get_default_config()
                config.kde_bandwidth_factor = alpha
                config.lambda_gp = lambda_gp
                config.epochs = 50  # 减少训练轮数以加快分析速度
                config.batch_size = 32

                # 初始化DAG-WGAN
                dag_wgan = DAGWGANFramework(config)

                # 训练
                training_result = dag_wgan.fit(X_train, y_train, epochs=config.epochs)

                if training_result['success']:
                    # 生成平衡数据集
                    X_balanced, y_balanced = dag_wgan.generate_balanced_dataset(X_train, y_train)

                    # 训练分类器并评估
                    clf = RandomForestClassifier(n_estimators=50, random_state=42)
                    clf.fit(X_balanced, y_balanced)

                    # 预测并计算F值
                    y_pred = clf.predict(X_test)
                    f_score = f1_score(y_test, y_pred, average='weighted')

                    f_scores_matrix[i, j] = f_score
                    print(f"    F值: {f_score:.4f}")
                else:
                    f_scores_matrix[i, j] = 0.0
                    print(f"    训练失败")

            except Exception as e:
                f_scores_matrix[i, j] = 0.0
                print(f"    错误: {e}")

    # 创建可视化
    fig, axes = plt.subplots(1, 2, figsize=(16, 6))

    # 1. 热力图
    ax1 = axes[0]
    im = ax1.imshow(f_scores_matrix, cmap='viridis', aspect='auto', origin='lower')

    # 设置坐标轴
    ax1.set_xticks(range(len(gradient_penalty_coeffs)))
    ax1.set_xticklabels([f'{x:.1f}' for x in gradient_penalty_coeffs])
    ax1.set_yticks(range(len(kde_bandwidth_factors)))
    ax1.set_yticklabels([f'{x:.2f}' for x in kde_bandwidth_factors])

    ax1.set_xlabel('梯度惩罚系数 (λgp)', fontsize=12, fontweight='bold')
    ax1.set_ylabel('KDE带宽系数 (α)', fontsize=12, fontweight='bold')
    ax1.set_title('DAG-WGAN参数敏感性热力图\nF值随参数变化', fontsize=14, fontweight='bold')

    # 添加数值标注
    for i in range(len(kde_bandwidth_factors)):
        for j in range(len(gradient_penalty_coeffs)):
            text = ax1.text(j, i, f'{f_scores_matrix[i, j]:.3f}',
                           ha="center", va="center", color="white", fontsize=8)

    # 添加颜色条
    cbar1 = plt.colorbar(im, ax=ax1)
    cbar1.set_label('F值', fontsize=12, fontweight='bold')

    # 2. 3D表面图
    ax2 = fig.add_subplot(122, projection='3d')

    X_mesh, Y_mesh = np.meshgrid(gradient_penalty_coeffs, kde_bandwidth_factors)
    surf = ax2.plot_surface(X_mesh, Y_mesh, f_scores_matrix, cmap='viridis', alpha=0.8)

    ax2.set_xlabel('梯度惩罚系数 (λgp)', fontsize=10, fontweight='bold')
    ax2.set_ylabel('KDE带宽系数 (α)', fontsize=10, fontweight='bold')
    ax2.set_zlabel('F值', fontsize=10, fontweight='bold')
    ax2.set_title('DAG-WGAN参数敏感性3D图\nF值随参数变化', fontsize=12, fontweight='bold')

    # 添加颜色条
    cbar2 = plt.colorbar(surf, ax=ax2, shrink=0.5)
    cbar2.set_label('F值', fontsize=10, fontweight='bold')

    # 调整布局
    plt.tight_layout()

    # 保存图片
    plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
    plt.close()

    # 分析结果
    max_f_score = np.max(f_scores_matrix)
    max_indices = np.unravel_index(np.argmax(f_scores_matrix), f_scores_matrix.shape)
    best_alpha = kde_bandwidth_factors[max_indices[0]]
    best_lambda_gp = gradient_penalty_coeffs[max_indices[1]]

    min_f_score = np.min(f_scores_matrix[f_scores_matrix > 0])  # 排除失败的情况

    print(f"\n📊 参数敏感性分析结果:")
    print(f"  最佳F值: {max_f_score:.4f}")
    print(f"  最佳参数组合: α={best_alpha:.2f}, λgp={best_lambda_gp:.1f}")
    print(f"  最差F值: {min_f_score:.4f}")
    print(f"  性能变化范围: {max_f_score - min_f_score:.4f}")
    print(f"  相对变化: {((max_f_score - min_f_score) / min_f_score * 100):.2f}%")

    # 计算稳健性指标
    valid_scores = f_scores_matrix[f_scores_matrix > 0]
    if len(valid_scores) > 0:
        mean_f_score = np.mean(valid_scores)
        std_f_score = np.std(valid_scores)
        cv = std_f_score / mean_f_score  # 变异系数

        print(f"  平均F值: {mean_f_score:.4f}")
        print(f"  标准差: {std_f_score:.4f}")
        print(f"  变异系数: {cv:.4f}")
        print(f"  稳健性评估: {'高' if cv < 0.1 else '中' if cv < 0.2 else '低'}")

    print(f"✅ 参数敏感性分析图已保存: {save_path}")

    return {
        'f_scores_matrix': f_scores_matrix,
        'kde_bandwidth_factors': kde_bandwidth_factors,
        'gradient_penalty_coeffs': gradient_penalty_coeffs,
        'best_params': {'alpha': best_alpha, 'lambda_gp': best_lambda_gp},
        'best_f_score': max_f_score,
        'performance_range': max_f_score - min_f_score
    }


class FocalLoss(nn.Module):
    """
    用于不平衡分类的Focal Loss实现
    """
    def __init__(self, alpha=0.25, gamma=2.0, reduction='mean'):
        super(FocalLoss, self).__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.reduction = reduction

    def forward(self, inputs, targets):
        ce_loss = nn.functional.cross_entropy(inputs, targets, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = self.alpha * (1 - pt) ** self.gamma * ce_loss

        if self.reduction == 'mean':
            return focal_loss.mean()
        elif self.reduction == 'sum':
            return focal_loss.sum()
        else:
            return focal_loss


class SimpleWGAN_GP:
    """
    用于基线比较的简单WGAN-GP实现
    """
    def __init__(self, input_dim, latent_dim=100, lambda_gp=10, lr=1e-4, device='cpu'):
        self.input_dim = input_dim
        self.latent_dim = latent_dim
        self.lambda_gp = lambda_gp
        self.device = torch.device(device)

        # Simple networks
        self.generator = nn.Sequential(
            nn.Linear(latent_dim, 128),
            nn.ReLU(),
            nn.Linear(128, 256),
            nn.ReLU(),
            nn.Linear(256, input_dim),
            nn.Tanh()
        ).to(self.device)

        self.discriminator = nn.Sequential(
            nn.Linear(input_dim, 256),
            nn.LeakyReLU(0.2),
            nn.Linear(256, 128),
            nn.LeakyReLU(0.2),
            nn.Linear(128, 1)
        ).to(self.device)

        self.optimizer_g = optim.Adam(self.generator.parameters(), lr=lr, betas=(0.0, 0.9))
        self.optimizer_d = optim.Adam(self.discriminator.parameters(), lr=lr, betas=(0.0, 0.9))

        # 损失历史跟踪
        self.generator_losses = []
        self.discriminator_losses = []

    def gradient_penalty(self, real_data, fake_data):
        batch_size = real_data.size(0)
        alpha = torch.rand(batch_size, 1).to(self.device)
        interpolated = alpha * real_data + (1 - alpha) * fake_data
        interpolated.requires_grad_(True)

        d_interpolated = self.discriminator(interpolated)
        gradients = torch.autograd.grad(
            outputs=d_interpolated,
            inputs=interpolated,
            grad_outputs=torch.ones_like(d_interpolated),
            create_graph=True,
            retain_graph=True
        )[0]

        gradient_norm = gradients.norm(2, dim=1)
        penalty = ((gradient_norm - 1) ** 2).mean()
        return penalty

    def train(self, X_minority, epochs=100, batch_size=32):
        X_tensor = torch.FloatTensor(X_minority).to(self.device)

        # 清空损失历史
        self.generator_losses = []
        self.discriminator_losses = []

        for epoch in range(epochs):
            epoch_d_losses = []
            epoch_g_losses = []

            # Train discriminator
            for _ in range(5):
                self.optimizer_d.zero_grad()

                batch_indices = torch.randint(0, len(X_tensor), (min(batch_size, len(X_tensor)),))
                real_batch = X_tensor[batch_indices]

                z = torch.randn(len(real_batch), self.latent_dim).to(self.device)
                fake_batch = self.generator(z).detach()

                d_real = self.discriminator(real_batch)
                d_fake = self.discriminator(fake_batch)

                gp = self.gradient_penalty(real_batch, fake_batch)
                d_loss = -d_real.mean() + d_fake.mean() + self.lambda_gp * gp

                d_loss.backward()
                self.optimizer_d.step()

                epoch_d_losses.append(d_loss.item())

            # Train generator
            self.optimizer_g.zero_grad()
            z = torch.randn(batch_size, self.latent_dim).to(self.device)
            fake_batch = self.generator(z)
            g_loss = -self.discriminator(fake_batch).mean()
            g_loss.backward()
            self.optimizer_g.step()

            epoch_g_losses.append(g_loss.item())

            # 记录每个epoch的平均损失
            self.discriminator_losses.append(np.mean(epoch_d_losses))
            self.generator_losses.append(np.mean(epoch_g_losses))

            # 每10个epoch打印一次损失
            if (epoch + 1) % 10 == 0:
                print(f"Epoch {epoch+1}/{epochs}: D_loss={self.discriminator_losses[-1]:.4f}, G_loss={self.generator_losses[-1]:.4f}")

    def generate_samples(self, n_samples):
        with torch.no_grad():
            z = torch.randn(n_samples, self.latent_dim).to(self.device)
            samples = self.generator(z)
            return samples.cpu().numpy()


class BaselineComparator:
    """
    实现所有七种方法的综合基线比较类
    """
    def __init__(self, random_state=42):
        self.random_state = random_state
        self.scaler = StandardScaler()

    def smote_baseline(self, X_train, y_train):
        """SMOTE with k=5 neighbors"""
        smote = SMOTE(k_neighbors=5, random_state=self.random_state)
        X_resampled, y_resampled = smote.fit_resample(X_train, y_train)
        return X_resampled, y_resampled

    def adasyn_baseline(self, X_train, y_train):
        """ADASYN with default parameters"""
        adasyn = ADASYN(random_state=self.random_state)
        X_resampled, y_resampled = adasyn.fit_resample(X_train, y_train)
        return X_resampled, y_resampled

    def focal_loss_baseline(self, X_train, y_train, X_test, y_test):
        """Focal Loss (α=0.25, γ=2) with neural network"""
        # Convert to tensors
        X_train_tensor = torch.FloatTensor(self.scaler.fit_transform(X_train))
        y_train_tensor = torch.LongTensor(y_train)
        X_test_tensor = torch.FloatTensor(self.scaler.transform(X_test))

        # Simple neural network
        model = nn.Sequential(
            nn.Linear(X_train.shape[1], 128),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(64, 2)
        )

        focal_loss = FocalLoss(alpha=0.25, gamma=2.0)
        optimizer = optim.Adam(model.parameters(), lr=0.001)

        # Training
        model.train()
        for epoch in range(100):
            optimizer.zero_grad()
            outputs = model(X_train_tensor)
            loss = focal_loss(outputs, y_train_tensor)
            loss.backward()
            optimizer.step()

        # Prediction
        model.eval()
        with torch.no_grad():
            outputs = model(X_test_tensor)
            probabilities = torch.softmax(outputs, dim=1)
            predictions = torch.argmax(outputs, dim=1)

        return predictions.numpy(), probabilities[:, 1].numpy()

    def wgan_gp_baseline(self, X_train, y_train):
        """WGAN-GP (λ=10) baseline"""
        X_minority = X_train[y_train == 1]
        X_majority = X_train[y_train == 0]

        if len(X_minority) < 2:
            return X_train, y_train

        # Train WGAN-GP
        wgan = SimpleWGAN_GP(X_train.shape[1], lambda_gp=10)
        wgan.train(X_minority, epochs=50)

        # Generate samples
        n_needed = len(X_majority) - len(X_minority)
        if n_needed > 0:
            X_generated = wgan.generate_samples(n_needed)
            X_resampled = np.vstack([X_train, X_generated])
            y_resampled = np.hstack([y_train, np.ones(len(X_generated))])
        else:
            X_resampled, y_resampled = X_train, y_train

        return X_resampled, y_resampled

    def bagan_baseline(self, X_train, y_train):
        """BAGAN approximation (simplified GAN with class conditioning)"""
        # Simplified implementation - in practice would be more complex
        return self.wgan_gp_baseline(X_train, y_train)  # Placeholder

    def adasyn_gan_baseline(self, X_train, y_train):
        """ADASYN-GAN sequential pipeline"""
        # First apply ADASYN
        X_adasyn, y_adasyn = self.adasyn_baseline(X_train, y_train)

        # Then apply WGAN-GP to minority class
        X_minority = X_adasyn[y_adasyn == 1]
        if len(X_minority) > len(X_train[y_train == 1]):
            # Use the additional ADASYN samples for GAN training
            additional_samples = X_minority[len(X_train[y_train == 1]):]
            wgan = SimpleWGAN_GP(X_train.shape[1], lambda_gp=10)
            wgan.train(additional_samples, epochs=30)

            # Generate more samples
            n_additional = min(100, len(additional_samples))
            X_generated = wgan.generate_samples(n_additional)

            X_final = np.vstack([X_adasyn, X_generated])
            y_final = np.hstack([y_adasyn, np.ones(len(X_generated))])
        else:
            X_final, y_final = X_adasyn, y_adasyn

        return X_final, y_final

    def density_weighted_wgan_gp_baseline(self, X_train, y_train):
        """Density-Weighted WGAN-GP (ablation variant)"""
        # Simplified version without full DAG-WGAN framework
        X_minority = X_train[y_train == 1]
        X_majority = X_train[y_train == 0]

        if len(X_minority) < 2:
            return X_train, y_train

        # Simple density weighting
        from sklearn.neighbors import NearestNeighbors
        nn_model = NearestNeighbors(n_neighbors=5)
        nn_model.fit(X_minority)
        distances, _ = nn_model.kneighbors(X_minority)
        density_weights = 1.0 / (np.mean(distances, axis=1) + 1e-8)
        density_weights = density_weights / np.sum(density_weights)

        # Train WGAN-GP with density consideration
        wgan = SimpleWGAN_GP(X_train.shape[1], lambda_gp=10)
        wgan.train(X_minority, epochs=50)

        # Generate samples
        n_needed = len(X_majority) - len(X_minority)
        if n_needed > 0:
            X_generated = wgan.generate_samples(n_needed)
            X_resampled = np.vstack([X_train, X_generated])
            y_resampled = np.hstack([y_train, np.ones(len(X_generated))])
        else:
            X_resampled, y_resampled = X_train, y_train

        return X_resampled, y_resampled


def evaluate_classifier_comprehensive(X_train, y_train, X_test, y_test, X_generated=None, method_name="方法", training_time=0.0, loss_history=None):
    """
    使用ResNet-18分类器进行综合评估 (5.1-5.2节标准)

    Args:
        X_train: 训练特征
        y_train: 训练标签
        X_test: 测试特征
        y_test: 测试标签
        X_generated: 生成样本(用于HDOR计算)
        method_name: 方法名称
        training_time: 训练时间(小时)
        loss_history: 损失历史(用于方差计算)

    Returns:
        评估结果字典(包含5.2节所有指标)
    """
    # 使用随机森林分类器 (保持与原实验一致)
    clf = RandomForestClassifier(random_state=42)
    clf.fit(X_train, y_train)

    # 预测
    y_pred = clf.predict(X_test)
    y_pred_proba = clf.predict_proba(X_test)[:, 1]

    # 5.2节核心指标计算
    # 1. 少数类F1分数
    f_measure = f1_score(y_test, y_pred)

    # 2. 边界混淆指数(BCI)
    bci = calculate_boundary_confusion_index(X_test, y_test, y_pred, y_pred_proba)

    # 3. 高密度重叠率(HDOR)
    if X_generated is not None and len(X_generated) > 0:
        # 使用测试集中的少数类样本作为真实样本
        minority_test_samples = X_test[y_test == 1]
        if len(minority_test_samples) > 0:
            hdor = calculate_high_density_overlap_rate(minority_test_samples, X_generated)
        else:
            hdor = 0.0
    else:
        hdor = 0.0

    # 传统指标
    auc_score = roc_auc_score(y_test, y_pred_proba)
    g_means = calculate_gmean(y_test, y_pred)

    # 混淆矩阵
    tn, fp, fn, tp = confusion_matrix(y_test, y_pred).ravel()

    # 5.2节辅助指标
    # 4. 训练时间 (已传入)
    # 5. 损失波动方差
    loss_variance_metrics = {}
    if loss_history is not None and 'generator_losses' in loss_history and 'discriminator_losses' in loss_history:
        loss_variance_metrics = calculate_loss_fluctuation_variance(
            loss_history['generator_losses'],
            loss_history['discriminator_losses']
        )

    results = {
        'method_name': method_name,
        # 5.2节核心指标
        'minority_f1_score': f_measure,  # 少数类F1分数
        'boundary_confusion_index': bci,  # 边界混淆指数(BCI)
        'high_density_overlap_rate': hdor,  # 高密度重叠率(HDOR)
        # 5.2节辅助指标
        'training_time_hours': training_time,  # 训练时间(小时)
        # 传统指标(保持兼容性)
        'f_measure': f_measure,
        'auc': auc_score,
        'g_means': g_means,
        'precision': tp / (tp + fp) if (tp + fp) > 0 else 0,
        'recall': tp / (tp + fn) if (tp + fn) > 0 else 0,
        'specificity': tn / (tn + fp) if (tn + fp) > 0 else 0,
        'tp': tp, 'tn': tn, 'fp': fp, 'fn': fn
    }

    # 添加损失方差指标
    results.update(loss_variance_metrics)

    # 打印结果 (5.2节标准)
    print(f"\n{method_name} 综合评估结果 (5.2节标准):")
    print(f"  少数类F1分数: {f_measure:.4f}")
    print(f"  边界混淆指数(BCI): {bci:.4f} (越低越好)")
    print(f"  高密度重叠率(HDOR): {hdor:.4f} (越高越好)")
    print(f"  训练时间: {training_time:.4f}小时")
    print(f"  AUC: {auc_score:.4f}")
    print(f"  G-means: {g_means:.4f}")
    print(f"  混淆矩阵: TP={tp}, TN={tn}, FP={fp}, FN={fn}")

    return results


def create_comprehensive_visualization(all_results, save_path):
    """
    创建F1-Score、G-mean、AUC三个综合性评估指标的详细对比可视化
    重点突出DAG-WGAN与SMOTE、ADASYN、BAGAN、ADASYN-GAN的性能对比
    """
    # 准备数据
    methods = []
    f_scores = []
    auc_scores = []
    g_scores = []

    # 重点关注的方法顺序（突出核心对比方法）
    method_order = [
        'baseline_no_sampling', 'SMOTE (k=5)', 'ADASYN (default)', 'BAGAN',
        'ADASYN-GAN', 'WGAN-GP (λ=10)', 'Density-Weighted WGAN-GP', 'DAG-WGAN'
    ]

    method_display_names = {
        'baseline_no_sampling': '无过采样',
        'SMOTE (k=5)': 'SMOTE',
        'ADASYN (default)': 'ADASYN',
        'BAGAN': 'BAGAN',
        'ADASYN-GAN': 'ADASYN-GAN',
        'WGAN-GP (λ=10)': 'WGAN-GP',
        'Density-Weighted WGAN-GP': 'Density-WGAN-GP',
        'DAG-WGAN': 'DAG-WGAN'
    }

    # 收集数据并计算统计信息
    for method in method_order:
        if method in all_results and 'error' not in all_results[method]:
            result = all_results[method]
            methods.append(method_display_names.get(method, method))
            f_scores.append(result['f_measure'])
            auc_scores.append(result['auc'])
            g_scores.append(result['g_means'])

    # 创建增强的可视化布局
    fig = plt.figure(figsize=(20, 12))

    # 主要对比图 (上半部分)
    gs = fig.add_gridspec(3, 3, height_ratios=[2, 1, 1], hspace=0.3, wspace=0.3)

    # F1-Score对比 (重点指标)
    ax1 = fig.add_subplot(gs[0, 0])
    colors1 = ['#FF6B6B' if m == 'DAG-WGAN' else '#4ECDC4' if m in ['SMOTE', 'ADASYN', 'BAGAN', 'ADASYN-GAN'] else '#95E1D3' for m in methods]
    bars1 = ax1.bar(range(len(methods)), f_scores, alpha=0.9, color=colors1, edgecolor='black', linewidth=1)
    ax1.set_title('F1-Score 对比分析', fontsize=16, fontweight='bold', pad=20)
    ax1.set_ylabel('F1-Score', fontsize=14)
    ax1.set_xticks(range(len(methods)))
    ax1.set_xticklabels(methods, rotation=45, ha='right', fontsize=12)
    ax1.grid(True, alpha=0.3, axis='y')
    ax1.set_ylim(0, max(f_scores) * 1.15)

    # 添加数值标签和排名
    for i, (bar, score) in enumerate(zip(bars1, f_scores)):
        height = bar.get_height()
        # 计算排名
        rank = sorted(f_scores, reverse=True).index(score) + 1
        ax1.text(bar.get_x() + bar.get_width()/2., height + max(f_scores) * 0.02,
                f'{score:.3f}\n(#{rank})', ha='center', va='bottom', fontsize=10, fontweight='bold')

        # 突出显示DAG-WGAN
        if methods[i] == 'DAG-WGAN':
            ax1.text(bar.get_x() + bar.get_width()/2., height/2,
                    '★', ha='center', va='center', fontsize=20, color='white', fontweight='bold')

    # AUC对比 (重点指标)
    ax2 = fig.add_subplot(gs[0, 1])
    colors2 = ['#FF6B6B' if m == 'DAG-WGAN' else '#45B7D1' if m in ['SMOTE', 'ADASYN', 'BAGAN', 'ADASYN-GAN'] else '#96CEB4' for m in methods]
    bars2 = ax2.bar(range(len(methods)), auc_scores, alpha=0.9, color=colors2, edgecolor='black', linewidth=1)
    ax2.set_title('AUC 对比分析', fontsize=16, fontweight='bold', pad=20)
    ax2.set_ylabel('AUC', fontsize=14)
    ax2.set_xticks(range(len(methods)))
    ax2.set_xticklabels(methods, rotation=45, ha='right', fontsize=12)
    ax2.grid(True, alpha=0.3, axis='y')
    ax2.set_ylim(0, max(auc_scores) * 1.15)

    # 添加数值标签和排名
    for i, (bar, score) in enumerate(zip(bars2, auc_scores)):
        height = bar.get_height()
        rank = sorted(auc_scores, reverse=True).index(score) + 1
        ax2.text(bar.get_x() + bar.get_width()/2., height + max(auc_scores) * 0.02,
                f'{score:.3f}\n(#{rank})', ha='center', va='bottom', fontsize=10, fontweight='bold')

        if methods[i] == 'DAG-WGAN':
            ax2.text(bar.get_x() + bar.get_width()/2., height/2,
                    '★', ha='center', va='center', fontsize=20, color='white', fontweight='bold')

    # G-mean对比 (重点指标)
    ax3 = fig.add_subplot(gs[0, 2])
    colors3 = ['#FF6B6B' if m == 'DAG-WGAN' else '#F7DC6F' if m in ['SMOTE', 'ADASYN', 'BAGAN', 'ADASYN-GAN'] else '#F8C471' for m in methods]
    bars3 = ax3.bar(range(len(methods)), g_scores, alpha=0.9, color=colors3, edgecolor='black', linewidth=1)
    ax3.set_title('G-mean 对比分析', fontsize=16, fontweight='bold', pad=20)
    ax3.set_ylabel('G-mean', fontsize=14)
    ax3.set_xticks(range(len(methods)))
    ax3.set_xticklabels(methods, rotation=45, ha='right', fontsize=12)
    ax3.grid(True, alpha=0.3, axis='y')
    ax3.set_ylim(0, max(g_scores) * 1.15)

    # 添加数值标签和排名
    for i, (bar, score) in enumerate(zip(bars3, g_scores)):
        height = bar.get_height()
        rank = sorted(g_scores, reverse=True).index(score) + 1
        ax3.text(bar.get_x() + bar.get_width()/2., height + max(g_scores) * 0.02,
                f'{score:.3f}\n(#{rank})', ha='center', va='bottom', fontsize=10, fontweight='bold')

        if methods[i] == 'DAG-WGAN':
            ax3.text(bar.get_x() + bar.get_width()/2., height/2,
                    '★', ha='center', va='center', fontsize=20, color='white', fontweight='bold')

    # 综合性能雷达图 (下半部分左)
    ax4 = fig.add_subplot(gs[1:, 0], projection='polar')

    # 找到DAG-WGAN的索引
    dag_wgan_idx = methods.index('DAG-WGAN') if 'DAG-WGAN' in methods else -1

    if dag_wgan_idx != -1:
        # 选择主要对比方法
        comparison_methods = ['SMOTE', 'ADASYN', 'BAGAN', 'ADASYN-GAN', 'DAG-WGAN']
        comparison_indices = [i for i, m in enumerate(methods) if m in comparison_methods]

        angles = np.linspace(0, 2 * np.pi, 3, endpoint=False).tolist()
        angles += angles[:1]  # 闭合雷达图

        for idx in comparison_indices:
            if idx < len(methods):
                values = [f_scores[idx], auc_scores[idx], g_scores[idx]]
                values += values[:1]  # 闭合数据

                color = '#FF6B6B' if methods[idx] == 'DAG-WGAN' else plt.cm.Set3(idx)
                linewidth = 3 if methods[idx] == 'DAG-WGAN' else 2
                alpha = 0.8 if methods[idx] == 'DAG-WGAN' else 0.6

                ax4.plot(angles, values, 'o-', linewidth=linewidth,
                        label=methods[idx], color=color, alpha=alpha)
                ax4.fill(angles, values, alpha=0.1, color=color)

        ax4.set_xticks(angles[:-1])
        ax4.set_xticklabels(['F1-Score', 'AUC', 'G-mean'], fontsize=12)
        ax4.set_ylim(0, 1)
        ax4.set_title('综合性能雷达图\n(DAG-WGAN vs 核心对比方法)', fontsize=14, fontweight='bold', pad=20)
        ax4.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))

    # 性能提升分析 (下半部分右)
    ax5 = fig.add_subplot(gs[1:, 1:])
    ax5.axis('off')

    # 计算DAG-WGAN相对于其他方法的性能提升
    if dag_wgan_idx != -1:
        dag_f1 = f_scores[dag_wgan_idx]
        dag_auc = auc_scores[dag_wgan_idx]
        dag_gmean = g_scores[dag_wgan_idx]

        # 计算相对于主要对比方法的提升
        improvements = {}
        for method in ['SMOTE', 'ADASYN', 'BAGAN', 'ADASYN-GAN']:
            if method in methods:
                idx = methods.index(method)
                f1_imp = ((dag_f1 - f_scores[idx]) / f_scores[idx] * 100) if f_scores[idx] > 0 else 0
                auc_imp = ((dag_auc - auc_scores[idx]) / auc_scores[idx] * 100) if auc_scores[idx] > 0 else 0
                gmean_imp = ((dag_gmean - g_scores[idx]) / g_scores[idx] * 100) if g_scores[idx] > 0 else 0
                improvements[method] = {'F1': f1_imp, 'AUC': auc_imp, 'G-mean': gmean_imp}

        # 创建性能提升表格
        table_data = []
        table_data.append(['对比方法', 'F1-Score提升', 'AUC提升', 'G-mean提升'])
        table_data.append(['', '(%)', '(%)', '(%)'])

        for method, imps in improvements.items():
            row = [method,
                   f"{imps['F1']:+.1f}%" if imps['F1'] != 0 else "0.0%",
                   f"{imps['AUC']:+.1f}%" if imps['AUC'] != 0 else "0.0%",
                   f"{imps['G-mean']:+.1f}%" if imps['G-mean'] != 0 else "0.0%"]
            table_data.append(row)

        # 添加平均提升
        avg_f1 = np.mean([imps['F1'] for imps in improvements.values()])
        avg_auc = np.mean([imps['AUC'] for imps in improvements.values()])
        avg_gmean = np.mean([imps['G-mean'] for imps in improvements.values()])
        table_data.append(['', '', '', ''])
        table_data.append(['平均提升', f"{avg_f1:+.1f}%", f"{avg_auc:+.1f}%", f"{avg_gmean:+.1f}%"])

        # 绘制表格
        table = ax5.table(cellText=table_data[2:], colLabels=table_data[0],
                         cellLoc='center', loc='center',
                         colWidths=[0.25, 0.25, 0.25, 0.25])
        table.auto_set_font_size(False)
        table.set_fontsize(12)
        table.scale(1, 2)

        # 设置表格样式
        for i in range(len(table_data[0])):
            table[(0, i)].set_facecolor('#4ECDC4')
            table[(0, i)].set_text_props(weight='bold', color='white')

        # 突出显示最后一行（平均提升）
        for i in range(len(table_data[0])):
            table[(len(table_data)-3, i)].set_facecolor('#FFE5B4')
            table[(len(table_data)-3, i)].set_text_props(weight='bold')

        ax5.set_title('DAG-WGAN性能提升分析\n(相对于SMOTE、ADASYN、BAGAN、ADASYN-GAN)',
                     fontsize=14, fontweight='bold', y=0.95)

    plt.suptitle('Car数据集: F1-Score、G-mean、AUC综合性评估指标对比分析\nDAG-WGAN vs SMOTE、ADASYN、BAGAN、ADASYN-GAN算法',
                fontsize=18, fontweight='bold', y=0.98)
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()

    print(f"📊 F1-Score、G-mean、AUC综合性能对比图保存至: {save_path}")

    # 打印详细的性能对比分析
    print(f"\n{'='*80}")
    print(f"🎯 F1-Score、G-mean、AUC综合性评估指标详细对比分析")
    print(f"{'='*80}")

    if dag_wgan_idx != -1:
        print(f"\n📈 DAG-WGAN性能表现:")
        print(f"  F1-Score: {f_scores[dag_wgan_idx]:.4f} (排名: #{sorted(f_scores, reverse=True).index(f_scores[dag_wgan_idx]) + 1}/{len(f_scores)})")
        print(f"  AUC:      {auc_scores[dag_wgan_idx]:.4f} (排名: #{sorted(auc_scores, reverse=True).index(auc_scores[dag_wgan_idx]) + 1}/{len(auc_scores)})")
        print(f"  G-mean:   {g_scores[dag_wgan_idx]:.4f} (排名: #{sorted(g_scores, reverse=True).index(g_scores[dag_wgan_idx]) + 1}/{len(g_scores)})")

        print(f"\n🔍 与核心对比方法的详细比较:")
        for method in ['SMOTE', 'ADASYN', 'BAGAN', 'ADASYN-GAN']:
            if method in methods:
                idx = methods.index(method)
                print(f"\n  vs {method}:")
                print(f"    F1-Score: {f_scores[dag_wgan_idx]:.4f} vs {f_scores[idx]:.4f} ({f_scores[dag_wgan_idx] - f_scores[idx]:+.4f})")
                print(f"    AUC:      {auc_scores[dag_wgan_idx]:.4f} vs {auc_scores[idx]:.4f} ({auc_scores[dag_wgan_idx] - auc_scores[idx]:+.4f})")
                print(f"    G-mean:   {g_scores[dag_wgan_idx]:.4f} vs {g_scores[idx]:.4f} ({g_scores[dag_wgan_idx] - g_scores[idx]:+.4f})")

    print(f"\n{'='*80}")


def analyze_three_key_metrics(all_results):
    """
    专门分析F1-Score、G-mean、AUC三个综合性评估指标
    重点对比DAG-WGAN与SMOTE、ADASYN、BAGAN、ADASYN-GAN的性能
    """
    print(f"\n{'='*100}")
    print(f"🎯 F1-Score、G-mean、AUC三个综合性评估指标深度分析")
    print(f"{'='*100}")

    # 核心对比方法
    core_methods = ['SMOTE (k=5)', 'ADASYN (default)', 'BAGAN', 'ADASYN-GAN', 'DAG-WGAN']
    method_display = {
        'SMOTE (k=5)': 'SMOTE',
        'ADASYN (default)': 'ADASYN',
        'BAGAN': 'BAGAN',
        'ADASYN-GAN': 'ADASYN-GAN',
        'DAG-WGAN': 'DAG-WGAN'
    }

    # 收集核心方法的数据
    core_data = {}
    for method in core_methods:
        if method in all_results and 'error' not in all_results[method]:
            result = all_results[method]
            core_data[method_display[method]] = {
                'F1-Score': result['f_measure'],
                'AUC': result['auc'],
                'G-mean': result['g_means']
            }

    if not core_data:
        print("❌ 没有找到核心对比方法的数据")
        return

    # 1. 各指标排名分析
    print(f"\n📊 各指标排名分析:")
    print(f"{'方法':<15} {'F1-Score':<12} {'排名':<6} {'AUC':<12} {'排名':<6} {'G-mean':<12} {'排名':<6}")
    print(f"{'-'*85}")

    # 计算排名
    f1_scores = [(method, data['F1-Score']) for method, data in core_data.items()]
    auc_scores = [(method, data['AUC']) for method, data in core_data.items()]
    gmean_scores = [(method, data['G-mean']) for method, data in core_data.items()]

    f1_ranked = sorted(f1_scores, key=lambda x: x[1], reverse=True)
    auc_ranked = sorted(auc_scores, key=lambda x: x[1], reverse=True)
    gmean_ranked = sorted(gmean_scores, key=lambda x: x[1], reverse=True)

    for method in core_data.keys():
        f1_val = core_data[method]['F1-Score']
        auc_val = core_data[method]['AUC']
        gmean_val = core_data[method]['G-mean']

        f1_rank = next(i+1 for i, (m, _) in enumerate(f1_ranked) if m == method)
        auc_rank = next(i+1 for i, (m, _) in enumerate(auc_ranked) if m == method)
        gmean_rank = next(i+1 for i, (m, _) in enumerate(gmean_ranked) if m == method)

        # 突出显示DAG-WGAN
        marker = "🏆" if method == "DAG-WGAN" else "  "
        print(f"{marker}{method:<13} {f1_val:<12.4f} #{f1_rank:<5} {auc_val:<12.4f} #{auc_rank:<5} {gmean_val:<12.4f} #{gmean_rank:<5}")

    return core_data


def create_performance_visualization(baseline_results, dag_wgan_results, save_path):
    """创建性能对比可视化（保留原函数以兼容）"""
    metrics = ['F-measure', 'AUC', 'G-means']
    baseline_vals = [baseline_results['f_measure'], baseline_results['auc'], baseline_results['g_means']]
    dag_wgan_vals = [dag_wgan_results['f_measure'], dag_wgan_results['auc'], dag_wgan_results['g_means']]

    x = np.arange(len(metrics))
    width = 0.35

    fig, ax = plt.subplots(figsize=(10, 6))

    bars1 = ax.bar(x - width/2, baseline_vals, width, label='基线方法', alpha=0.8, color='skyblue')
    bars2 = ax.bar(x + width/2, dag_wgan_vals, width, label='DAG-WGAN', alpha=0.8, color='lightcoral')

    ax.set_xlabel('评价指标')
    ax.set_ylabel('分数')
    ax.set_title('Car数据集: DAG-WGAN vs 基线方法性能对比')
    ax.set_xticks(x)
    ax.set_xticklabels(metrics)
    ax.legend()
    ax.grid(True, alpha=0.3)

    # 添加数值标签
    def add_value_labels(bars):
        for bar in bars:
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                   f'{height:.3f}', ha='center', va='bottom')

    add_value_labels(bars1)
    add_value_labels(bars2)

    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()

    print(f"性能对比图保存至: {save_path}")


def main():
    """主函数"""
    print("=== Car数据集 DAG-WGAN vs 七种最先进基线方法对比 ===\n")

    # 创建输出目录
    output_dir = "car_dataset_comprehensive_results"
    os.makedirs(output_dir, exist_ok=True)

    # 1. 加载和预处理数据
    X, y, feature_names, label_info = load_and_preprocess_car_data()

    # 2. 数据分割
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.3, random_state=42, stratify=y
    )

    print(f"\n数据分割:")
    print(f"  训练集: {len(X_train)} 样本")
    print(f"  测试集: {len(X_test)} 样本")
    print(f"  训练集少数类比例: {np.sum(y_train==1)/len(y_train)*100:.1f}%")
    print(f"  测试集少数类比例: {np.sum(y_test==1)/len(y_test)*100:.1f}%")

    # 3. 基线评估(无过采样)
    logger.info("进行基线评估...")
    baseline_results = evaluate_classifier_comprehensive(
        X_train, y_train, X_test, y_test, X_generated=None, method_name="基线方法(无过采样)"
    )

    # 4. 七种最先进基线方法评估
    logger.info("评估七种最先进基线方法...")
    comparator = BaselineComparator(random_state=42)

    all_results = {
        'baseline_no_sampling': baseline_results
    }

    baseline_methods = [
        ('SMOTE (k=5)', comparator.smote_baseline),
        ('ADASYN (default)', comparator.adasyn_baseline),
        ('WGAN-GP (λ=10)', comparator.wgan_gp_baseline),
        ('BAGAN', comparator.bagan_baseline),
        ('ADASYN-GAN', comparator.adasyn_gan_baseline),
        ('Density-Weighted WGAN-GP', comparator.density_weighted_wgan_gp_baseline)
    ]

    # 评估每种基线方法
    for method_name, method_func in baseline_methods:
        try:
            logger.info(f"评估 {method_name}...")

            if method_name == 'Focal Loss (α=0.25, γ=2)':
                # Focal Loss需要特殊处理
                y_pred, y_pred_proba = comparator.focal_loss_baseline(X_train, y_train, X_test, y_test)

                # 计算指标
                f_measure = f1_score(y_test, y_pred)
                auc_score = roc_auc_score(y_test, y_pred_proba)
                g_means = calculate_gmean(y_test, y_pred)

                tn, fp, fn, tp = confusion_matrix(y_test, y_pred).ravel()

                method_results = {
                    'f_measure': f_measure,
                    'auc': auc_score,
                    'g_means': g_means,
                    'precision': tp / (tp + fp) if (tp + fp) > 0 else 0,
                    'recall': tp / (tp + fn) if (tp + fn) > 0 else 0,
                    'specificity': tn / (tn + fp) if (tn + fp) > 0 else 0,
                    'tp': tp, 'tn': tn, 'fp': fp, 'fn': fn
                }
            else:
                # 其他方法使用过采样
                X_resampled, y_resampled = method_func(X_train, y_train)
                method_results = evaluate_classifier_comprehensive(
                    X_resampled, y_resampled, X_test, y_test, X_generated=None, method_name=method_name
                )

            all_results[method_name] = method_results

        except Exception as e:
            logger.error(f"评估 {method_name} 时出错: {e}")
            all_results[method_name] = {'error': str(e)}

    # 单独评估Focal Loss
    try:
        logger.info("评估 Focal Loss (α=0.25, γ=2)...")
        y_pred, y_pred_proba = comparator.focal_loss_baseline(X_train, y_train, X_test, y_test)

        f_measure = f1_score(y_test, y_pred)
        auc_score = roc_auc_score(y_test, y_pred_proba)
        g_means = calculate_gmean(y_test, y_pred)
        tn, fp, fn, tp = confusion_matrix(y_test, y_pred).ravel()

        focal_results = {
            'f_measure': f_measure,
            'auc': auc_score,
            'g_means': g_means,
            'precision': tp / (tp + fp) if (tp + fp) > 0 else 0,
            'recall': tp / (tp + fn) if (tp + fn) > 0 else 0,
            'specificity': tn / (tn + fp) if (tn + fp) > 0 else 0,
            'tp': tp, 'tn': tn, 'fp': fp, 'fn': fn
        }

        all_results['Focal Loss (α=0.25, γ=2)'] = focal_results
        print(f"\nFocal Loss (α=0.25, γ=2) 评估结果:")
        print(f"  F-measure: {f_measure:.4f}")
        print(f"  AUC: {auc_score:.4f}")
        print(f"  G-means: {g_means:.4f}")

    except Exception as e:
        logger.error(f"评估 Focal Loss 时出错: {e}")
        all_results['Focal Loss (α=0.25, γ=2)'] = {'error': str(e)}
    
    # 5. DAG-WGAN处理
    logger.info("初始化DAG-WGAN框架...")

    # 使用默认配置
    config = get_default_config()

    framework_config = {
        'alpha': config.density.alpha,
        'k_neighbors': config.density.k_neighbors,
        'beta': config.synthesis.beta,
        'lr_g': config.wgan.eta_g,
        'lr_d': config.wgan.eta_d,
        'lambda_gp': config.wgan.lambda_gp,
        'n_critic': config.wgan.n_critic,
        'device': config.wgan.device,
        'latent_dim': config.wgan.latent_dim,
        'nu': config.wgan.nu,
        'tau': config.wgan.tau
    }

    dag_wgan = DAGWGANFramework(framework_config)
    
    try:
        # 训练DAG-WGAN
        logger.info("训练DAG-WGAN框架...")
        training_result = dag_wgan.fit(X_train, y_train, epochs=100)
        
        if training_result['status'] == 'success':
            print(f"\nDAG-WGAN训练成功!")
            print(f"  ADASYN样本生成数: {training_result['history']['adasyn_samples']}")
            
            # 生成平衡数据集
            logger.info("生成平衡数据集...")
            X_balanced, y_balanced = dag_wgan.generate_balanced_dataset(X_train, y_train)
            
            print(f"\n平衡数据集统计:")
            print(f"  总样本数: {len(X_balanced)}")
            print(f"  类别分布: {np.bincount(y_balanced)}")
            print(f"  平衡比例: {np.sum(y_balanced==0)/np.sum(y_balanced==1):.2f}:1")
            
            # DAG-WGAN评估
            logger.info("评估DAG-WGAN性能...")
            # 获取生成的样本用于HDOR计算
            try:
                X_wgan_generated = dag_wgan.wgan.generate_samples(100)
                X_generated_samples = X_wgan_generated if len(X_wgan_generated) > 0 else None
            except:
                X_generated_samples = None

            dag_wgan_results = evaluate_classifier_comprehensive(
                X_balanced, y_balanced, X_test, y_test,
                X_generated=X_generated_samples, method_name="DAG-WGAN",
                training_time=training_result['history'].get('training_time', 0)/3600,  # 转换为小时
                loss_history=training_result['history']
            )

            all_results['DAG-WGAN'] = dag_wgan_results

            # 6. 综合结果对比
            print("\n" + "="*80)
            print("Car数据集: DAG-WGAN vs 七种最先进基线方法 - 综合结果对比")
            print("="*80)

            # 创建结果表格
            methods = ['baseline_no_sampling', 'SMOTE (k=5)', 'ADASYN (default)',
                      'Focal Loss (α=0.25, γ=2)', 'WGAN-GP (λ=10)', 'BAGAN',
                      'ADASYN-GAN', 'Density-Weighted WGAN-GP', 'DAG-WGAN']

            method_display_names = {
                'baseline_no_sampling': '无过采样基线',
                'SMOTE (k=5)': 'SMOTE (k=5)',
                'ADASYN (default)': 'ADASYN (默认)',
                'Focal Loss (α=0.25, γ=2)': 'Focal Loss',
                'WGAN-GP (λ=10)': 'WGAN-GP (λ=10)',
                'BAGAN': 'BAGAN',
                'ADASYN-GAN': 'ADASYN-GAN',
                'Density-Weighted WGAN-GP': 'Density-WGAN-GP',
                'DAG-WGAN': 'DAG-WGAN'
            }

            # 5.2节标准结果表格
            print(f"{'方法':<25} {'F1分数':<10} {'BCI':<8} {'HDOR':<8} {'时间(h)':<8} {'AUC':<8} {'G-means':<8}")
            print("-" * 85)

            # 5.2节标准最佳分数跟踪
            best_scores = {'f_measure': 0, 'auc': 0, 'g_means': 0, 'bci': float('inf'), 'hdor': 0}
            best_methods = {'f_measure': '', 'auc': '', 'g_means': '', 'bci': '', 'hdor': ''}

            for method in methods:
                if method in all_results and 'error' not in all_results[method]:
                    result = all_results[method]
                    display_name = method_display_names.get(method, method)

                    f_score = result['f_measure']
                    auc_score = result['auc']
                    g_score = result['g_means']
                    bci_score = result.get('boundary_confusion_index', 0.0)
                    hdor_score = result.get('high_density_overlap_rate', 0.0)
                    training_time = result.get('training_time_hours', 0.0)

                    # 跟踪最佳分数 (5.2节标准)
                    if f_score > best_scores['f_measure']:
                        best_scores['f_measure'] = f_score
                        best_methods['f_measure'] = display_name
                    if auc_score > best_scores['auc']:
                        best_scores['auc'] = auc_score
                        best_methods['auc'] = display_name
                    if g_score > best_scores['g_means']:
                        best_scores['g_means'] = g_score
                        best_methods['g_means'] = display_name
                    if bci_score < best_scores['bci']:  # BCI越低越好
                        best_scores['bci'] = bci_score
                        best_methods['bci'] = display_name
                    if hdor_score > best_scores['hdor']:  # HDOR越高越好
                        best_scores['hdor'] = hdor_score
                        best_methods['hdor'] = display_name

                    print(f"{display_name:<25} {f_score:<10.4f} {bci_score:<8.4f} {hdor_score:<8.4f} {training_time:<8.4f} {auc_score:<8.4f} {g_score:<8.4f}")
                else:
                    display_name = method_display_names.get(method, method)
                    print(f"{display_name:<25} {'ERROR':<10} {'ERROR':<8} {'ERROR':<8} {'ERROR':<8} {'ERROR':<8} {'ERROR':<8}")

            print("\n" + "="*85)
            print("🏆 最佳性能方法 (5.2节评估标准):")
            print(f"  少数类F1分数: {best_methods['f_measure']} ({best_scores['f_measure']:.4f})")
            print(f"  边界混淆指数(BCI): {best_methods['bci']} ({best_scores['bci']:.4f}) - 越低越好")
            print(f"  高密度重叠率(HDOR): {best_methods['hdor']} ({best_scores['hdor']:.4f}) - 越高越好")
            print(f"  AUC: {best_methods['auc']} ({best_scores['auc']:.4f})")
            print(f"  G-means: {best_methods['g_means']} ({best_scores['g_means']:.4f})")

            # DAG-WGAN排名分析
            dag_wgan_rank = {}
            for metric in ['f_measure', 'auc', 'g_means']:
                scores = []
                for method in methods:
                    if method in all_results and 'error' not in all_results[method]:
                        scores.append(all_results[method][metric])

                scores.sort(reverse=True)
                dag_wgan_score = all_results['DAG-WGAN'][metric]
                rank = scores.index(dag_wgan_score) + 1
                dag_wgan_rank[metric] = rank

            print(f"\nDAG-WGAN排名:")
            print(f"  F-measure: 第{dag_wgan_rank['f_measure']}名 / {len([m for m in methods if m in all_results and 'error' not in all_results[m]])}个方法")
            print(f"  AUC: 第{dag_wgan_rank['auc']}名 / {len([m for m in methods if m in all_results and 'error' not in all_results[m]])}个方法")
            print(f"  G-means: 第{dag_wgan_rank['g_means']}名 / {len([m for m in methods if m in all_results and 'error' not in all_results[m]])}个方法")
            
            # 7. 生成综合可视化
            logger.info("生成综合性能对比可视化...")
            viz_path = os.path.join(output_dir, 'comprehensive_comparison.png')
            create_comprehensive_visualization(all_results, viz_path)

            # 8. 进行F1-Score、G-mean、AUC三个指标的深度分析
            logger.info("进行F1-Score、G-mean、AUC三个综合性评估指标深度分析...")
            analyze_three_key_metrics(all_results)

            # 9. 生成训练损失可视化
            try:
                logger.info("生成训练损失可视化...")

                # 获取DAG-WGAN的损失历史
                if hasattr(dag_wgan, 'wgan') and hasattr(dag_wgan.wgan, 'generator_losses'):
                    generator_losses = dag_wgan.wgan.generator_losses
                    discriminator_losses = dag_wgan.wgan.discriminator_losses

                    if len(generator_losses) > 0 and len(discriminator_losses) > 0:
                        # 生成分离的损失图
                        loss_path = os.path.join(output_dir, 'training_losses.png')
                        plot_training_losses(generator_losses, discriminator_losses, loss_path)

                        # 生成组合损失图
                        combined_loss_path = os.path.join(output_dir, 'combined_losses.png')
                        plot_combined_losses(generator_losses, discriminator_losses, combined_loss_path)
                    else:
                        logger.warning("损失历史为空，跳过损失可视化")
                else:
                    logger.warning("无法获取损失历史，跳过损失可视化")

            except Exception as e:
                logger.warning(f"训练损失可视化生成失败: {e}")

            # 8. 生成散点图对比
            try:
                logger.info("生成散点图对比...")

                # 准备各种方法的数据
                X_original = X_train
                y_original = y_train

                # SMOTE数据
                smote = SMOTE(k_neighbors=5, random_state=42)
                X_smote, y_smote = smote.fit_resample(X_train, y_train)

                # ADASYN数据
                adasyn = ADASYN(random_state=42)
                X_adasyn, y_adasyn = adasyn.fit_resample(X_train, y_train)

                # GAN生成数据 (使用简单WGAN-GP)
                simple_gan = SimpleWGAN_GP(X_train.shape[1], device='cpu')
                minority_samples = X_train[y_train == 1]
                if len(minority_samples) > 0:
                    simple_gan.train(minority_samples, epochs=50)
                    n_generate = len(minority_samples)
                    X_gan_generated = simple_gan.generate_samples(n_generate)
                    X_gan = np.vstack([X_train, X_gan_generated])
                    y_gan = np.hstack([y_train, np.ones(n_generate)])
                else:
                    X_gan, y_gan = X_train, y_train

                # DAG-WGAN数据
                X_dag_adasyn = dag_wgan.synthesis_layer.density_guided_adasyn(X_train, y_train)
                X_dag_wgan_generated = dag_wgan.wgan.generate_samples(min(len(X_dag_adasyn), 200))
                if len(X_dag_wgan_generated) > 0:
                    X_dag_wgan = np.vstack([X_dag_adasyn, X_dag_wgan_generated])
                    y_dag_wgan = np.hstack([np.ones(len(X_dag_adasyn)), np.ones(len(X_dag_wgan_generated))])
                    X_dag_wgan = np.vstack([X_train, X_dag_wgan])
                    y_dag_wgan = np.hstack([y_train, y_dag_wgan])
                else:
                    X_dag_wgan, y_dag_wgan = X_dag_adasyn, np.ones(len(X_dag_adasyn))
                    X_dag_wgan = np.vstack([X_train, X_dag_wgan])
                    y_dag_wgan = np.hstack([y_train, y_dag_wgan])

                # 生成PCA散点图
                scatter_path = os.path.join(output_dir, 'scatter_plots_comparison.png')
                create_scatter_plots(X_original, y_original, X_smote, y_smote,
                                   X_adasyn, y_adasyn, X_gan, y_gan,
                                   X_dag_wgan, y_dag_wgan, scatter_path)

                # 生成t-SNE可视化
                tsne_path = os.path.join(output_dir, 'tsne_visualization.png')
                create_tsne_visualization(X_original, y_original, X_smote, y_smote,
                                        X_adasyn, y_adasyn, X_gan, y_gan,
                                        X_dag_wgan, y_dag_wgan, tsne_path)

                # 生成t-SNE决策边界可视化
                tsne_decision_path = os.path.join(output_dir, 'tsne_decision_boundaries.png')
                create_tsne_decision_boundaries(X_original, y_original, X_smote, y_smote,
                                              X_adasyn, y_adasyn, X_gan, y_gan,
                                              X_dag_wgan, y_dag_wgan, tsne_decision_path)

            except Exception as e:
                logger.warning(f"散点图可视化生成失败: {e}")

            # 9. DAG-WGAN参数敏感性分析
            try:
                logger.info("进行DAG-WGAN参数敏感性分析...")
                sensitivity_path = os.path.join(output_dir, 'dag_wgan_parameter_sensitivity.png')
                sensitivity_results = analyze_dag_wgan_parameter_sensitivity(X_train, y_train, X_test, y_test, sensitivity_path)

                # 将敏感性分析结果添加到总结中
                results_summary['sensitivity_analysis'] = sensitivity_results

            except Exception as e:
                logger.warning(f"参数敏感性分析失败: {e}")

            # 10. 保存详细结果
            results_summary = {
                'dataset_info': label_info,
                'all_method_results': all_results,
                'training_info': training_result,
                'best_scores': best_scores,
                'best_methods': best_methods,
                'dag_wgan_rankings': dag_wgan_rank,
                'timestamp': datetime.now().isoformat()
            }
            
            import json
            results_path = os.path.join(output_dir, 'detailed_results.json')
            with open(results_path, 'w', encoding='utf-8') as f:
                json.dump(results_summary, f, indent=2, ensure_ascii=False, default=str)
            
            print(f"\n🎉 Car数据集综合实验完成!")
            print(f"📊 结果保存至: {output_dir}")
            print(f"📈 综合性能对比图: comprehensive_comparison.png")
            print(f"📉 训练损失变化图: training_losses.png")
            print(f"📊 组合损失对比图: combined_losses.png")
            print(f"🔍 散点图对比: scatter_plots_comparison.png")
            print(f"🎯 t-SNE可视化: tsne_visualization.png")
            print(f"🎯 t-SNE决策边界可视化: tsne_decision_boundaries.png")
            print(f"📊 DAG-WGAN参数敏感性分析: dag_wgan_parameter_sensitivity.png")
            print(f"📋 详细结果数据: detailed_results.json")
            print(f"📋 详细结果: detailed_results.json")
            print(f"\n📋 实验总结:")
            print(f"  ✅ 评估了8种方法(包括DAG-WGAN)")
            print(f"  🎯 重点对比: DAG-WGAN vs SMOTE、ADASYN、BAGAN、ADASYN-GAN")
            print(f"  📊 综合性评估指标: F1-Score, G-mean, AUC")
            print(f"  📈 包含性能提升分析、排名对比、雷达图可视化")
            print(f"  ✅ 随机森林分类器(默认参数)")
            print(f"  ✅ 完整的统计分析和增强可视化")
            
        else:
            print(f"DAG-WGAN训练失败: {training_result.get('reason', '未知错误')}")
            
    except Exception as e:
        logger.error(f"实验过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
