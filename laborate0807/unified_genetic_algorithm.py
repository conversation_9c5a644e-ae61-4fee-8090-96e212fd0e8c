"""
统一遗传算法模块

实现论文中描述的遗传算法配置:
- 锦标赛选择(规模=3)
- SBX交叉(η_c=15)  
- 多项式变异(η_m=20)
- 种群规模: 50
- 运行代数: 50

作者: 研究团队
日期: 2024-08-07
"""

import numpy as np
import random
from typing import List, Tuple, Dict, Callable, Optional
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)


@dataclass
class UnifiedGAConfig:
    """统一遗传算法配置"""
    population_size: int = 50          # 种群规模
    max_generations: int = 50          # 最大代数
    tournament_size: int = 3           # 锦标赛选择规模
    eta_c: float = 15.0               # SBX交叉分布指数
    eta_m: float = 20.0               # 多项式变异分布指数
    crossover_prob: float = 0.9       # 交叉概率
    mutation_prob: float = 0.1        # 变异概率
    elite_size: int = 2               # 精英保留数量
    random_state: int = 42            # 随机种子


class Individual:
    """个体类"""
    
    def __init__(self, genes: np.ndarray):
        """
        初始化个体
        
        参数:
            genes: 基因数组(参数值)
        """
        self.genes = genes.copy()
        self.fitness = None
        self.objectives = None  # 多目标值
        
    def copy(self):
        """复制个体"""
        new_individual = Individual(self.genes)
        new_individual.fitness = self.fitness
        new_individual.objectives = self.objectives
        return new_individual


class UnifiedGeneticAlgorithm:
    """
    统一遗传算法实现
    采用论文中指定的算法配置
    """
    
    def __init__(self, config: UnifiedGAConfig, parameter_bounds: Dict[str, List]):
        """
        初始化遗传算法
        
        参数:
            config: GA配置
            parameter_bounds: 参数边界字典 {param_name: [min, max, type, log_scale]}
        """
        self.config = config
        self.parameter_bounds = parameter_bounds
        self.parameter_names = list(parameter_bounds.keys())
        self.n_parameters = len(self.parameter_names)
        
        # 设置随机种子
        random.seed(config.random_state)
        np.random.seed(config.random_state)
        
        # 初始化种群
        self.population = []
        self.generation = 0
        self.best_individual = None
        self.fitness_history = []
        
    def initialize_population(self) -> List[Individual]:
        """
        初始化种群
        
        返回:
            初始种群
        """
        population = []
        
        for _ in range(self.config.population_size):
            genes = self._generate_random_individual()
            individual = Individual(genes)
            population.append(individual)
        
        logger.info(f"初始化种群: {self.config.population_size}个个体")
        return population
    
    def _generate_random_individual(self) -> np.ndarray:
        """生成随机个体"""
        genes = np.zeros(self.n_parameters)
        
        for i, param_name in enumerate(self.parameter_names):
            bounds = self.parameter_bounds[param_name]
            min_val, max_val, param_type, log_scale = bounds
            
            if log_scale:
                # 对数尺度参数
                log_min = np.log10(min_val)
                log_max = np.log10(max_val)
                log_val = np.random.uniform(log_min, log_max)
                genes[i] = 10 ** log_val
            else:
                # 线性尺度参数
                if param_type == 'integer':
                    genes[i] = np.random.randint(min_val, max_val + 1)
                else:
                    genes[i] = np.random.uniform(min_val, max_val)
        
        return genes
    
    def tournament_selection(self, population: List[Individual]) -> Individual:
        """
        锦标赛选择(规模=3)
        
        参数:
            population: 当前种群
            
        返回:
            选中的个体
        """
        tournament_individuals = random.sample(population, self.config.tournament_size)
        
        # 选择适应度最高的个体
        best_individual = max(tournament_individuals, key=lambda x: x.fitness or -float('inf'))
        
        return best_individual.copy()
    
    def sbx_crossover(self, parent1: Individual, parent2: Individual) -> Tuple[Individual, Individual]:
        """
        SBX交叉(η_c=15)
        
        参数:
            parent1: 父代1
            parent2: 父代2
            
        返回:
            两个子代
        """
        if random.random() > self.config.crossover_prob:
            return parent1.copy(), parent2.copy()
        
        child1_genes = parent1.genes.copy()
        child2_genes = parent2.genes.copy()
        
        for i in range(self.n_parameters):
            if random.random() <= 0.5:  # 基因交叉概率
                bounds = self.parameter_bounds[self.parameter_names[i]]
                min_val, max_val, param_type, log_scale = bounds
                
                y1 = min(parent1.genes[i], parent2.genes[i])
                y2 = max(parent1.genes[i], parent2.genes[i])
                
                if abs(y2 - y1) > 1e-14:
                    # SBX交叉计算
                    rand = random.random()
                    
                    if rand <= 0.5:
                        beta = (2.0 * rand) ** (1.0 / (self.config.eta_c + 1.0))
                    else:
                        beta = (1.0 / (2.0 * (1.0 - rand))) ** (1.0 / (self.config.eta_c + 1.0))
                    
                    child1_val = 0.5 * ((y1 + y2) - beta * (y2 - y1))
                    child2_val = 0.5 * ((y1 + y2) + beta * (y2 - y1))
                    
                    # 边界检查
                    child1_val = max(min_val, min(max_val, child1_val))
                    child2_val = max(min_val, min(max_val, child2_val))
                    
                    # 整数类型处理
                    if param_type == 'integer':
                        child1_val = int(round(child1_val))
                        child2_val = int(round(child2_val))
                    
                    child1_genes[i] = child1_val
                    child2_genes[i] = child2_val
        
        return Individual(child1_genes), Individual(child2_genes)
    
    def polynomial_mutation(self, individual: Individual) -> Individual:
        """
        多项式变异(η_m=20)
        
        参数:
            individual: 待变异个体
            
        返回:
            变异后的个体
        """
        if random.random() > self.config.mutation_prob:
            return individual.copy()
        
        mutated_genes = individual.genes.copy()
        
        for i in range(self.n_parameters):
            if random.random() <= (1.0 / self.n_parameters):  # 基因变异概率
                bounds = self.parameter_bounds[self.parameter_names[i]]
                min_val, max_val, param_type, log_scale = bounds
                
                y = individual.genes[i]
                delta1 = (y - min_val) / (max_val - min_val)
                delta2 = (max_val - y) / (max_val - min_val)
                
                rand = random.random()
                mut_pow = 1.0 / (self.config.eta_m + 1.0)
                
                if rand <= 0.5:
                    xy = 1.0 - delta1
                    val = 2.0 * rand + (1.0 - 2.0 * rand) * (xy ** (self.config.eta_m + 1.0))
                    deltaq = val ** mut_pow - 1.0
                else:
                    xy = 1.0 - delta2
                    val = 2.0 * (1.0 - rand) + 2.0 * (rand - 0.5) * (xy ** (self.config.eta_m + 1.0))
                    deltaq = 1.0 - val ** mut_pow
                
                mutated_val = y + deltaq * (max_val - min_val)
                
                # 边界检查
                mutated_val = max(min_val, min(max_val, mutated_val))
                
                # 整数类型处理
                if param_type == 'integer':
                    mutated_val = int(round(mutated_val))
                
                mutated_genes[i] = mutated_val
        
        return Individual(mutated_genes)
    
    def evaluate_population(self, population: List[Individual], 
                          fitness_function: Callable[[np.ndarray], float]):
        """
        评估种群适应度
        
        参数:
            population: 种群
            fitness_function: 适应度函数
        """
        for individual in population:
            if individual.fitness is None:
                try:
                    individual.fitness = fitness_function(individual.genes)
                except Exception as e:
                    logger.warning(f"适应度评估失败: {e}")
                    individual.fitness = -float('inf')
    
    def select_elite(self, population: List[Individual]) -> List[Individual]:
        """
        精英选择
        
        参数:
            population: 当前种群
            
        返回:
            精英个体列表
        """
        sorted_population = sorted(population, key=lambda x: x.fitness or -float('inf'), reverse=True)
        return sorted_population[:self.config.elite_size]
    
    def evolve_generation(self, population: List[Individual]) -> List[Individual]:
        """
        进化一代
        
        参数:
            population: 当前种群
            
        返回:
            新一代种群
        """
        new_population = []
        
        # 精英保留
        elite = self.select_elite(population)
        new_population.extend([ind.copy() for ind in elite])
        
        # 生成剩余个体
        while len(new_population) < self.config.population_size:
            # 选择
            parent1 = self.tournament_selection(population)
            parent2 = self.tournament_selection(population)
            
            # 交叉
            child1, child2 = self.sbx_crossover(parent1, parent2)
            
            # 变异
            child1 = self.polynomial_mutation(child1)
            child2 = self.polynomial_mutation(child2)
            
            new_population.extend([child1, child2])
        
        # 确保种群大小
        return new_population[:self.config.population_size]
    
    def optimize(self, fitness_function: Callable[[np.ndarray], float]) -> Tuple[Individual, List[float]]:
        """
        执行遗传算法优化
        
        参数:
            fitness_function: 适应度函数
            
        返回:
            最佳个体和适应度历史
        """
        logger.info("开始遗传算法优化")
        logger.info(f"配置: 种群{self.config.population_size}, 代数{self.config.max_generations}")
        logger.info(f"锦标赛选择(size={self.config.tournament_size}), SBX交叉(η_c={self.config.eta_c}), 多项式变异(η_m={self.config.eta_m})")
        
        # 初始化种群
        self.population = self.initialize_population()
        
        for generation in range(self.config.max_generations):
            self.generation = generation
            
            # 评估适应度
            self.evaluate_population(self.population, fitness_function)
            
            # 记录最佳个体
            current_best = max(self.population, key=lambda x: x.fitness or -float('inf'))
            if self.best_individual is None or current_best.fitness > self.best_individual.fitness:
                self.best_individual = current_best.copy()
            
            # 记录适应度历史
            avg_fitness = np.mean([ind.fitness for ind in self.population if ind.fitness is not None])
            self.fitness_history.append(avg_fitness)
            
            # 日志输出
            if generation % 10 == 0:
                logger.info(f"第{generation}代: 最佳适应度={self.best_individual.fitness:.4f}, "
                           f"平均适应度={avg_fitness:.4f}")
            
            # 进化到下一代
            if generation < self.config.max_generations - 1:
                self.population = self.evolve_generation(self.population)
        
        logger.info(f"遗传算法优化完成: 最佳适应度={self.best_individual.fitness:.4f}")
        
        return self.best_individual, self.fitness_history
    
    def decode_individual(self, individual: Individual) -> Dict[str, float]:
        """
        解码个体基因为参数字典
        
        参数:
            individual: 个体
            
        返回:
            参数字典
        """
        params = {}
        for i, param_name in enumerate(self.parameter_names):
            params[param_name] = individual.genes[i]
        
        return params
