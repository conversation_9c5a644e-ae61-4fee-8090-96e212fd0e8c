import pandas as pd

# 读取数据
df = pd.read_csv('data/car.data', header=None)

# 标签二值化：vgood为1，其他为0
df['label'] = (df.iloc[:, -1] == 'vgood').astype(int)

# 统计类别数量
minority_count = (df['label'] == 1).sum()
majority_count = (df['label'] == 0).sum()

# 输出不平衡比例
if minority_count > 0:
    ir = majority_count / minority_count
    print(f"多数类样本数: {majority_count}")
    print(f"少数类样本数: {minority_count}")
    print(f'不平衡比例: {ir:.2f}')
else:
    print("数据中没有少数类（vgood）样本！")