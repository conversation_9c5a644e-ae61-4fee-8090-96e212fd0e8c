import pandas as pd
import numpy as np
from sklearn.preprocessing import LabelEncoder, MinMaxScaler
from imblearn.over_sampling import ADASYN, SMOTE
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import f1_score, confusion_matrix, roc_auc_score
from deap import base, creator, tools, algorithms
import random
import torch
import torch.nn as nn
import torch.optim as optim
import os
import pickle
import matplotlib.pyplot as plt
from torch import nn

log_dir = 'GAAD/result'
os.makedirs(log_dir, exist_ok=True)
log_file = os.path.join(log_dir, 'ga_adasyn_wgan_compare_log.txt')
def log(msg):
    print(msg)
    with open(log_file, 'a', encoding='utf-8') as f:
        f.write(str(msg) + '\n')

# 数据加载与预处理
raw = pd.read_csv('C:/Users/<USER>/Desktop/GAAD/data/credit.data', header=None, na_values='?')
data = raw.dropna()
X = data.iloc[:, :-1]
y = data.iloc[:, -1]
y = (y == '+').astype(int)
for col in X.columns:
    if X[col].dtype == 'object':
        X[col] = LabelEncoder().fit_transform(X[col])
X = X.values
scaler = MinMaxScaler()
X = scaler.fit_transform(X)
X_train_val, X_test, y_train_val, y_test = train_test_split(
    X, y, test_size=0.1, random_state=42)
X_train, X_val, y_train, y_val = train_test_split(
    X_train_val, y_train_val, test_size=0.222, random_state=42)
input_dim = X_train.shape[1]

# 记录各组结果
groups = []
f1s = []
gmeans = []
aucs = []

def record_result(note, f1, g_mean, auc):
    groups.append(note)
    f1s.append(f1)
    gmeans.append(g_mean)
    aucs.append(auc)

# 公共评估函数
def evaluate_clf(X_train, y_train, X_val, y_val, X_test, y_test, note):
    rf = RandomForestClassifier(random_state=42)
    rf.fit(X_train, y_train)
    y_pred = rf.predict(X_test)
    y_prob = rf.predict_proba(X_test)[:,1]
    f1 = f1_score(y_test, y_pred)
    tn, fp, fn, tp = confusion_matrix(y_test, y_pred).ravel()
    g_mean = np.sqrt((tp/(tp+fn)) * (tn/(tn+fp)))
    auc = roc_auc_score(y_test, y_prob)
    log(f"{note} F1: {f1:.4f}, G-mean: {g_mean:.4f}, AUC: {auc:.4f}")
    record_result(note, f1, g_mean, auc)
    return f1, g_mean, auc

# 1. 原始数据
log('对比组1：原始数据')
evaluate_clf(X_train, y_train, X_val, y_val, X_test, y_test, '原始数据')

# 2. SMOTE
log('对比组2：SMOTE')
smote = SMOTE(sampling_strategy='minority', random_state=42)
X_smote, y_smote = smote.fit_resample(X_train, y_train)
evaluate_clf(X_smote, y_smote, X_val, y_val, X_test, y_test, 'SMOTE')

# 3. ADASYN-WGAN（不优化参数，默认参数）
log('对比组3：ADASYN-WGAN（默认参数）')
adasyn = ADASYN(sampling_strategy='minority', n_neighbors=5, random_state=42)
X_adasyn, y_adasyn = adasyn.fit_resample(X_train, y_train)
class Generator(nn.Module):
    def __init__(self, input_dim, layers=3, units=128):
        super().__init__()
        self.model = nn.Sequential(
            nn.Linear(input_dim, units),
            nn.BatchNorm1d(units),
            nn.ReLU(),
            *[nn.Sequential(
                nn.Linear(units, units),
                nn.BatchNorm1d(units),
                nn.ReLU()
            ) for _ in range(layers-2)],
            nn.Linear(units, input_dim),
            nn.Tanh()
        )
    def forward(self, x):
        return self.model(x)
class Discriminator(nn.Module):
    def __init__(self, input_dim, layers=2, units=64):
        super().__init__()
        self.model = nn.Sequential(
            nn.Linear(input_dim, units),
            nn.LeakyReLU(0.2),
            *[nn.Sequential(
                nn.Linear(units, units),
                nn.LeakyReLU(0.2)
            ) for _ in range(layers-1)],
            nn.Linear(units, 1)
        )
    def forward(self, x):
        return self.model(x)
G = Generator(input_dim=input_dim)
D = Discriminator(input_dim=input_dim)
optimizer_G = optim.RMSprop(G.parameters(), lr=1e-4)
optimizer_D = optim.RMSprop(D.parameters(), lr=1e-4)
for epoch in range(200):
    real_samples = torch.tensor(X_train, dtype=torch.float32)
    fake_samples = G(torch.tensor(X_adasyn[y_adasyn==1], dtype=torch.float32))
    loss_D = -torch.mean(D(real_samples)) + torch.mean(D(fake_samples))
    optimizer_D.zero_grad()
    loss_D.backward()
    optimizer_D.step()
    for p in D.parameters():
        p.data.clamp_(-0.01, 0.01)
    fake_samples = G(torch.tensor(X_adasyn[y_adasyn==1], dtype=torch.float32))
    loss_G = -torch.mean(D(fake_samples))
    optimizer_G.zero_grad()
    loss_G.backward()
    optimizer_G.step()
X_synth = G(torch.tensor(X_adasyn[y_adasyn==1], dtype=torch.float32)).detach().numpy()
X_combined = np.vstack([X_train, X_synth])
y_combined = np.hstack([y_train, np.ones(len(X_synth))])
evaluate_clf(X_combined, y_combined, X_val, y_val, X_test, y_test, 'ADASYN-WGAN')

# 4. GA仅优化ADASYN参数
log('实验组1：GA仅优化ADASYN参数')
creator.create("FitnessMax", base.Fitness, weights=(1.0,))
creator.create("Individual", list, fitness=creator.FitnessMax)

toolbox = base.Toolbox()
# 确保学习率参数在合理范围内（0.0001 到 0.1）
toolbox.register("attr_k", random.randint, 1, 10)  # 1到10之间的整数
toolbox.register("attr_alpha", random.uniform, 0.0001, 0.1)  # alpha如果你需要的话
toolbox.register("individual", tools.initCycle, creator.Individual, (toolbox.attr_k, toolbox.attr_alpha), n=1)
toolbox.register("population", tools.initRepeat, list, toolbox.individual, n=10)
def evaluate_adasyn(ind):
    k, alpha = ind
    k = max(1, int(round(k)))
    adasyn = ADASYN(sampling_strategy='minority', n_neighbors=int(k), random_state=42)
    X_adasyn, y_adasyn = adasyn.fit_resample(X_train, y_train)
    rf = RandomForestClassifier(random_state=42)
    rf.fit(X_adasyn, y_adasyn)
    y_pred = rf.predict(X_val)
    tn, fp, fn, tp = confusion_matrix(y_val, y_pred).ravel()
    f1 = f1_score(y_val, y_pred)
    g_mean = np.sqrt((tp/(tp+fn)) * (tn/(tn+fp)))
    delta_beta = 0.05
    return (0.4*f1 + 0.4*g_mean + 0.2*(1-delta_beta),)
toolbox.register("evaluate", evaluate_adasyn)
toolbox.register("mate", tools.cxTwoPoint)
toolbox.register("mutate", tools.mutGaussian, mu=0, sigma=0.1, indpb=0.1)
toolbox.register("select", tools.selTournament, tournsize=3)
pop = toolbox.population()
hof = tools.HallOfFame(1)
stats = tools.Statistics(lambda ind: ind.fitness.values)
stats.register("avg", np.mean)
stats.register("max", np.max)
pop, logbook = algorithms.eaSimple(pop, toolbox, cxpb=0.8, mutpb=0.2, ngen=5, stats=stats, halloffame=hof, verbose=True)
best_ind = hof[0]
log(f"GA-ADASYN最优参数: {best_ind}")
adasyn = ADASYN(sampling_strategy='minority', n_neighbors=int(best_ind[0]), random_state=42)
X_adasyn, y_adasyn = adasyn.fit_resample(X_train, y_train)
evaluate_clf(X_adasyn, y_adasyn, X_val, y_val, X_test, y_test, 'GA-ADASYN')

# 5. GA仅优化WGAN参数
log('实验组2：GA仅优化WGAN参数')
def init_individual_wgan():
    return creator.Individual([
        random.randint(2,4),  # G_layers
        random.randint(64,256),  # G_units
        10**random.uniform(-5,-3),  # G_lr
        random.randint(2,3),  # D_layers
        random.randint(64,128),  # D_units
        10**random.uniform(-5,-3)  # D_lr
    ])
toolbox.register("individual", init_individual_wgan)
toolbox.register("population", tools.initRepeat, list, toolbox.individual, n=10)
def evaluate_wgan(ind):
    G_layers, G_units, G_lr, D_layers, D_units, D_lr = ind
    G_lr = min(max(1e-5, float(G_lr)), 1e-2)
    D_lr = min(max(1e-5, float(D_lr)), 1e-2)
    adasyn = ADASYN(sampling_strategy='minority', n_neighbors=5, random_state=42)
    X_adasyn, y_adasyn = adasyn.fit_resample(X_train, y_train)
    G = Generator(input_dim=input_dim, layers=int(G_layers), units=int(G_units))
    D = Discriminator(input_dim=input_dim, layers=int(D_layers), units=int(D_units))
    optimizer_G = optim.RMSprop(G.parameters(), lr=G_lr)
    optimizer_D = optim.RMSprop(D.parameters(), lr=D_lr)
    for _ in range(100):
        real_samples = torch.tensor(X_train, dtype=torch.float32)
        fake_samples = G(torch.tensor(X_adasyn[y_adasyn==1], dtype=torch.float32))
        loss_D = -torch.mean(D(real_samples)) + torch.mean(D(fake_samples))
        optimizer_D.zero_grad()
        loss_D.backward()
        optimizer_D.step()
        for p in D.parameters():
            p.data.clamp_(-0.01, 0.01)
        fake_samples = G(torch.tensor(X_adasyn[y_adasyn==1], dtype=torch.float32))
        loss_G = -torch.mean(D(fake_samples))
        optimizer_G.zero_grad()
        loss_G.backward()
        optimizer_G.step()
    X_synth = G(torch.tensor(X_adasyn[y_adasyn==1], dtype=torch.float32)).detach().numpy()
    X_combined = np.vstack([X_train, X_synth])
    y_combined = np.hstack([y_train, np.ones(len(X_synth))])
    rf = RandomForestClassifier(random_state=42)
    rf.fit(X_combined, y_combined)
    y_pred = rf.predict(X_val)
    tn, fp, fn, tp = confusion_matrix(y_val, y_pred).ravel()
    f1 = f1_score(y_val, y_pred)
    g_mean = np.sqrt((tp/(tp+fn)) * (tn/(tn+fp)))
    delta_beta = 0.05
    return (0.4*f1 + 0.4*g_mean + 0.2*(1-delta_beta),)
toolbox.register("evaluate", evaluate_wgan)
toolbox.register("mate", tools.cxTwoPoint)
toolbox.register("mutate", tools.mutGaussian, mu=0, sigma=0.1, indpb=0.1)
toolbox.register("select", tools.selTournament, tournsize=3)
pop = toolbox.population()
hof = tools.HallOfFame(1)
stats = tools.Statistics(lambda ind: ind.fitness.values)
stats.register("avg", np.mean)
stats.register("max", np.max)
pop, logbook = algorithms.eaSimple(pop, toolbox, cxpb=0.8, mutpb=0.2, ngen=5, stats=stats, halloffame=hof, verbose=True)
best_ind = hof[0]
log(f"GA-WGAN最优参数: {best_ind}")
G = Generator(input_dim=input_dim, layers=int(best_ind[0]), units=int(best_ind[1]))
D = Discriminator(input_dim=input_dim, layers=int(best_ind[3]), units=int(best_ind[4]))
optimizer_G = optim.RMSprop(G.parameters(), lr=best_ind[2])
optimizer_D = optim.RMSprop(D.parameters(), lr=best_ind[5])
adasyn = ADASYN(sampling_strategy='minority', n_neighbors=5, random_state=42)
X_adasyn, y_adasyn = adasyn.fit_resample(X_train, y_train)
for epoch in range(200):
    real_samples = torch.tensor(X_train, dtype=torch.float32)
    fake_samples = G(torch.tensor(X_adasyn[y_adasyn==1], dtype=torch.float32))
    loss_D = -torch.mean(D(real_samples)) + torch.mean(D(fake_samples))
    optimizer_D.zero_grad()
    loss_D.backward()
    optimizer_D.step()
    for p in D.parameters():
        p.data.clamp_(-0.01, 0.01)
    fake_samples = G(torch.tensor(X_adasyn[y_adasyn==1], dtype=torch.float32))
    loss_G = -torch.mean(D(fake_samples))
    optimizer_G.zero_grad()
    loss_G.backward()
    optimizer_G.step()
X_synth = G(torch.tensor(X_adasyn[y_adasyn==1], dtype=torch.float32)).detach().numpy()
X_combined = np.vstack([X_train, X_synth])
y_combined = np.hstack([y_train, np.ones(len(X_synth))])
evaluate_clf(X_combined, y_combined, X_val, y_val, X_test, y_test, 'GA-WGAN')

# 实验全部完成后可视化
plt.figure(figsize=(10,6))
bar_width = 0.2
x = np.arange(len(groups))
plt.bar(x-bar_width, f1s, width=bar_width, label='F1')
plt.bar(x, gmeans, width=bar_width, label='G-mean')
plt.bar(x+bar_width, aucs, width=bar_width, label='AUC')
plt.xticks(x, groups, rotation=20)
plt.ylabel('Score')
plt.title('各方法对比实验结果')
plt.legend()
plt.tight_layout()
plt.savefig(os.path.join(log_dir, 'compare_results.png'))
plt.close()
log('可视化结果已保存为compare_results.png') 