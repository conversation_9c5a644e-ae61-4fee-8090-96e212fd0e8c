import numpy as np
import pandas as pd
from sklearn.model_selection import cross_val_score
from sklearn.tree import DecisionTreeClassifier
from sklearn.preprocessing import LabelEncoder, MinMaxScaler
import torch
import torch.nn as nn
import torch.optim as optim
from sklearn.svm import SVC
from sklearn.metrics import f1_score, roc_auc_score
from sklearn.model_selection import train_test_split

# ===================== 数据读取与预处理 =====================
def load_data(path):
    df = pd.read_csv(path, header=None)
    # 标签二值化
    df['label'] = (df.iloc[:, -1] == 'vgood').astype(int)
    X = df.iloc[:, :-2].values
    y = df['label'].values
    # 类别特征编码
    for i in range(X.shape[1]):
        if not np.issubdtype(X[:, i].dtype, np.number):
            le = LabelEncoder()
            X[:, i] = le.fit_transform(X[:, i])
    X = X.astype(float)
    # 归一化
    scaler = MinMaxScaler()
    X = scaler.fit_transform(X)
    return X, y

# ===================== ADASYN采样 =====================
def adasyn(X, y, K=5, beta=0.5, random_state=None):
    from sklearn.neighbors import NearestNeighbors
    np.random.seed(random_state)
    X_min = X[y == 1]
    X_maj = X[y == 0]
    m_s, m_l = len(X_min), len(X_maj)
    d = m_s / m_l
    d_th = 0.5
    if d >= d_th:
        return X, y  # 不需要采样
    G = int((m_l - m_s) * beta)
    # 计算每个少数类样本的K近邻
    nn = NearestNeighbors(n_neighbors=K+1).fit(X)
    r = []
    for xi in X_min:
        neighbors = nn.kneighbors([xi], return_distance=False)[0][1:]
        r_i = np.sum(y[neighbors] == 0) / K
        r.append(r_i)
    r = np.array(r)
    r_hat = r / r.sum()
    g = np.round(r_hat * G).astype(int)
    S = []
    for i, xi in enumerate(X_min):
        if g[i] == 0:
            continue
        neighbors = nn.kneighbors([xi], return_distance=False)[0][1:]
        for _ in range(g[i]):
            xzi = X[neighbors[np.random.randint(0, K)]]
            lam = np.random.rand()
            si = xi + (xzi - xi) * lam
            S.append(si)
    if len(S) == 0:
        return X, y
    X_syn = np.vstack([X, np.array(S)])
    y_syn = np.hstack([y, np.ones(len(S))])
    return X_syn, y_syn

# ===================== WGAN生成器与判别器 =====================
class Generator(nn.Module):
    def __init__(self, input_dim, output_dim):
        super().__init__()
        self.net = nn.Sequential(
            nn.Linear(input_dim, 128),
            nn.ReLU(),
            nn.Linear(128, 256),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.Linear(256, 512),
            nn.BatchNorm1d(512),
            nn.ReLU(),
            nn.Linear(512, 1024),
            nn.Tanh(),
            nn.Linear(1024, output_dim)
        )
    def forward(self, x):
        return self.net(x)

class Discriminator(nn.Module):
    def __init__(self, input_dim):
        super().__init__()
        self.net = nn.Sequential(
            nn.Linear(input_dim, 512),
            nn.LeakyReLU(0.2),
            nn.Linear(512, 256),
            nn.LeakyReLU(0.2),
            nn.Linear(256, 128),
            nn.LeakyReLU(0.2),
            nn.Linear(128, 1)
        )
    def forward(self, x):
        return self.net(x)

# ===================== WGAN训练 =====================
def train_wgan(X_min, epochs=100, batch_size=32, z_dim=10, device='cuda'):
    X_min = torch.tensor(X_min, dtype=torch.float32).to(device)
    g = Generator(z_dim, X_min.shape[1]).to(device)
    d = Discriminator(X_min.shape[1]).to(device)
    g_opt = optim.Adam(g.parameters(), lr=1e-4)
    d_opt = optim.Adam(d.parameters(), lr=1e-4)
    for epoch in range(epochs):
        idx = np.random.permutation(len(X_min))
        for i in range(0, len(X_min), batch_size):
            real = X_min[idx[i:i+batch_size]]
            z = torch.randn(real.size(0), z_dim).to(device)
            fake = g(z)
            # 判别器
            d_loss = -(torch.mean(d(real)) - torch.mean(d(fake.detach())))
            d_opt.zero_grad()
            d_loss.backward()
            d_opt.step()
            # 生成器
            if i % 5 == 0:
                z = torch.randn(real.size(0), z_dim).to(device)
                fake = g(z)
                g_loss = -torch.mean(d(fake))
                g_opt.zero_grad()
                g_loss.backward()
                g_opt.step()
    # 生成合成样本
    z = torch.randn(len(X_min), z_dim).to(device)
    synth = g(z).detach().cpu().numpy()
    return synth

def train_wgan_with_params(X_adasyn, X_real, lr=1e-4, gp_lambda=10, epochs=30, batch_size=32, z_dim=None, device='cpu'):
    # X_adasyn: 作为生成器输入（不再用随机噪声）
    # X_real: 真实少数类样本
    # 其余参数由GA传入
    X_adasyn = torch.tensor(X_adasyn, dtype=torch.float32).to(device)
    X_real = torch.tensor(X_real, dtype=torch.float32).to(device)
    g = Generator(X_adasyn.shape[1], X_real.shape[1]).to(device)
    d = Discriminator(X_real.shape[1]).to(device)
    g_opt = optim.Adam(g.parameters(), lr=lr)
    d_opt = optim.Adam(d.parameters(), lr=lr)
    for epoch in range(epochs):
        idx = np.random.permutation(len(X_adasyn))
        for i in range(0, len(X_adasyn), batch_size):
            fake_input = X_adasyn[idx[i:i+batch_size]]
            cur_batch_size = fake_input.shape[0]
            if cur_batch_size < 2:
                continue  # 跳过 batch size 为1的情况
            real = X_real[np.random.choice(len(X_real), cur_batch_size, replace=True)]
            fake = g(fake_input)
            # 判别器损失
            d_loss = -(torch.mean(d(real)) - torch.mean(d(fake.detach())))
            # 梯度惩罚
            alpha = torch.rand(cur_batch_size, 1).to(device)
            interpolates = alpha * real + (1 - alpha) * fake.detach()
            interpolates.requires_grad_(True)
            d_interpolates = d(interpolates)
            gradients = torch.autograd.grad(
                outputs=d_interpolates, inputs=interpolates,
                grad_outputs=torch.ones_like(d_interpolates),
                create_graph=True, retain_graph=True, only_inputs=True
            )[0]
            gradient_penalty = ((gradients.norm(2, dim=1) - 1) ** 2).mean() * gp_lambda
            d_loss += gradient_penalty
            d_opt.zero_grad()
            d_loss.backward()
            d_opt.step()
            # 生成器
            if i % 5 == 0:
                fake = g(fake_input)
                g_loss = -torch.mean(d(fake))
                g_opt.zero_grad()
                g_loss.backward()
                g_opt.step()
    # 生成合成样本
    synth = g(X_adasyn).detach().cpu().numpy()
    return synth

# ===================== 适应度函数 =====================
def fitness_func(ind, X, y, random_state=None):
    k, beta, wgan_lr, wgan_gp = int(ind[0]), ind[1], ind[2], ind[3]
    # Step1: ADASYN采样
    X_adasyn, y_adasyn = adasyn(X, y, K=k, beta=beta, random_state=random_state)
    # Step2: WGAN训练（以X_adasyn为输入，原始少数类为真实样本，参数用wgan_lr, wgan_gp）
    X_min = X[y == 1]
    X_wgan = train_wgan_with_params(X_adasyn[y_adasyn == 1], X_min, lr=wgan_lr, gp_lambda=wgan_gp, epochs=30, batch_size=32, z_dim=X_min.shape[1], device='cpu')
    # Step3: 构建平衡数据集
    X_maj = X[y == 0]
    X_bal = np.vstack([X_maj, X_min, X_wgan])
    y_bal = np.hstack([np.zeros(len(X_maj)), np.ones(len(X_min) + len(X_wgan))])
    # Step4: SVM评估
    X_train, X_val, y_train, y_val = train_test_split(X_bal, y_bal, test_size=0.3, random_state=random_state, stratify=y_bal)
    clf = SVC(probability=True, random_state=random_state)
    clf.fit(X_train, y_train)
    y_pred = clf.predict(X_val)
    y_prob = clf.predict_proba(X_val)[:, 1]
    f1 = f1_score(y_val, y_pred, pos_label=1)
    auc = roc_auc_score(y_val, y_prob)
    return f1  # 只返回F1分数作为适应度

# ===================== GA主循环 =====================
def ga_optimize(X, y, n=10, MAX=10, pc=0.8, pm=0.2, random_state=None):
    np.random.seed(random_state)
    # 染色体：[k, beta, wgan_lr, wgan_gp]
    population = []
    for _ in range(n):
        k = np.random.randint(1, 11)
        beta = np.random.uniform(0.1, 1.0)
        wgan_lr = 10 ** np.random.uniform(-5, -3)
        wgan_gp = np.random.uniform(0.1, 10)
        population.append([k, beta, wgan_lr, wgan_gp])
    population = np.array(population)
    fitness = np.array([fitness_func(ind, X, y, random_state) for ind in population])
    global_best_f1 = -1
    global_best_ind = None
    for t in range(MAX):
        # 锦标赛选择
        idx = np.random.choice(n, n, replace=True)
        selected = population[idx]
        # 交叉
        offspring = []
        for i in range(0, n, 2):
            p1, p2 = selected[i], selected[(i+1)%n]
            if np.random.rand() < pc:
                point = np.random.randint(1, 4)
                child1 = np.hstack([p1[:point], p2[point:]])
                child2 = np.hstack([p2[:point], p1[point:]])
            else:
                child1, child2 = p1.copy(), p2.copy()
            offspring.extend([child1, child2])
        offspring = np.array(offspring[:n])
        # 变异
        for i in range(n):
            if np.random.rand() < pm:
                offspring[i, 0] = np.clip(np.round(offspring[i, 0] + np.random.normal(0, 1)), 1, 10)
                offspring[i, 1] = np.clip(offspring[i, 1] + np.random.normal(0, 0.1), 0.1, 1.0)
                offspring[i, 2] = np.clip(offspring[i, 2] * 10 ** np.random.normal(0, 0.1), 1e-5, 1e-3)
                offspring[i, 3] = np.clip(offspring[i, 3] + np.random.normal(0, 1), 0.1, 10)
        # 适应度
        fitness_off = np.array([fitness_func(ind, X, y, random_state) for ind in offspring])
        # 替换
        population = offspring
        fitness = fitness_off
        if fitness.max() > global_best_f1:
            global_best_f1 = fitness.max()
            global_best_ind = population[np.argmax(fitness)]
        print(f"第{t+1}代，最优适应度：{fitness.max():.4f}")
    # 最后输出global_best_ind和global_best_f1
    return global_best_ind

# ===================== 主程序 =====================
if __name__ == "__main__":
    X, y = load_data('data/car.data')
    best_params = ga_optimize(X, y, n=6, MAX=5, pc=0.8, pm=0.2, random_state=42)
    print("最优参数：k = %d, beta = %.3f, wgan_lr = %.6f, wgan_gp = %.3f" % tuple(best_params))

# 假设最优参数如下
best_beta = 0.656
best_lr = 0.000021
best_gp = 0.675

for k in range(1, 11):
    f1 = fitness_func([k, best_beta, best_lr, best_gp], X, y)
    print(f"k={k} 时的F1分数: {f1:.4f}")
