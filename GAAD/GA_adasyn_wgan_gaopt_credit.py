import pandas as pd
import numpy as np
from sklearn.preprocessing import LabelEncoder, MinMaxScaler
from imblearn.over_sampling import ADASYN, SMOTE
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import f1_score, confusion_matrix, roc_auc_score
from deap import base, creator, tools, algorithms
import random
import torch
import torch.nn as nn
import torch.optim as optim
import os
import pickle
import matplotlib.pyplot as plt
import matplotlib
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

# 设置随机种子以确保结果可重现
np.random.seed(42)
torch.manual_seed(42)
random.seed(42)


# ==== 新增：支持car.data二分类处理 ====
def load_car_data():
    df = pd.read_csv('../data/car.data', header=None)
    # vgood为1，其他为0
    df['label'] = (df.iloc[:, -1] == 'vgood').astype(int)
    X = df.iloc[:, :-2]  # 除去最后一列原始标签和新加的label
    for col in X.columns:
        X[col] = LabelEncoder().fit_transform(X[col])
    y = df['label']
    # 输出不平衡比例
    minority_count = (y == 1).sum()
    majority_count = (y == 0).sum()
    if minority_count > 0:
        ir = majority_count / minority_count
        print(f"多数类样本数: {majority_count}")
        print(f"少数类样本数: {minority_count}")
        print(f'不平衡比例: {ir:.2f}')
    else:
        print("数据中没有少数类（vgood）样本！")
    return X.values, y.values
# ==== 新增结束 ====

# ==== 选择数据集：credit 或 car ====
USE_CAR = False  # 改为False使用credit数据集，因为car数据集可能没有足够的少数类样本
if USE_CAR:
    X, y = load_car_data()
    col_num = X.shape[1]
else:
    # 读取数据，处理缺失
    raw = pd.read_csv('../data/credit.data', header=None, na_values='?')
    data = raw.dropna()
    X = data.iloc[:, :-1]
    y = data.iloc[:, -1]
    y = (y == '+').astype(int)
    # 类别特征编码
    for col in X.columns:
        if X[col].dtype == 'object':
            X[col] = LabelEncoder().fit_transform(X[col])
    X = X.values
# 不平衡率
ir = len(y[y==0]) / len(y[y==1])
print(f"原始不平衡率: {ir:.2f}")
# ADASYN初步平衡
minority_count = sum(y == 1)
majority_count = sum(y == 0)
if majority_count / minority_count < 1.5:
    print("类别分布较平衡，跳过ADASYN采样。")
    X_adasyn, y_adasyn = X, y
else:
    adasyn_init = ADASYN(sampling_strategy="minority", n_neighbors=3, random_state=42)
    X_adasyn, y_adasyn = adasyn_init.fit_resample(X, y)
# 归一化
scaler = MinMaxScaler()
X_scaled = scaler.fit_transform(X_adasyn)
# 划分数据集
X_train_val, X_test, y_train_val, y_test = train_test_split(
    X_scaled, y_adasyn, test_size=0.1, random_state=42)
X_train, X_val, y_train, y_val = train_test_split(
    X_train_val, y_train_val, test_size=0.222, random_state=42)
input_dim = X_train.shape[1]

# 2. ADASYN生成函数
def adasyn_generate(X, y, k=5, alpha=0.98):
    """
    使用ADASYN算法生成少数类样本
    参数:
        X: 特征数据
        y: 标签数据
        k: 邻居数量
        alpha: 平衡参数
    返回:
        新生成的少数类样本
    """
    alpha = min(1.0, max(0.95, float(alpha)))  # 限制alpha范围
    k = min(int(k), max(1, sum(y==1)-1))  # 防止k大于少数类样本数-1

    # 确保有足够的少数类样本进行ADASYN
    minority_count = sum(y == 1)
    if minority_count < 2:
        print(f"少数类样本数量不足({minority_count})，无法进行ADASYN采样")
        return np.empty((0, X.shape[1]))

    adasyn = ADASYN(sampling_strategy=alpha, n_neighbors=k, random_state=42)
    try:
        X_res, y_res = adasyn.fit_resample(X, y)
        # 提取新生成的少数类样本
        new_samples = X_res[len(X):][y_res[len(y):] == 1]
        print(f"ADASYN成功生成 {len(new_samples)} 个新样本 (k={k}, alpha={alpha:.4f})")
        return new_samples
    except Exception as e:
        print(f"ADASYN采样失败: {e} (alpha={alpha}, k={k})")
        return np.empty((0, X.shape[1]))

def smote_generate(X, y, k=5, alpha=1.0):
    """
    使用SMOTE算法生成少数类样本（备用方案）
    """
    alpha = min(1.0, max(0.95, float(alpha)))
    k = min(int(k), max(1, sum(y==1)-1))
    smote = SMOTE(sampling_strategy=alpha, k_neighbors=k, random_state=42)
    try:
        X_res, y_res = smote.fit_resample(X, y)
        new_samples = X_res[len(X):][y_res[len(y):] == 1]
        return new_samples
    except Exception as e:
        print(f"SMOTE采样失败: {e} (alpha={alpha}, k={k})")
        return np.empty((0, X.shape[1]))

# 3. WGAN模型
def get_G_D(input_dim, G_layers, G_units, D_layers, D_units):
    """
    创建WGAN的生成器和判别器
    参数:
        input_dim: 输入维度
        G_layers: 生成器层数
        G_units: 生成器隐藏单元数
        D_layers: 判别器层数
        D_units: 判别器隐藏单元数
    """
    class Generator(nn.Module):
        def __init__(self):
            super().__init__()
            layers = [nn.Linear(input_dim, G_units), nn.BatchNorm1d(G_units), nn.ReLU()]
            for _ in range(G_layers-2):
                layers += [nn.Linear(G_units, G_units), nn.BatchNorm1d(G_units), nn.ReLU()]
            layers += [nn.Linear(G_units, input_dim), nn.Tanh()]
            self.model = nn.Sequential(*layers)

        def forward(self, x):
            return self.model(x)

    class Discriminator(nn.Module):
        def __init__(self):
            super().__init__()
            layers = [nn.Linear(input_dim, D_units), nn.LeakyReLU(0.2)]
            for _ in range(D_layers-1):
                layers += [nn.Linear(D_units, D_units), nn.LeakyReLU(0.2)]
            layers += [nn.Linear(D_units, 1)]
            self.model = nn.Sequential(*layers)

        def forward(self, x):
            return self.model(x)

    return Generator(), Discriminator()

def compute_gradient_penalty(D, real_samples, fake_samples, lambda_gp, device='cpu'):
    """
    计算WGAN-GP的梯度惩罚
    """
    batch_size = real_samples.size(0)
    alpha = torch.rand(batch_size, 1).to(device)

    # 插值样本
    interpolates = alpha * real_samples + (1 - alpha) * fake_samples
    interpolates = interpolates.to(device)
    interpolates.requires_grad_(True)

    # 计算判别器对插值样本的输出
    d_interpolates = D(interpolates)

    # 计算梯度
    gradients = torch.autograd.grad(
        outputs=d_interpolates,
        inputs=interpolates,
        grad_outputs=torch.ones_like(d_interpolates),
        create_graph=True,
        retain_graph=True,
        only_inputs=True
    )[0]

    # 计算梯度惩罚
    gradient_penalty = ((gradients.norm(2, dim=1) - 1) ** 2).mean() * lambda_gp
    return gradient_penalty

# 4. GA参数优化设置
# 清理之前的creator定义（如果存在）
if hasattr(creator, "FitnessMax"):
    del creator.FitnessMax
if hasattr(creator, "Individual"):
    del creator.Individual

creator.create("FitnessMax", base.Fitness, weights=(1.0,))
creator.create("Individual", list, fitness=creator.FitnessMax)

def init_individual():
    """
    初始化GA个体，包含ADASYN和WGAN的所有超参数
    参数顺序: [k, alpha, G_layers, G_units, G_lr, D_layers, D_units, D_lr, lambda_gp, n_critic, batch_size]
    """
    return creator.Individual([
        random.randint(3, 8),           # k: ADASYN邻居数，范围3-8
        random.uniform(0.95, 1.0),      # alpha: ADASYN平衡参数，接近1.0
        random.randint(2, 4),           # G_layers: 生成器层数
        random.choice([64, 128, 256]),  # G_units: 生成器隐藏单元数
        10**random.uniform(-4.5, -3),   # G_lr: 生成器学习率，10^(-4.5) 到 10^(-3)
        random.randint(2, 3),           # D_layers: 判别器层数
        random.choice([64, 128]),       # D_units: 判别器隐藏单元数
        10**random.uniform(-4.5, -3),   # D_lr: 判别器学习率
        random.uniform(8, 15),          # lambda_gp: 梯度惩罚系数
        random.randint(3, 6),           # n_critic: 判别器训练次数
        random.choice([32, 64, 128])    # batch_size: 批量大小
    ])

# 设置GA工具箱
toolbox = base.Toolbox()
toolbox.register("individual", init_individual)
toolbox.register("population", tools.initRepeat, list, toolbox.individual, n=15)  # 增加种群大小

def evaluate(ind):
    """
    GA适应度评估函数
    评估ADASYN和WGAN参数组合的性能
    """
    k, alpha, G_layers, G_units, G_lr, D_layers, D_units, D_lr, lambda_gp, n_critic, batch_size = ind

    # 参数范围限制
    G_lr = min(max(1e-5, float(G_lr)), 1e-2)
    D_lr = min(max(1e-5, float(D_lr)), 1e-2)
    batch_size = max(8, min(int(batch_size), len(X_train)//2))  # 确保batch_size合理

    try:
        # Step 1: 使用GA优化的参数进行ADASYN采样
        print(f"正在评估参数组合: k={int(k)}, alpha={alpha:.4f}, batch_size={batch_size}")
        X_adasyn = adasyn_generate(X_train, y_train, k=int(k), alpha=alpha)

        if X_adasyn.shape[0] == 0:
            print("ADASYN生成失败，返回最低适应度")
            return (-9999,)

        # Step 2: 创建WGAN模型
        G, D = get_G_D(input_dim, int(G_layers), int(G_units), int(D_layers), int(D_units))
        optimizer_G = optim.RMSprop(G.parameters(), lr=G_lr)
        optimizer_D = optim.RMSprop(D.parameters(), lr=D_lr)

        # Step 3: WGAN训练
        G_losses, D_losses = [], []
        device = 'cpu'  # 使用CPU以确保稳定性

        for epoch in range(50):  # 减少训练轮数以加快GA评估
            # 训练判别器 n_critic 次
            for _ in range(int(n_critic)):
                # 获取真实样本
                if len(X_train) < batch_size:
                    real_idx = np.random.choice(X_train.shape[0], batch_size, replace=True)
                else:
                    real_idx = np.random.choice(X_train.shape[0], batch_size, replace=False)
                real_samples = torch.tensor(X_train[real_idx], dtype=torch.float32)

                # 使用ADASYN生成的样本作为生成器输入
                adasyn_idx = np.random.choice(X_adasyn.shape[0], batch_size, replace=True)
                adasyn_input = torch.tensor(X_adasyn[adasyn_idx], dtype=torch.float32)
                fake_samples = G(adasyn_input)

                # 计算判别器损失
                loss_D = -torch.mean(D(real_samples)) + torch.mean(D(fake_samples.detach()))

                # 添加梯度惩罚
                gradient_penalty = compute_gradient_penalty(D, real_samples, fake_samples.detach(), lambda_gp, device)
                loss_D += gradient_penalty

                # 更新判别器
                optimizer_D.zero_grad()
                loss_D.backward()
                optimizer_D.step()

            # 训练生成器
            adasyn_idx = np.random.choice(X_adasyn.shape[0], batch_size, replace=True)
            adasyn_input = torch.tensor(X_adasyn[adasyn_idx], dtype=torch.float32)
            fake_samples = G(adasyn_input)
            loss_G = -torch.mean(D(fake_samples))

            optimizer_G.zero_grad()
            loss_G.backward()
            optimizer_G.step()

            G_losses.append(loss_G.item())
            D_losses.append(loss_D.item())

        # Step 4: 生成合成样本
        X_synth = G(torch.tensor(X_adasyn, dtype=torch.float32)).detach().numpy()

        # Step 5: 构建平衡数据集
        X_combined = np.vstack([X_train, X_synth])
        y_combined = np.hstack([y_train, np.ones(len(X_synth))])

        # Step 6: 使用随机森林评估性能
        rf = RandomForestClassifier(n_estimators=50, random_state=42)  # 减少树的数量以加快评估
        rf.fit(X_combined, y_combined)
        y_pred = rf.predict(X_val)
        y_prob = rf.predict_proba(X_val)[:, 1]

        # Step 7: 计算评估指标
        tn, fp, fn, tp = confusion_matrix(y_val, y_pred).ravel()
        f1 = f1_score(y_val, y_pred)
        g_mean = np.sqrt((tp/(tp+fn)) * (tn/(tn+fp))) if (tp+fn) > 0 and (tn+fp) > 0 else 0
        auc = roc_auc_score(y_val, y_prob) if len(np.unique(y_val)) > 1 else 0

        # 综合适应度函数
        fitness = 0.4*f1 + 0.4*g_mean + 0.2*auc
        print(f"评估结果: F1={f1:.4f}, G-mean={g_mean:.4f}, AUC={auc:.4f}, Fitness={fitness:.4f}")

        return (fitness,)

    except Exception as e:
        print(f"评估过程中出现错误: {e}")
        return (-9999,)

# 自定义变异函数
def custom_mutate(individual, indpb=0.2):
    """
    自定义变异函数，针对不同类型的参数使用不同的变异策略
    """
    if random.random() < indpb:
        # k (邻居数): 3-8
        individual[0] = max(3, min(8, individual[0] + random.randint(-1, 1)))
    if random.random() < indpb:
        # alpha (平衡参数): 0.95-1.0
        individual[1] = max(0.95, min(1.0, individual[1] + random.uniform(-0.02, 0.02)))
    if random.random() < indpb:
        # G_layers: 2-4
        individual[2] = max(2, min(4, individual[2] + random.randint(-1, 1)))
    if random.random() < indpb:
        # G_units: 从候选值中选择
        individual[3] = random.choice([64, 128, 256])
    if random.random() < indpb:
        # G_lr: 学习率变异
        individual[4] = max(1e-5, min(1e-2, individual[4] * random.uniform(0.5, 2.0)))
    if random.random() < indpb:
        # D_layers: 2-3
        individual[5] = max(2, min(3, individual[5] + random.randint(-1, 1)))
    if random.random() < indpb:
        # D_units: 从候选值中选择
        individual[6] = random.choice([64, 128])
    if random.random() < indpb:
        # D_lr: 学习率变异
        individual[7] = max(1e-5, min(1e-2, individual[7] * random.uniform(0.5, 2.0)))
    if random.random() < indpb:
        # lambda_gp: 8-15
        individual[8] = max(8, min(15, individual[8] + random.uniform(-1, 1)))
    if random.random() < indpb:
        # n_critic: 3-6
        individual[9] = max(3, min(6, individual[9] + random.randint(-1, 1)))
    if random.random() < indpb:
        # batch_size: 从候选值中选择
        individual[10] = random.choice([32, 64, 128])

    return individual,

# 注册GA操作
toolbox.register("evaluate", evaluate)
toolbox.register("mate", tools.cxTwoPoint)
toolbox.register("mutate", custom_mutate)
toolbox.register("select", tools.selTournament, tournsize=3)

# 5. 运行GA优化
log_dir = 'GAAD/result'
os.makedirs(log_dir, exist_ok=True)
log_file = os.path.join(log_dir, 'ga_adasyn_wgan_log.txt')

# 清空之前的日志文件
with open(log_file, 'w', encoding='utf-8') as f:
    f.write('')

# 日志记录函数
def log(msg):
    print(msg)
    with open(log_file, 'a', encoding='utf-8') as f:
        f.write(str(msg) + '\n')

# 初始化GA
pop = toolbox.population()
hof = tools.HallOfFame(3)  # 保存前3个最优个体
stats = tools.Statistics(lambda ind: ind.fitness.values)
stats.register("avg", np.mean)
stats.register("std", np.std)
stats.register("min", np.min)
stats.register("max", np.max)

log('=' * 60)
log('开始GA-ADASYN-WGAN参数优化')
log('=' * 60)
log(f'数据集信息:')
log(f'  训练集大小: {X_train.shape}')
log(f'  验证集大小: {X_val.shape}')
log(f'  测试集大小: {X_test.shape}')
log(f'  特征维度: {input_dim}')
log(f'  不平衡率: {len(y_train[y_train==0]) / len(y_train[y_train==1]):.2f}')
log('')
log('GA参数设置:')
log(f'  种群大小: {len(pop)}')
log(f'  进化代数: 8')
log(f'  交叉概率: 0.7')
log(f'  变异概率: 0.3')
log('')

# 运行GA算法
pop, logbook = algorithms.eaSimple(
    pop, toolbox,
    cxpb=0.7,      # 交叉概率
    mutpb=0.3,     # 变异概率
    ngen=8,        # 进化代数
    stats=stats,
    halloffame=hof,
    verbose=True
)

best_ind = hof[0]
log('')
log('GA优化完成！')
# 输出GA优化得到的最佳参数
k, alpha, G_layers, G_units, G_lr, D_layers, D_units, D_lr, lambda_gp, n_critic, batch_size = best_ind

log('=' * 60)
log('GA优化结果')
log('=' * 60)
log(f"最佳适应度值: {best_ind.fitness.values[0]:.6f}")
log('')
log("最佳ADASYN参数:")
log(f"  邻居数 k = {int(k)}")
log(f"  平衡参数 alpha = {alpha:.4f}")
log('')
log("最佳WGAN参数:")
log(f"  生成器:")
log(f"    层数 G_layers = {int(G_layers)}")
log(f"    隐藏单元数 G_units = {int(G_units)}")
log(f"    学习率 G_lr = {G_lr:.6f}")
log(f"  判别器:")
log(f"    层数 D_layers = {int(D_layers)}")
log(f"    隐藏单元数 D_units = {int(D_units)}")
log(f"    学习率 D_lr = {D_lr:.6f}")
log(f"  训练参数:")
log(f"    梯度惩罚系数 lambda_gp = {lambda_gp:.4f}")
log(f"    判别器训练次数 n_critic = {int(n_critic)}")
log(f"    批量大小 batch_size = {int(batch_size)}")
log('')

# 输出前3个最优个体
log("前3个最优参数组合:")
for i, ind in enumerate(hof):
    k_i, alpha_i, G_layers_i, G_units_i, G_lr_i, D_layers_i, D_units_i, D_lr_i, lambda_gp_i, n_critic_i, batch_size_i = ind
    log(f"  第{i+1}名 (适应度: {ind.fitness.values[0]:.6f}):")
    log(f"    ADASYN: k={int(k_i)}, alpha={alpha_i:.4f}")
    log(f"    WGAN: G_lr={G_lr_i:.6f}, D_lr={D_lr_i:.6f}, lambda_gp={lambda_gp_i:.4f}")

# 保存GA优化结果
ga_results = {
    'best_individual': best_ind,
    'best_fitness': best_ind.fitness.values[0],
    'hall_of_fame': list(hof),
    'logbook': logbook,
    'parameter_names': ['k', 'alpha', 'G_layers', 'G_units', 'G_lr', 'D_layers', 'D_units', 'D_lr', 'lambda_gp', 'n_critic', 'batch_size']
}

with open(os.path.join(log_dir, 'ga_optimization_results.pkl'), 'wb') as f:
    pickle.dump(ga_results, f)

log('GA优化结果已保存。')
log('')

# 6. 用最优参数训练最终模型并记录损失曲线
log('开始最终WGAN模型训练...')
k, alpha, G_layers, G_units, G_lr, D_layers, D_units, D_lr, lambda_gp, n_critic, batch_size = best_ind

# 参数处理
G_lr = min(max(1e-5, float(G_lr)), 1e-2)
D_lr = min(max(1e-5, float(D_lr)), 1e-2)
batch_size = max(8, min(int(batch_size), len(X_train)//2))

log(f'最终训练参数:')
log(f'  ADASYN: k={int(k)}, alpha={alpha:.4f}')
log(f'  WGAN: G_layers={int(G_layers)}, G_units={int(G_units)}, G_lr={G_lr:.6f}')
log(f'        D_layers={int(D_layers)}, D_units={int(D_units)}, D_lr={D_lr:.6f}')
log(f'        lambda_gp={lambda_gp:.4f}, n_critic={int(n_critic)}, batch_size={batch_size}')

# Step 1: 使用最优参数进行ADASYN采样
X_adasyn = adasyn_generate(X_train, y_train, k=int(k), alpha=alpha)
if X_adasyn.shape[0] == 0:
    log('ADASYN采样失败，无法生成合成样本，WGAN训练终止。')
    exit(0)

log(f'ADASYN生成了 {len(X_adasyn)} 个少数类样本')

# Step 2: 创建WGAN模型
G, D = get_G_D(input_dim, int(G_layers), int(G_units), int(D_layers), int(D_units))
optimizer_G = optim.RMSprop(G.parameters(), lr=G_lr)
optimizer_D = optim.RMSprop(D.parameters(), lr=D_lr)

# Step 3: 完整的WGAN训练流程
G_losses, D_losses = [], []
device = 'cpu'
epochs = 500

log(f'开始WGAN训练，共 {epochs} 轮...')

for epoch in range(epochs):
    epoch_G_loss = 0
    epoch_D_loss = 0

    # 训练判别器 n_critic 次
    for critic_iter in range(int(n_critic)):
        # 获取真实样本
        if len(X_train) < batch_size:
            real_idx = np.random.choice(X_train.shape[0], batch_size, replace=True)
        else:
            real_idx = np.random.choice(X_train.shape[0], batch_size, replace=False)
        real_samples = torch.tensor(X_train[real_idx], dtype=torch.float32)

        # 使用ADASYN生成的样本作为生成器输入
        adasyn_idx = np.random.choice(X_adasyn.shape[0], batch_size, replace=True)
        adasyn_input = torch.tensor(X_adasyn[adasyn_idx], dtype=torch.float32)
        fake_samples = G(adasyn_input)

        # 计算判别器损失 (Wasserstein距离)
        loss_D = -torch.mean(D(real_samples)) + torch.mean(D(fake_samples.detach()))

        # 添加梯度惩罚
        gradient_penalty = compute_gradient_penalty(D, real_samples, fake_samples.detach(), lambda_gp, device)
        loss_D += gradient_penalty

        # 更新判别器
        optimizer_D.zero_grad()
        loss_D.backward()
        optimizer_D.step()

        epoch_D_loss += loss_D.item()

    # 训练生成器
    adasyn_idx = np.random.choice(X_adasyn.shape[0], batch_size, replace=True)
    adasyn_input = torch.tensor(X_adasyn[adasyn_idx], dtype=torch.float32)
    fake_samples = G(adasyn_input)
    loss_G = -torch.mean(D(fake_samples))

    optimizer_G.zero_grad()
    loss_G.backward()
    optimizer_G.step()

    # 记录损失
    G_losses.append(loss_G.item())
    D_losses.append(epoch_D_loss / int(n_critic))

    # 定期输出训练进度
    if epoch % 50 == 0:
        log(f"Epoch {epoch}/{epochs}, Loss_G: {loss_G.item():.4f}, Loss_D: {epoch_D_loss/int(n_critic):.4f}")

log('WGAN训练完成')

# Step 4: 保存训练损失曲线
plt.figure(figsize=(12, 8))

# 绘制损失曲线
plt.subplot(2, 2, 1)
plt.plot(G_losses, label='Generator Loss', color='blue', alpha=0.7)
plt.plot(D_losses, label='Discriminator Loss', color='red', alpha=0.7)
plt.xlabel('Epoch')
plt.ylabel('Loss')
plt.title('WGAN训练损失曲线')
plt.legend()
plt.grid(True, alpha=0.3)

# 绘制损失的移动平均
plt.subplot(2, 2, 2)
window_size = 20
if len(G_losses) > window_size:
    G_smooth = np.convolve(G_losses, np.ones(window_size)/window_size, mode='valid')
    D_smooth = np.convolve(D_losses, np.ones(window_size)/window_size, mode='valid')
    plt.plot(range(window_size-1, len(G_losses)), G_smooth, label='Generator Loss (Smoothed)', color='blue')
    plt.plot(range(window_size-1, len(D_losses)), D_smooth, label='Discriminator Loss (Smoothed)', color='red')
    plt.xlabel('Epoch')
    plt.ylabel('Smoothed Loss')
    plt.title('WGAN训练损失曲线 (平滑)')
    plt.legend()
    plt.grid(True, alpha=0.3)

# 绘制损失分布
plt.subplot(2, 2, 3)
plt.hist(G_losses, bins=30, alpha=0.7, label='Generator Loss', color='blue')
plt.xlabel('Loss Value')
plt.ylabel('Frequency')
plt.title('生成器损失分布')
plt.legend()
plt.grid(True, alpha=0.3)

plt.subplot(2, 2, 4)
plt.hist(D_losses, bins=30, alpha=0.7, label='Discriminator Loss', color='red')
plt.xlabel('Loss Value')
plt.ylabel('Frequency')
plt.title('判别器损失分布')
plt.legend()
plt.grid(True, alpha=0.3)

plt.tight_layout()
plt.savefig(os.path.join(log_dir, 'wgan_loss_curve.png'), dpi=300, bbox_inches='tight')
plt.close()
log('WGAN损失曲线已保存到: ' + os.path.join(log_dir, 'wgan_loss_curve.png'))

# Step 5: 生成最终的合成样本
log('生成最终合成样本...')
X_synth = G(torch.tensor(X_adasyn, dtype=torch.float32)).detach().numpy()
log(f'WGAN生成了 {len(X_synth)} 个合成样本')

# 构建最终的训练数据集
X_combined = np.vstack([X_train, X_synth])
y_combined = np.hstack([y_train, np.ones(len(X_synth))])

# 保存模型
torch.save(G.state_dict(), os.path.join(log_dir, 'wgan_generator.pth'))
torch.save(D.state_dict(), os.path.join(log_dir, 'wgan_discriminator.pth'))
log('WGAN模型已保存。')

# 保存损失数据
loss_data = {
    'G_losses': G_losses,
    'D_losses': D_losses,
    'best_params': best_ind
}
with open(os.path.join(log_dir, 'training_losses.pkl'), 'wb') as f:
    pickle.dump(loss_data, f)
log('训练损失数据已保存。')

# 7. 全面的性能评估
log('开始性能评估...')

# 7.1 在测试集上评估
log('=== 测试集评估 ===')
rf_test = RandomForestClassifier(n_estimators=100, random_state=42)
rf_test.fit(X_combined, y_combined)

y_test_pred = rf_test.predict(X_test)
y_test_prob = rf_test.predict_proba(X_test)[:, 1]

# 计算测试集指标
f1_test = f1_score(y_test, y_test_pred)
tn, fp, fn, tp = confusion_matrix(y_test, y_test_pred).ravel()
g_mean_test = np.sqrt((tp/(tp+fn)) * (tn/(tn+fp))) if (tp+fn) > 0 and (tn+fp) > 0 else 0
auc_test = roc_auc_score(y_test, y_test_prob) if len(np.unique(y_test)) > 1 else 0

log(f"测试集结果:")
log(f"  F1-Score: {f1_test:.4f}")
log(f"  G-mean: {g_mean_test:.4f}")
log(f"  AUC: {auc_test:.4f}")
log(f"  混淆矩阵: TN={tn}, FP={fp}, FN={fn}, TP={tp}")

# 7.2 在验证集上评估（用于对比）
log('=== 验证集评估 ===')
y_val_pred = rf_test.predict(X_val)
y_val_prob = rf_test.predict_proba(X_val)[:, 1]

f1_val = f1_score(y_val, y_val_pred)
tn_val, fp_val, fn_val, tp_val = confusion_matrix(y_val, y_val_pred).ravel()
g_mean_val = np.sqrt((tp_val/(tp_val+fn_val)) * (tn_val/(tn_val+fp_val))) if (tp_val+fn_val) > 0 and (tn_val+fp_val) > 0 else 0
auc_val = roc_auc_score(y_val, y_val_prob) if len(np.unique(y_val)) > 1 else 0

log(f"验证集结果:")
log(f"  F1-Score: {f1_val:.4f}")
log(f"  G-mean: {g_mean_val:.4f}")
log(f"  AUC: {auc_val:.4f}")
log(f"  混淆矩阵: TN={tn_val}, FP={fp_val}, FN={fn_val}, TP={tp_val}")

# 7.3 对比原始数据的性能
log('=== 原始数据对比 ===')
rf_original = RandomForestClassifier(n_estimators=100, random_state=42)
rf_original.fit(X_train, y_train)

y_test_pred_orig = rf_original.predict(X_test)
y_test_prob_orig = rf_original.predict_proba(X_test)[:, 1]

f1_orig = f1_score(y_test, y_test_pred_orig)
tn_orig, fp_orig, fn_orig, tp_orig = confusion_matrix(y_test, y_test_pred_orig).ravel()
g_mean_orig = np.sqrt((tp_orig/(tp_orig+fn_orig)) * (tn_orig/(tn_orig+fp_orig))) if (tp_orig+fn_orig) > 0 and (tn_orig+fp_orig) > 0 else 0
auc_orig = roc_auc_score(y_test, y_test_prob_orig) if len(np.unique(y_test)) > 1 else 0

log(f"原始数据结果:")
log(f"  F1-Score: {f1_orig:.4f}")
log(f"  G-mean: {g_mean_orig:.4f}")
log(f"  AUC: {auc_orig:.4f}")

# 7.4 性能提升分析
log('=== 性能提升分析 ===')
f1_improvement = ((f1_test - f1_orig) / f1_orig * 100) if f1_orig > 0 else 0
g_mean_improvement = ((g_mean_test - g_mean_orig) / g_mean_orig * 100) if g_mean_orig > 0 else 0
auc_improvement = ((auc_test - auc_orig) / auc_orig * 100) if auc_orig > 0 else 0

log(f"性能提升:")
log(f"  F1-Score提升: {f1_improvement:.2f}%")
log(f"  G-mean提升: {g_mean_improvement:.2f}%")
log(f"  AUC提升: {auc_improvement:.2f}%")

# 保存评估结果
evaluation_results = {
    'test_results': {
        'f1': f1_test,
        'g_mean': g_mean_test,
        'auc': auc_test,
        'confusion_matrix': [tn, fp, fn, tp]
    },
    'validation_results': {
        'f1': f1_val,
        'g_mean': g_mean_val,
        'auc': auc_val,
        'confusion_matrix': [tn_val, fp_val, fn_val, tp_val]
    },
    'original_results': {
        'f1': f1_orig,
        'g_mean': g_mean_orig,
        'auc': auc_orig,
        'confusion_matrix': [tn_orig, fp_orig, fn_orig, tp_orig]
    },
    'improvements': {
        'f1_improvement': f1_improvement,
        'g_mean_improvement': g_mean_improvement,
        'auc_improvement': auc_improvement
    }
}

with open(os.path.join(log_dir, 'evaluation_results.pkl'), 'wb') as f:
    pickle.dump(evaluation_results, f)

log('=== 实验总结 ===')
log(f"GA-ADASYN-WGAN算法在信用数据集上的最终结果:")
log(f"测试集 - F1: {f1_test:.4f}, G-mean: {g_mean_test:.4f}, AUC: {auc_test:.4f}")
log(f"相比原始数据的性能提升: F1({f1_improvement:.2f}%), G-mean({g_mean_improvement:.2f}%), AUC({auc_improvement:.2f}%)")
log('所有结果已保存到: ' + log_dir)