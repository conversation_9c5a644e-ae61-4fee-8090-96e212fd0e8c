============================================================
开始GA-ADASYN-WGAN参数优化
============================================================
数据集信息:
  训练集大小: (456, 15)
  验证集大小: (131, 15)
  测试集大小: (66, 15)
  特征维度: 15
  不平衡率: 1.18

GA参数设置:
  种群大小: 15
  进化代数: 8
  交叉概率: 0.7
  变异概率: 0.3


GA优化完成！
============================================================
GA优化结果
============================================================
最佳适应度值: 0.892653

最佳ADASYN参数:
  邻居数 k = 8
  平衡参数 alpha = 0.9824

最佳WGAN参数:
  生成器:
    层数 G_layers = 4
    隐藏单元数 G_units = 256
    学习率 G_lr = 0.000057
  判别器:
    层数 D_layers = 2
    隐藏单元数 D_units = 64
    学习率 D_lr = 0.000156
  训练参数:
    梯度惩罚系数 lambda_gp = 9.8896
    判别器训练次数 n_critic = 4
    批量大小 batch_size = 128

前3个最优参数组合:
  第1名 (适应度: 0.892653):
    ADASYN: k=8, alpha=0.9824
    WGAN: G_lr=0.000057, D_lr=0.000156, lambda_gp=9.8896
  第2名 (适应度: 0.886040):
    ADASYN: k=8, alpha=0.9750
    WGAN: G_lr=0.000052, D_lr=0.000414, lambda_gp=11.7728
  第3名 (适应度: 0.885405):
    ADASYN: k=8, alpha=0.9556
    WGAN: G_lr=0.000074, D_lr=0.000414, lambda_gp=11.7728
GA优化结果已保存。

开始最终WGAN模型训练...
最终训练参数:
  ADASYN: k=8, alpha=0.9824
  WGAN: G_layers=4, G_units=256, G_lr=0.000057
        D_layers=2, D_units=64, D_lr=0.000156
        lambda_gp=9.8896, n_critic=4, batch_size=128
ADASYN生成了 13 个少数类样本
开始WGAN训练，共 500 轮...
Epoch 0/500, Loss_G: -0.0556, Loss_D: 7.4899
Epoch 50/500, Loss_G: -2.0520, Loss_D: 1.4634
Epoch 100/500, Loss_G: -0.4022, Loss_D: -0.4536
Epoch 150/500, Loss_G: -0.6100, Loss_D: -0.1288
Epoch 200/500, Loss_G: -0.5924, Loss_D: -0.1515
Epoch 250/500, Loss_G: -0.8151, Loss_D: -0.2166
Epoch 300/500, Loss_G: -1.1492, Loss_D: -0.1548
Epoch 350/500, Loss_G: -1.2980, Loss_D: -0.0934
Epoch 400/500, Loss_G: -1.5693, Loss_D: -0.0661
Epoch 450/500, Loss_G: -1.3859, Loss_D: -0.0969
WGAN训练完成
WGAN损失曲线已保存到: GAAD/result\wgan_loss_curve.png
生成最终合成样本...
WGAN生成了 13 个合成样本
WGAN模型已保存。
训练损失数据已保存。
开始性能评估...
=== 测试集评估 ===
测试集结果:
  F1-Score: 0.8421
  G-mean: 0.8628
  AUC: 0.9417
  混淆矩阵: TN=33, FP=5, FN=4, TP=24
=== 验证集评估 ===
验证集结果:
  F1-Score: 0.8621
  G-mean: 0.8747
  AUC: 0.9237
  混淆矩阵: TN=65, FP=7, FN=9, TP=50
=== 原始数据对比 ===
原始数据结果:
  F1-Score: 0.8421
  G-mean: 0.8628
  AUC: 0.9450
=== 性能提升分析 ===
性能提升:
  F1-Score提升: 0.00%
  G-mean提升: 0.00%
  AUC提升: -0.35%
=== 实验总结 ===
GA-ADASYN-WGAN算法在信用数据集上的最终结果:
测试集 - F1: 0.8421, G-mean: 0.8628, AUC: 0.9417
相比原始数据的性能提升: F1(0.00%), G-mean(0.00%), AUC(-0.35%)
所有结果已保存到: GAAD/result
