#!/usr/bin/env python3
"""
测试GA-ADASYN-WGAN算法的简化版本
用于验证代码的基本功能
"""

import pandas as pd
import numpy as np
from sklearn.preprocessing import LabelEncoder, MinMaxScaler
from imblearn.over_sampling import ADASYN, SMOTE
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import f1_score, confusion_matrix, roc_auc_score
from deap import base, creator, tools, algorithms
import random
import torch
import torch.nn as nn
import torch.optim as optim
import os
import warnings
warnings.filterwarnings('ignore')

# 设置随机种子
np.random.seed(42)
torch.manual_seed(42)
random.seed(42)

print("开始测试GA-ADASYN-WGAN算法...")

# 1. 生成测试数据
print("生成测试数据...")
from sklearn.datasets import make_classification

X, y = make_classification(
    n_samples=1000,
    n_features=10,
    n_informative=8,
    n_redundant=2,
    n_clusters_per_class=1,
    weights=[0.9, 0.1],  # 不平衡数据
    random_state=42
)

print(f"数据集大小: {X.shape}")
print(f"不平衡率: {sum(y==0)/sum(y==1):.2f}")

# 2. 数据预处理
scaler = MinMaxScaler()
X_scaled = scaler.fit_transform(X)

# 划分数据集
X_train_val, X_test, y_train_val, y_test = train_test_split(
    X_scaled, y, test_size=0.2, random_state=42, stratify=y)
X_train, X_val, y_train, y_val = train_test_split(
    X_train_val, y_train_val, test_size=0.25, random_state=42, stratify=y_train_val)

input_dim = X_train.shape[1]
print(f"训练集: {X_train.shape}, 验证集: {X_val.shape}, 测试集: {X_test.shape}")

# 3. 定义ADASYN函数
def adasyn_generate(X, y, k=5, alpha=0.98):
    alpha = min(1.0, max(0.95, float(alpha)))
    k = min(int(k), max(1, sum(y==1)-1))
    
    minority_count = sum(y == 1)
    if minority_count < 2:
        print(f"少数类样本数量不足({minority_count})")
        return np.empty((0, X.shape[1]))
    
    adasyn = ADASYN(sampling_strategy=alpha, n_neighbors=k, random_state=42)
    try:
        X_res, y_res = adasyn.fit_resample(X, y)
        new_samples = X_res[len(X):][y_res[len(y):] == 1]
        print(f"ADASYN生成了 {len(new_samples)} 个新样本")
        return new_samples
    except Exception as e:
        print(f"ADASYN采样失败: {e}")
        return np.empty((0, X.shape[1]))

# 4. 定义WGAN模型
def get_G_D(input_dim, G_layers=3, G_units=128, D_layers=2, D_units=64):
    class Generator(nn.Module):
        def __init__(self):
            super().__init__()
            layers = [nn.Linear(input_dim, G_units), nn.BatchNorm1d(G_units), nn.ReLU()]
            for _ in range(G_layers-2):
                layers += [nn.Linear(G_units, G_units), nn.BatchNorm1d(G_units), nn.ReLU()]
            layers += [nn.Linear(G_units, input_dim), nn.Tanh()]
            self.model = nn.Sequential(*layers)
        
        def forward(self, x):
            return self.model(x)
    
    class Discriminator(nn.Module):
        def __init__(self):
            super().__init__()
            layers = [nn.Linear(input_dim, D_units), nn.LeakyReLU(0.2)]
            for _ in range(D_layers-1):
                layers += [nn.Linear(D_units, D_units), nn.LeakyReLU(0.2)]
            layers += [nn.Linear(D_units, 1)]
            self.model = nn.Sequential(*layers)
        
        def forward(self, x):
            return self.model(x)
    
    return Generator(), Discriminator()

def compute_gradient_penalty(D, real_samples, fake_samples, lambda_gp=10, device='cpu'):
    batch_size = real_samples.size(0)
    alpha = torch.rand(batch_size, 1).to(device)
    
    interpolates = alpha * real_samples + (1 - alpha) * fake_samples
    interpolates = interpolates.to(device)
    interpolates.requires_grad_(True)
    
    d_interpolates = D(interpolates)
    
    gradients = torch.autograd.grad(
        outputs=d_interpolates,
        inputs=interpolates,
        grad_outputs=torch.ones_like(d_interpolates),
        create_graph=True,
        retain_graph=True,
        only_inputs=True
    )[0]
    
    gradient_penalty = ((gradients.norm(2, dim=1) - 1) ** 2).mean() * lambda_gp
    return gradient_penalty

# 5. 测试完整流程
print("\n测试完整的GA-ADASYN-WGAN流程...")

# 测试参数
test_params = [5, 0.98, 3, 128, 1e-4, 2, 64, 1e-4, 10, 3, 32]
k, alpha, G_layers, G_units, G_lr, D_layers, D_units, D_lr, lambda_gp, n_critic, batch_size = test_params

print(f"测试参数: k={k}, alpha={alpha}, batch_size={batch_size}")

# Step 1: ADASYN采样
print("Step 1: ADASYN采样...")
X_adasyn = adasyn_generate(X_train, y_train, k=k, alpha=alpha)

if X_adasyn.shape[0] == 0:
    print("ADASYN失败，使用SMOTE作为备选")
    smote = SMOTE(sampling_strategy=alpha, k_neighbors=k, random_state=42)
    X_res, y_res = smote.fit_resample(X_train, y_train)
    X_adasyn = X_res[len(X_train):][y_res[len(y_train):] == 1]

print(f"生成的ADASYN样本数: {len(X_adasyn)}")

# Step 2: WGAN训练
print("Step 2: WGAN训练...")
G, D = get_G_D(input_dim, G_layers, G_units, D_layers, D_units)
optimizer_G = optim.RMSprop(G.parameters(), lr=G_lr)
optimizer_D = optim.RMSprop(D.parameters(), lr=D_lr)

G_losses, D_losses = [], []
epochs = 50  # 减少训练轮数用于测试

for epoch in range(epochs):
    # 训练判别器
    for _ in range(n_critic):
        real_idx = np.random.choice(X_train.shape[0], min(batch_size, len(X_train)), replace=True)
        real_samples = torch.tensor(X_train[real_idx], dtype=torch.float32)
        
        adasyn_idx = np.random.choice(X_adasyn.shape[0], min(batch_size, len(X_adasyn)), replace=True)
        adasyn_input = torch.tensor(X_adasyn[adasyn_idx], dtype=torch.float32)
        fake_samples = G(adasyn_input)
        
        loss_D = -torch.mean(D(real_samples)) + torch.mean(D(fake_samples.detach()))
        gradient_penalty = compute_gradient_penalty(D, real_samples, fake_samples.detach(), lambda_gp)
        loss_D += gradient_penalty
        
        optimizer_D.zero_grad()
        loss_D.backward()
        optimizer_D.step()
    
    # 训练生成器
    adasyn_idx = np.random.choice(X_adasyn.shape[0], min(batch_size, len(X_adasyn)), replace=True)
    adasyn_input = torch.tensor(X_adasyn[adasyn_idx], dtype=torch.float32)
    fake_samples = G(adasyn_input)
    loss_G = -torch.mean(D(fake_samples))
    
    optimizer_G.zero_grad()
    loss_G.backward()
    optimizer_G.step()
    
    G_losses.append(loss_G.item())
    D_losses.append(loss_D.item())
    
    if epoch % 10 == 0:
        print(f"Epoch {epoch}: G_loss={loss_G.item():.4f}, D_loss={loss_D.item():.4f}")

print("WGAN训练完成")

# Step 3: 生成合成样本并评估
print("Step 3: 生成合成样本并评估...")
X_synth = G(torch.tensor(X_adasyn, dtype=torch.float32)).detach().numpy()
print(f"生成的合成样本数: {len(X_synth)}")

# 构建平衡数据集
X_combined = np.vstack([X_train, X_synth])
y_combined = np.hstack([y_train, np.ones(len(X_synth))])

# 评估性能
rf = RandomForestClassifier(n_estimators=50, random_state=42)
rf.fit(X_combined, y_combined)

y_pred = rf.predict(X_test)
y_prob = rf.predict_proba(X_test)[:, 1]

f1 = f1_score(y_test, y_pred)
tn, fp, fn, tp = confusion_matrix(y_test, y_pred).ravel()
g_mean = np.sqrt((tp/(tp+fn)) * (tn/(tn+fp))) if (tp+fn) > 0 and (tn+fp) > 0 else 0
auc = roc_auc_score(y_test, y_prob)

print(f"\n测试结果:")
print(f"F1-Score: {f1:.4f}")
print(f"G-mean: {g_mean:.4f}")
print(f"AUC: {auc:.4f}")
print(f"混淆矩阵: TN={tn}, FP={fp}, FN={fn}, TP={tp}")

# 对比原始数据
rf_orig = RandomForestClassifier(n_estimators=50, random_state=42)
rf_orig.fit(X_train, y_train)
y_pred_orig = rf_orig.predict(X_test)
f1_orig = f1_score(y_test, y_pred_orig)

print(f"\n性能对比:")
print(f"原始数据 F1-Score: {f1_orig:.4f}")
print(f"GA-ADASYN-WGAN F1-Score: {f1:.4f}")
print(f"F1提升: {((f1-f1_orig)/f1_orig*100):.2f}%")

print("\n测试完成！算法基本功能正常。")
