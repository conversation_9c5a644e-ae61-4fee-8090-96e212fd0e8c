"""
Yeast数据集DAG-WGAN方法快速测试脚本
验证实现的正确性并生成示例结果
"""

import os
import sys
import numpy as np
import pandas as pd
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import f1_score, roc_auc_score
import warnings
warnings.filterwarnings('ignore')

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from yeast import (
    load_yeast_data,
    generate_balanced_data_dag_wgan,
    plot_loss_comparison,
    geometric_mean_score,
    create_demo_losses
)

def test_data_loading():
    """测试数据加载功能"""
    print("🔍 测试数据加载...")
    
    X, y, minority_count, majority_count, imbalance_ratio = load_yeast_data()
    
    if X is not None:
        print(f"✅ 数据加载成功")
        print(f"   数据形状: {X.shape}")
        print(f"   类别分布: 少数类{minority_count}, 多数类{majority_count}")
        return True, X, y
    else:
        print("❌ 数据加载失败")
        return False, None, None

def test_dag_wgan_generation():
    """测试DAG-WGAN数据生成"""
    print("\n🔍 测试DAG-WGAN数据生成...")
    
    # 加载数据
    X, y, _, _, _ = load_yeast_data()
    if X is None:
        return False
    
    # 划分数据
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.3, stratify=y, random_state=42
    )
    
    # 测试参数
    test_params = {
        'alpha': 0.6,
        'lambda_gp': 10,
        'lr': 1e-4,
        'batch_size': 32,
        'n_critic': 3,  # 减少以加快测试
        'k_neighbors': 5,
        'latent_dim': 64,
        'n_epochs': 50  # 减少训练轮数
    }
    
    try:
        X_balanced, y_balanced, losses = generate_balanced_data_dag_wgan(
            X_train, y_train, test_params, use_ga_params=False
        )
        
        print(f"✅ DAG-WGAN生成成功")
        print(f"   平衡后数据形状: {X_balanced.shape}")
        print(f"   类别分布: {np.bincount(y_balanced)}")
        
        return True, X_balanced, y_balanced, losses
        
    except Exception as e:
        print(f"❌ DAG-WGAN生成失败: {e}")
        return False, None, None, None

def test_classification():
    """测试分类性能"""
    print("\n🔍 测试分类性能...")
    
    success, X_balanced, y_balanced, losses = test_dag_wgan_generation()
    if not success:
        return False
    
    # 划分测试数据
    X, y, _, _, _ = load_yeast_data()
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.3, stratify=y, random_state=42
    )
    
    try:
        # 训练分类器
        rf = RandomForestClassifier(n_estimators=50, random_state=42)
        rf.fit(X_balanced, y_balanced)
        
        # 预测
        y_pred = rf.predict(X_test)
        y_pred_proba = rf.predict_proba(X_test)[:, 1]
        
        # 计算指标
        f1 = f1_score(y_test, y_pred)
        auc = roc_auc_score(y_test, y_pred_proba)
        gmean = geometric_mean_score(y_test, y_pred)
        
        print(f"✅ 分类测试完成")
        print(f"   F1-Score: {f1:.4f}")
        print(f"   AUC-ROC: {auc:.4f}")
        print(f"   G-mean: {gmean:.4f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 分类测试失败: {e}")
        return False

def test_loss_visualization():
    """测试损失函数可视化"""
    print("\n🔍 测试损失函数可视化...")
    
    try:
        # 创建模拟损失数据
        losses_default = create_demo_losses("default")
        losses_optimized = create_demo_losses("optimized")
        
        # 生成对比图
        plot_loss_comparison(losses_default, losses_optimized, 'test_yeast_loss_comparison.png')
        
        print(f"✅ 损失函数可视化测试完成")
        print(f"   对比图已保存至: test_yeast_loss_comparison.png")
        
        return True
        
    except Exception as e:
        print(f"❌ 损失函数可视化测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 80)
    print("Yeast数据集DAG-WGAN方法测试")
    print("=" * 80)
    
    tests = [
        ("数据加载", test_data_loading),
        ("DAG-WGAN生成", test_dag_wgan_generation),
        ("分类性能", test_classification),
        ("损失函数可视化", test_loss_visualization)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"运行测试: {test_name}")
        print(f"{'='*60}")
        
        try:
            if test_name == "数据加载":
                result, _, _ = test_func()
            elif test_name == "DAG-WGAN生成":
                result, _, _, _ = test_func()
            else:
                result = test_func()
            
            results.append((test_name, result))
            
            if result:
                print(f"✅ {test_name}测试通过")
            else:
                print(f"❌ {test_name}测试失败")
                
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 总结测试结果
    print(f"\n{'='*80}")
    print("测试结果总结")
    print(f"{'='*80}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<20}: {status}")
    
    print(f"\n总体结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！可以运行完整实验")
        print("运行命令: python yeast.py")
    else:
        print("⚠️  部分测试失败，请检查实现")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
