"""
DAG-WGAN多分类器决策边界对比演示
专门展示多分类器决策边界分析功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from dag_wgan import *

def multi_classifier_demo():
    """DAG-WGAN多分类器决策边界对比演示"""
    
    print("="*80)
    print("DAG-WGAN 多分类器决策边界对比演示")
    print("="*80)
    
    # 加载数据
    import os
    current_dir = os.path.dirname(os.path.abspath(__file__))
    file_path = os.path.join(current_dir, 'data', 'car.data')
    
    if not os.path.exists(file_path):
        alternative_paths = [
            'data/car.data',
            'laborate0823/data/car.data',
            os.path.join(os.getcwd(), 'laborate0823', 'data', 'car.data')
        ]
        
        for alt_path in alternative_paths:
            if os.path.exists(alt_path):
                file_path = alt_path
                break
    
    print(f"使用数据文件: {file_path}")
    X, y, label_encoders = load_and_preprocess_car_data(file_path)
    
    # 数据预处理
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42, stratify=y
    )
    
    minority_mask = y_train == 1
    X_minority = X_train[minority_mask]
    X_majority = X_train[~minority_mask]
    
    # 标准化
    scaler = StandardScaler()
    X_minority_scaled = scaler.fit_transform(X_minority)
    X_majority_scaled = scaler.transform(X_majority)
    X_test_scaled = scaler.transform(X_test)
    
    print(f"数据准备完成: 少数类 {len(X_minority)}, 多数类 {len(X_majority)}")
    
    # 1. 快速训练DAG-WGAN生成样本
    print("\n1. 训练DAG-WGAN生成合成样本...")
    dag_wgan = DAG_WGAN(noise_dim=50)
    
    # 快速训练
    training_results = dag_wgan.train_with_dag_wgan_algorithm(
        X_minority_scaled, X_majority_scaled[:300], epochs=50, batch_size=16
    )
    
    # 生成合成样本
    n_synthetic = 200  # 生成适量样本用于可视化
    synthetic_samples = dag_wgan.generate_adaptive_samples(
        n_synthetic, training_results['synthesis_strategy']
    )
    synthetic_samples_original = scaler.inverse_transform(synthetic_samples)
    
    print(f"✓ 生成了 {len(synthetic_samples_original)} 个合成样本")
    
    # 2. 准备对比数据
    print("\n2. 准备多分类器对比数据...")
    
    # 原始数据（限制数量以加快可视化）
    X_original_combined = np.vstack([X_majority[:300], X_minority])
    y_original_combined = np.hstack([np.zeros(300), np.ones(len(X_minority))])
    
    print(f"原始数据: {len(X_original_combined)} 个样本")
    print(f"合成数据: {len(synthetic_samples_original)} 个样本")
    
    # 3. 多分类器决策边界对比分析
    print("\n3. 执行多分类器决策边界对比分析...")
    
    comprehensive_results = comprehensive_classifier_analysis(
        X_original_combined, y_original_combined,
        synthetic_samples_original, np.ones(len(synthetic_samples_original)),
        method_name="DAG-WGAN"
    )
    
    # 4. 详细分析结果
    print("\n4. 详细分析结果...")
    
    classifier_metrics = comprehensive_results['classifier_metrics']
    ranked_classifiers = comprehensive_results['ranked_classifiers']
    
    print(f"\n📊 分类器性能详细对比:")
    print("=" * 70)
    print(f"{'分类器':<20} {'F1-Min':<8} {'F1-Maj':<8} {'AUC':<8} {'Bal-Acc':<8} {'综合':<8}")
    print("=" * 70)
    
    for clf_name, metrics in classifier_metrics.items():
        composite_score = (0.4 * metrics['f1_minority'] + 
                          0.2 * metrics['f1_majority'] + 
                          0.3 * metrics['auc'] + 
                          0.1 * metrics['balanced_accuracy'])
        
        print(f"{clf_name:<20} {metrics['f1_minority']:<8.3f} {metrics['f1_majority']:<8.3f} "
              f"{metrics['auc']:<8.3f} {metrics['balanced_accuracy']:<8.3f} {composite_score:<8.3f}")
    
    print("=" * 70)
    
    # 5. 分类器特性分析
    print(f"\n5. 分类器特性分析:")
    
    # 找出各项指标的最佳分类器
    best_f1_minority = max(classifier_metrics.items(), key=lambda x: x[1]['f1_minority'])
    best_auc = max(classifier_metrics.items(), key=lambda x: x[1]['auc'])
    best_balanced = max(classifier_metrics.items(), key=lambda x: x[1]['balanced_accuracy'])
    
    print(f"🎯 最佳F1-Score (Minority): {best_f1_minority[0]} ({best_f1_minority[1]['f1_minority']:.3f})")
    print(f"🎯 最佳AUC-ROC: {best_auc[0]} ({best_auc[1]['auc']:.3f})")
    print(f"🎯 最佳平衡准确率: {best_balanced[0]} ({best_balanced[1]['balanced_accuracy']:.3f})")
    print(f"🏆 综合最佳: {ranked_classifiers[0][0]} ({ranked_classifiers[0][1]:.3f})")
    
    # 6. DAG-WGAN效果评估
    print(f"\n6. DAG-WGAN合成样本效果评估:")
    
    avg_f1_minority = np.mean([m['f1_minority'] for m in classifier_metrics.values()])
    avg_auc = np.mean([m['auc'] for m in classifier_metrics.values()])
    std_f1_minority = np.std([m['f1_minority'] for m in classifier_metrics.values()])
    std_auc = np.std([m['auc'] for m in classifier_metrics.values()])
    
    print(f"📈 平均性能:")
    print(f"  - 平均F1-Score (Minority): {avg_f1_minority:.4f} ± {std_f1_minority:.4f}")
    print(f"  - 平均AUC-ROC: {avg_auc:.4f} ± {std_auc:.4f}")
    
    print(f"📊 稳定性评估:")
    if std_f1_minority < 0.05 and std_auc < 0.05:
        stability = "优秀"
        stability_icon = "🟢"
    elif std_f1_minority < 0.1 and std_auc < 0.1:
        stability = "良好"
        stability_icon = "🟡"
    else:
        stability = "一般"
        stability_icon = "🟠"
    
    print(f"  - 性能稳定性: {stability_icon} {stability}")
    print(f"  - F1-Score标准差: {std_f1_minority:.4f}")
    print(f"  - AUC标准差: {std_auc:.4f}")
    
    # 7. 建议和总结
    print(f"\n7. 建议和总结:")
    
    if avg_f1_minority > 0.8 and avg_auc > 0.9:
        quality_assessment = "优秀"
        quality_icon = "🌟"
        recommendation = "DAG-WGAN生成的合成样本质量优秀，适合在生产环境中使用"
    elif avg_f1_minority > 0.6 and avg_auc > 0.8:
        quality_assessment = "良好"
        quality_icon = "✅"
        recommendation = "DAG-WGAN生成的合成样本质量良好，建议进一步优化参数"
    else:
        quality_assessment = "需要改进"
        quality_icon = "⚠️"
        recommendation = "建议调整DAG-WGAN参数或增加训练轮次"
    
    print(f"{quality_icon} 合成样本质量评估: {quality_assessment}")
    print(f"💡 建议: {recommendation}")
    
    print(f"\n🎨 生成的可视化文件:")
    print(f"  - 多分类器决策边界对比: dag-wgan_multi_classifier_boundaries.png")
    print(f"  - 分类器性能热图: dag-wgan_classifier_performance_heatmap.png")
    
    return comprehensive_results

if __name__ == "__main__":
    try:
        print("启动DAG-WGAN多分类器决策边界对比演示...")
        results = multi_classifier_demo()
        print("\n🎉 多分类器决策边界对比演示成功完成!")
        print("\n📋 演示总结:")
        print("✅ 成功对比了6种不同分类器的决策边界")
        print("✅ 生成了详细的性能对比热图")
        print("✅ 验证了DAG-WGAN合成样本的有效性")
        print("✅ 提供了分类器选择建议")
    except Exception as e:
        print(f"\n💥 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
