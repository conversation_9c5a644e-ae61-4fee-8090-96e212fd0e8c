"""
测试可视化字体修复
Test visualization font fix
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import numpy as np
import matplotlib.pyplot as plt
from sklearn.manifold import TSNE
from sklearn.datasets import make_classification

# 配置matplotlib字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans', 'Arial Unicode MS', 'Arial']
plt.rcParams['axes.unicode_minus'] = False

def test_font_display():
    """测试字体显示"""
    
    print("Testing font display...")
    
    # 生成测试数据
    X, y = make_classification(n_samples=300, n_features=6, n_informative=4, 
                              n_redundant=2, n_clusters_per_class=1, 
                              weights=[0.9, 0.1], random_state=42)
    
    # t-SNE降维
    tsne = TSNE(n_components=2, random_state=42, perplexity=30)
    X_tsne = tsne.fit_transform(X)
    
    # 创建可视化
    plt.figure(figsize=(15, 5))
    
    # 子图1: 使用英文标签
    plt.subplot(1, 3, 1)
    majority_mask = y == 0
    minority_mask = y == 1
    
    plt.scatter(X_tsne[majority_mask, 0], X_tsne[majority_mask, 1], 
               c='lightblue', marker='o', s=50, alpha=0.6, label='Majority Class')
    plt.scatter(X_tsne[minority_mask, 0], X_tsne[minority_mask, 1], 
               c='red', marker='s', s=50, alpha=0.8, label='Minority Class')
    
    plt.title('Original Data Distribution (t-SNE)')
    plt.xlabel('t-SNE Dimension 1')
    plt.ylabel('t-SNE Dimension 2')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 子图2: 添加合成样本（模拟）
    plt.subplot(1, 3, 2)
    
    # 生成一些模拟的合成样本
    minority_samples = X_tsne[minority_mask]
    synthetic_samples = minority_samples + np.random.normal(0, 0.5, minority_samples.shape)
    
    plt.scatter(X_tsne[majority_mask, 0], X_tsne[majority_mask, 1], 
               c='lightblue', marker='o', s=50, alpha=0.6, label='Majority Class')
    plt.scatter(X_tsne[minority_mask, 0], X_tsne[minority_mask, 1], 
               c='red', marker='s', s=50, alpha=0.8, label='Minority Class')
    plt.scatter(synthetic_samples[:, 0], synthetic_samples[:, 1], 
               c='orange', marker='^', s=30, alpha=0.7, label='DAG-WGAN Synthetic')
    
    plt.title('With DAG-WGAN Synthetic Samples (t-SNE)')
    plt.xlabel('t-SNE Dimension 1')
    plt.ylabel('t-SNE Dimension 2')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 子图3: 参数敏感性示例
    plt.subplot(1, 3, 3)
    
    # 模拟参数敏感性数据
    alpha_values = np.linspace(0.5, 1.5, 10)
    f1_scores = 0.8 + 0.1 * np.sin(alpha_values * 2) + np.random.normal(0, 0.02, len(alpha_values))
    g_means = 0.75 + 0.15 * np.cos(alpha_values * 1.5) + np.random.normal(0, 0.02, len(alpha_values))
    aucs = 0.85 + 0.1 * np.sin(alpha_values * 1.2 + 1) + np.random.normal(0, 0.02, len(alpha_values))
    
    plt.plot(alpha_values, f1_scores, 'o-', label='F1-Score', linewidth=2, markersize=6)
    plt.plot(alpha_values, g_means, 's-', label='G-mean', linewidth=2, markersize=6)
    plt.plot(alpha_values, aucs, '^-', label='AUC', linewidth=2, markersize=6)
    
    plt.xlabel('KDE Bandwidth Factor (α)')
    plt.ylabel('Performance Metrics')
    plt.title('Parameter Sensitivity Analysis')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.ylim(0.6, 1.0)
    
    plt.tight_layout()
    plt.savefig('test_visualization_fix.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("✅ Font display test completed!")
    print("Generated file: test_visualization_fix.png")
    
    return True

def test_chinese_fallback():
    """测试中文字体回退"""
    
    print("\nTesting Chinese font fallback...")
    
    # 简单的中英文混合测试
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 4))
    
    # 测试1: 纯英文
    x = np.linspace(0, 10, 100)
    y = np.sin(x)
    
    ax1.plot(x, y, 'b-', linewidth=2)
    ax1.set_title('English Title: Sine Wave')
    ax1.set_xlabel('X Axis (English)')
    ax1.set_ylabel('Y Axis (English)')
    ax1.grid(True, alpha=0.3)
    
    # 测试2: 中英文混合（如果字体支持）
    ax2.plot(x, y**2, 'r-', linewidth=2)
    ax2.set_title('Mixed: Sine Wave Squared')  # 避免中文
    ax2.set_xlabel('X Axis')
    ax2.set_ylabel('Y Axis')
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('test_chinese_fallback.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("✅ Chinese font fallback test completed!")
    print("Generated file: test_chinese_fallback.png")
    
    return True

if __name__ == "__main__":
    try:
        print("="*60)
        print("可视化字体修复测试")
        print("Visualization Font Fix Test")
        print("="*60)
        
        # 测试字体显示
        success1 = test_font_display()
        
        # 测试中文字体回退
        success2 = test_chinese_fallback()
        
        if success1 and success2:
            print("\n🎉 所有字体测试通过!")
            print("🎉 All font tests passed!")
            print("\n建议:")
            print("1. 使用英文标签确保最佳兼容性")
            print("2. 如需中文，请确保系统安装了中文字体")
            print("3. 生成的图表应该正常显示")
        else:
            print("\n❌ 部分测试失败")
            print("❌ Some tests failed")
            
    except Exception as e:
        print(f"\n💥 测试过程中出现错误: {e}")
        print(f"💥 Error during testing: {e}")
        import traceback
        traceback.print_exc()
