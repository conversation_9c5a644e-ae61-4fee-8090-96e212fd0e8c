#!/usr/bin/env python3
"""
GA-ADASYN-WGAN Research Configuration
====================================

研究框架的配置文件
包含所有实验参数、数据集配置和模型设置

作者：Research Team
日期：2025-07-12
"""

# 研究配置
RESEARCH_CONFIG = {
    # GA优化ADASYN参数配置
    'ga_adasyn': {
        'population_size': 50,          # 种群大小
        'generations': 100,             # 进化代数
        'crossover_prob': 0.8,          # 交叉概率
        'mutation_prob': 0.1,           # 变异概率
        'k_range': (3, 15),             # 邻居数k的范围
        'alpha_range': (0.1, 1.0),      # 平衡参数α的范围
        'tournament_size': 3,           # 锦标赛选择大小
        'elitism': True,                # 是否使用精英策略
        'convergence_threshold': 0.001   # 收敛阈值
    },
    
    # GA优化WGAN参数配置
    'ga_wgan': {
        'population_size': 50,          # 种群大小
        'generations': 50,              # 进化代数（较少，因为WGAN训练耗时）
        'crossover_prob': 0.8,          # 交叉概率
        'mutation_prob': 0.1,           # 变异概率
        'lambda_range': (0.1, 10.0),    # 梯度惩罚系数λ的范围
        'n_critic_range': (1, 10),      # 判别器训练次数的范围
        'lr_range': (0.0001, 0.01),     # 学习率的范围
        'batch_size_options': [32, 64, 128, 256],  # 批量大小选项
        'tournament_size': 3,           # 锦标赛选择大小
        'evaluation_epochs': 50         # GA评估时的训练轮数
    },
    
    # WGAN训练配置
    'wgan': {
        'max_epochs': 500,              # 最大训练轮数
        'convergence_threshold': 0.001,  # 收敛阈值
        'convergence_patience': 10,      # 收敛耐心（连续多少轮变化小于阈值）
        'generator_layers': [128, 256, 512],    # 生成器隐藏层
        'discriminator_layers': [256, 128, 64], # 判别器隐藏层
        'activation_g': 'relu',          # 生成器激活函数
        'activation_d': 'leaky_relu',    # 判别器激活函数
        'optimizer': 'rmsprop',          # 优化器类型
        'save_interval': 50,             # 模型保存间隔
        'log_interval': 10               # 日志输出间隔
    },
    
    # 实验设置
    'experiment': {
        'cv_folds': 10,                 # 交叉验证折数
        'test_size': 0.2,               # 测试集比例
        'validation_size': 0.2,         # 验证集比例
        'random_state': 42,             # 随机种子
        'n_jobs': -1,                   # 并行作业数
        'scoring_metrics': ['f1', 'g_mean', 'auc'],  # 评估指标
        'statistical_test': 'wilcoxon', # 统计检验方法
        'significance_level': 0.05       # 显著性水平
    },
    
    # 数据集配置
    'datasets': {
        'credit': {
            'path': '../data/credit.data',
            'target_class': '+',
            'header': None,
            'na_values': '?',
            'description': 'German Credit Dataset'
        },
        'glass6': {
            'path': '../data/glass.data',
            'target_class': 'vgood',
            'header': None,
            'description': 'Glass Identification Dataset'
        },
        'ecoli': {
            'path': '../data/ecoli.data',
            'target_class': 'minority',
            'header': None,
            'description': 'E.coli Dataset'
        },
        'yeast': {
            'path': '../data/yeast.data',
            'target_class': 'minority',
            'header': None,
            'description': 'Yeast Dataset'
        }
    },
    
    # 可视化配置
    'visualization': {
        'figure_size': (15, 10),        # 图形大小
        'dpi': 300,                     # 图形分辨率
        'font_size': 12,                # 字体大小
        'color_palette': 'Set2',        # 颜色调色板
        'save_format': 'png',           # 保存格式
        'chinese_font': ['SimHei', 'Microsoft YaHei'],  # 中文字体
        'style': 'seaborn-v0_8'         # 图形风格
    },
    
    # 日志配置
    'logging': {
        'level': 'INFO',                # 日志级别
        'format': '[%(asctime)s] [%(levelname)s] %(message)s',
        'date_format': '%Y-%m-%d %H:%M:%S',
        'file_encoding': 'utf-8',
        'max_file_size': '10MB',        # 最大日志文件大小
        'backup_count': 5               # 备份文件数量
    },
    
    # 结果保存配置
    'output': {
        'base_directory': 'research_results',
        'subdirectories': {
            'results': 'results',       # 结果文件
            'figures': 'figures',       # 图表文件
            'models': 'models',         # 模型文件
            'logs': 'logs',             # 日志文件
            'reports': 'reports'        # 报告文件
        },
        'file_formats': {
            'results': 'pkl',           # 结果文件格式
            'tables': 'csv',            # 表格文件格式
            'figures': 'png',           # 图表文件格式
            'reports': 'html'           # 报告文件格式
        }
    }
}

# 基线方法配置
BASELINE_METHODS = {
    'original': {
        'name': 'Original Data',
        'description': '原始不平衡数据',
        'enabled': True
    },
    'smote': {
        'name': 'SMOTE',
        'description': 'Synthetic Minority Oversampling Technique',
        'parameters': {
            'sampling_strategy': 'minority',
            'k_neighbors': 5,
            'random_state': 42
        },
        'enabled': True
    },
    'adasyn': {
        'name': 'ADASYN',
        'description': 'Adaptive Synthetic Sampling',
        'parameters': {
            'sampling_strategy': 'minority',
            'n_neighbors': 5,
            'random_state': 42
        },
        'enabled': True
    },
    'borderline_smote': {
        'name': 'Borderline-SMOTE',
        'description': 'Borderline SMOTE',
        'parameters': {
            'sampling_strategy': 'minority',
            'k_neighbors': 5,
            'random_state': 42
        },
        'enabled': False  # 可选方法
    }
}

# 分类器配置
CLASSIFIER_CONFIG = {
    'random_forest': {
        'name': 'Random Forest',
        'parameters': {
            'n_estimators': 100,
            'random_state': 42,
            'n_jobs': -1,
            'class_weight': 'balanced'
        },
        'enabled': True
    },
    'svm': {
        'name': 'Support Vector Machine',
        'parameters': {
            'kernel': 'rbf',
            'random_state': 42,
            'class_weight': 'balanced'
        },
        'enabled': False  # 可选分类器
    },
    'xgboost': {
        'name': 'XGBoost',
        'parameters': {
            'random_state': 42,
            'eval_metric': 'logloss'
        },
        'enabled': False  # 可选分类器
    }
}

# 评估指标配置
EVALUATION_METRICS = {
    'f1_score': {
        'name': 'F1-Score',
        'description': '精确率和召回率的调和平均',
        'higher_better': True,
        'range': [0, 1]
    },
    'g_mean': {
        'name': 'G-mean',
        'description': '敏感性和特异性的几何平均',
        'higher_better': True,
        'range': [0, 1]
    },
    'auc': {
        'name': 'AUC',
        'description': 'ROC曲线下面积',
        'higher_better': True,
        'range': [0, 1]
    },
    'precision': {
        'name': 'Precision',
        'description': '精确率',
        'higher_better': True,
        'range': [0, 1]
    },
    'recall': {
        'name': 'Recall',
        'description': '召回率',
        'higher_better': True,
        'range': [0, 1]
    },
    'specificity': {
        'name': 'Specificity',
        'description': '特异性',
        'higher_better': True,
        'range': [0, 1]
    }
}

# 硬件配置
HARDWARE_CONFIG = {
    'use_gpu': False,                   # 是否使用GPU
    'gpu_device': 'cuda:0',             # GPU设备
    'cpu_cores': -1,                    # CPU核心数（-1表示使用所有核心）
    'memory_limit': '8GB',              # 内存限制
    'parallel_jobs': 4                  # 并行作业数
}

# 实验重现性配置
REPRODUCIBILITY_CONFIG = {
    'random_seed': 42,                  # 全局随机种子
    'numpy_seed': 42,                   # NumPy随机种子
    'torch_seed': 42,                   # PyTorch随机种子
    'python_seed': 42,                  # Python随机种子
    'deterministic': True,              # 确定性计算
    'benchmark': False                  # 基准测试模式
}

def get_config():
    """
    获取完整配置
    
    Returns:
        dict: 完整的配置字典
    """
    return {
        'research': RESEARCH_CONFIG,
        'baselines': BASELINE_METHODS,
        'classifiers': CLASSIFIER_CONFIG,
        'metrics': EVALUATION_METRICS,
        'hardware': HARDWARE_CONFIG,
        'reproducibility': REPRODUCIBILITY_CONFIG
    }

def validate_config(config):
    """
    验证配置的有效性
    
    Args:
        config (dict): 配置字典
        
    Returns:
        bool: 配置是否有效
    """
    required_keys = ['research', 'baselines', 'classifiers', 'metrics']
    
    for key in required_keys:
        if key not in config:
            print(f"配置错误: 缺少必需的配置项 '{key}'")
            return False
    
    # 验证数据集路径
    import os
    for dataset_name, dataset_config in config['research']['datasets'].items():
        if not os.path.exists(dataset_config['path']):
            print(f"警告: 数据集文件不存在 '{dataset_config['path']}'")
    
    print("配置验证通过")
    return True

if __name__ == "__main__":
    # 测试配置
    config = get_config()
    validate_config(config)
    
    print("配置文件加载成功")
    print(f"包含 {len(config['research']['datasets'])} 个数据集")
    print(f"包含 {len([m for m in config['baselines'].values() if m['enabled']])} 个基线方法")
    print(f"包含 {len([c for c in config['classifiers'].values() if c['enabled']])} 个分类器")
