# GA-ADASYN-WGAN Research Framework

## 研究背景与方法概述

基于遗传算法优化ADASYN和WGAN参数的不平衡数据处理研究框架，符合顶级期刊论文标准的完整实验设计。

### 核心创新点

1. **GA优化ADASYN参数**：使用遗传算法自动优化ADASYN的邻居数k和平衡参数α
2. **GA优化WGAN参数**：优化梯度惩罚系数、判别器训练次数、学习率、批量大小
3. **端到端训练流程**：GA-ADASYN生成样本 → WGAN训练至纳什平衡 → 性能评估
4. **严格实验设计**：多数据集、交叉验证、统计检验、可视化分析

### 方法流程

```
原始不平衡数据
    ↓
GA优化ADASYN参数(k, α)
    ↓
ADASYN生成少数类样本
    ↓
GA优化WGAN参数(λ, n_critic, lr, batch_size)
    ↓
WGAN训练至收敛(纳什平衡)
    ↓
生成平衡数据集
    ↓
RF分类器评估(F1-Score, G-mean, AUC)
```

## 安装要求

### 环境依赖

```bash
Python >= 3.8
numpy >= 1.19.0
pandas >= 1.3.0
scikit-learn >= 1.0.0
imbalanced-learn >= 0.8.0
torch >= 1.9.0
deap >= 1.3.0
matplotlib >= 3.3.0
seaborn >= 0.11.0
scipy >= 1.7.0
```

### 安装命令

```bash
pip install numpy pandas scikit-learn imbalanced-learn torch deap matplotlib seaborn scipy
```

## 快速开始

### 1. 基本使用

```python
from ga_adasyn_wgan_research import GAAdaSynWGANResearch, ExperimentManager

# 初始化研究框架
research_framework = GAAdaSynWGANResearch()
experiment_manager = ExperimentManager(research_framework)

# 运行单个数据集实验
result = experiment_manager.run_complete_experiment('credit')
```

### 2. 自定义配置

```python
from config import get_config

# 获取默认配置
config = get_config()

# 修改GA参数
config['research']['ga_adasyn']['population_size'] = 100
config['research']['ga_adasyn']['generations'] = 200

# 初始化框架
research_framework = GAAdaSynWGANResearch(config['research'])
```

### 3. 运行完整实验

```bash
python ga_adasyn_wgan_research.py
```

## 详细使用指南

### 数据集配置

在 `config.py` 中配置数据集：

```python
'datasets': {
    'your_dataset': {
        'path': 'path/to/your/data.csv',
        'target_class': 'positive_class_label',
        'header': None,  # 或者指定列名
        'description': 'Your Dataset Description'
    }
}
```

### GA参数调优

#### ADASYN参数优化

```python
'ga_adasyn': {
    'population_size': 50,      # 种群大小
    'generations': 100,         # 进化代数
    'k_range': (3, 15),         # 邻居数范围
    'alpha_range': (0.1, 1.0),  # 平衡参数范围
    'crossover_prob': 0.8,      # 交叉概率
    'mutation_prob': 0.1        # 变异概率
}
```

#### WGAN参数优化

```python
'ga_wgan': {
    'population_size': 50,              # 种群大小
    'generations': 50,                  # 进化代数
    'lambda_range': (0.1, 10.0),       # 梯度惩罚系数
    'n_critic_range': (1, 10),         # 判别器训练次数
    'lr_range': (0.0001, 0.01),        # 学习率范围
    'batch_size_options': [32, 64, 128, 256]  # 批量大小选项
}
```

### WGAN网络架构

```python
'wgan': {
    'generator_layers': [128, 256, 512],    # 生成器隐藏层
    'discriminator_layers': [256, 128, 64], # 判别器隐藏层
    'max_epochs': 500,                      # 最大训练轮数
    'convergence_threshold': 0.001          # 收敛阈值
}
```

## 实验结果分析

### 输出文件结构

```
research_results/
├── results/
│   ├── credit_experiment_results.pkl      # 实验结果数据
│   └── ...
├── figures/
│   ├── wgan_training_losses.png           # WGAN训练损失图
│   ├── credit_performance_comparison.png  # 性能比较图
│   └── ...
├── models/
│   ├── wgan_generator.pth                 # 训练好的生成器
│   ├── wgan_discriminator.pth             # 训练好的判别器
│   └── ...
└── logs/
    └── research.log                       # 详细日志
```

### 性能指标

1. **F1-Score**: 精确率和召回率的调和平均
2. **G-mean**: 敏感性和特异性的几何平均
3. **AUC**: ROC曲线下面积

### 统计分析

- 10折交叉验证
- Wilcoxon signed-rank test (p<0.05)
- 性能提升百分比分析

## 高级功能

### 1. 自定义适应度函数

```python
class CustomAdaSynOptimizer(AdaSynGAOptimizer):
    def _evaluate_fitness(self, individual):
        # 自定义适应度计算
        k, alpha = individual
        # ... 你的评估逻辑
        return (fitness_value,)
```

### 2. 添加新的基线方法

```python
def run_custom_baseline(self, X_train, y_train, X_test, y_test):
    # 实现你的基线方法
    # ...
    return evaluation_result
```

### 3. 自定义可视化

```python
def plot_custom_analysis(self, results, save_path):
    # 自定义分析图表
    plt.figure(figsize=(12, 8))
    # ... 你的绘图代码
    plt.savefig(save_path)
```

## 实验复现

### 设置随机种子

```python
RANDOM_SEED = 42
np.random.seed(RANDOM_SEED)
torch.manual_seed(RANDOM_SEED)
random.seed(RANDOM_SEED)
```

### 保存实验配置

所有实验参数和结果都会自动保存，确保实验可重现。

## 性能优化建议

### 1. 计算资源优化

- 使用GPU加速WGAN训练（设置 `use_gpu=True`）
- 调整并行作业数（`n_jobs`参数）
- 减少GA种群大小和代数以加快实验

### 2. 内存优化

- 对大数据集使用批量处理
- 及时释放不需要的变量
- 使用数据生成器而非一次性加载

### 3. 实验效率

- 先在小数据集上测试参数
- 使用早停机制避免过度训练
- 并行运行多个数据集实验

## 故障排除

### 常见问题

1. **ADASYN采样失败**
   - 检查少数类样本数量是否足够
   - 调整k值范围和alpha值

2. **WGAN训练不收敛**
   - 调整学习率和梯度惩罚系数
   - 增加训练轮数或调整收敛阈值

3. **内存不足**
   - 减少批量大小
   - 使用更小的网络架构

4. **GPU相关错误**
   - 检查CUDA安装
   - 设置 `use_gpu=False` 使用CPU

### 调试模式

```python
# 启用详细日志
import logging
logging.basicConfig(level=logging.DEBUG)

# 使用小规模参数测试
config['research']['ga_adasyn']['generations'] = 5
config['research']['ga_wgan']['generations'] = 3
```

## 贡献指南

欢迎贡献代码和改进建议！

1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 发起 Pull Request

## 引用

如果您在研究中使用了此框架，请引用：

```bibtex
@article{ga_adasyn_wgan_2025,
  title={GA-ADASYN-WGAN: A Genetic Algorithm Optimized Framework for Imbalanced Data Classification},
  author={Research Team},
  journal={Your Journal},
  year={2025}
}
```

## 许可证

MIT License

## 联系方式

如有问题或建议，请联系：<EMAIL>
