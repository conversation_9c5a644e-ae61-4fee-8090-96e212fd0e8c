#!/usr/bin/env python3
"""
GA-ADASYN-WGAN Research Framework
=================================

基于遗传算法优化ADASYN和WGAN参数的不平衡数据处理研究方案
符合顶级期刊论文标准的完整实验设计

研究背景：
- 集成ADASYN过采样和WGAN生成对抗网络
- 使用GA自动优化超参数，提升方法鲁棒性
- 在多个公开数据集上进行严格评估

作者：Research Team
日期：2025-07-12
版本：1.0
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.preprocessing import LabelEncoder, MinMaxScaler
from sklearn.model_selection import StratifiedKFold, train_test_split
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import f1_score, roc_auc_score, confusion_matrix
from imblearn.over_sampling import ADASYN, SMOTE
from deap import base, creator, tools, algorithms
import torch
import torch.nn as nn
import torch.optim as optim
import random
import os
import pickle
import warnings
from scipy import stats
import time
from collections import defaultdict

warnings.filterwarnings('ignore')

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 设置随机种子确保可重现性
RANDOM_SEED = 42
np.random.seed(RANDOM_SEED)
torch.manual_seed(RANDOM_SEED)
random.seed(RANDOM_SEED)

class GAAdaSynWGANResearch:
    """
    GA-ADASYN-WGAN研究框架主类
    
    实现完整的研究流程：
    1. GA优化ADASYN参数（k, α）
    2. GA优化WGAN参数（λ, n_critic, lr, batch_size）
    3. 训练WGAN至纳什平衡
    4. 输出损失函数图
    5. 使用RF分类器评估性能（F1-Score, G-mean, AUC）
    """
    
    def __init__(self, config=None):
        """
        初始化研究框架
        
        Args:
            config (dict): 配置参数字典
        """
        self.config = config or self._default_config()
        self.results = defaultdict(list)
        self.setup_directories()
        
    def _default_config(self):
        """默认配置参数"""
        return {
            # GA参数设置
            'ga_adasyn': {
                'population_size': 50,
                'generations': 100,
                'crossover_prob': 0.8,
                'mutation_prob': 0.1,
                'k_range': (3, 15),
                'alpha_range': (0.1, 1.0)
            },
            'ga_wgan': {
                'population_size': 50,
                'generations': 50,
                'crossover_prob': 0.8,
                'mutation_prob': 0.1,
                'lambda_range': (0.1, 10.0),
                'n_critic_range': (1, 10),
                'lr_range': (0.0001, 0.01),
                'batch_size_options': [32, 64, 128, 256]
            },
            # WGAN训练参数
            'wgan': {
                'max_epochs': 500,
                'convergence_threshold': 0.001,
                'generator_layers': [128, 256, 512],
                'discriminator_layers': [256, 128, 64]
            },
            # 实验设置
            'experiment': {
                'cv_folds': 10,
                'test_size': 0.2,
                'random_state': RANDOM_SEED,
                'n_jobs': -1
            },
            # 数据集配置
            'datasets': {
                'glass6': {'path': '../data/glass.data', 'target_class': 'vgood'},
                'ecoli': {'path': '../data/ecoli.data', 'target_class': 'minority'},
                'yeast': {'path': '../data/yeast.data', 'target_class': 'minority'},
                'credit': {'path': '../data/credit.data', 'target_class': '+'}
            }
        }
    
    def setup_directories(self):
        """创建结果保存目录"""
        self.base_dir = 'research_results'
        self.dirs = {
            'results': os.path.join(self.base_dir, 'results'),
            'figures': os.path.join(self.base_dir, 'figures'),
            'models': os.path.join(self.base_dir, 'models'),
            'logs': os.path.join(self.base_dir, 'logs')
        }
        
        for dir_path in self.dirs.values():
            os.makedirs(dir_path, exist_ok=True)
    
    def log_message(self, message, level='INFO'):
        """记录日志信息"""
        timestamp = time.strftime('%Y-%m-%d %H:%M:%S')
        log_msg = f"[{timestamp}] [{level}] {message}"
        print(log_msg)
        
        with open(os.path.join(self.dirs['logs'], 'research.log'), 'a', encoding='utf-8') as f:
            f.write(log_msg + '\n')

class AdaSynGAOptimizer:
    """
    ADASYN参数的遗传算法优化器
    
    优化参数：
    - k: 邻居数 (3-15)
    - α: 平衡参数 (0.1-1.0)
    
    适应度函数：F1-Score
    """
    
    def __init__(self, X_train, y_train, config):
        self.X_train = X_train
        self.y_train = y_train
        self.config = config
        self.setup_ga()
    
    def setup_ga(self):
        """设置GA框架"""
        # 清理之前的creator定义
        if hasattr(creator, "FitnessMax"):
            del creator.FitnessMax
        if hasattr(creator, "Individual"):
            del creator.Individual
            
        creator.create("FitnessMax", base.Fitness, weights=(1.0,))
        creator.create("Individual", list, fitness=creator.FitnessMax)
        
        self.toolbox = base.Toolbox()
        self.toolbox.register("individual", self._init_individual)
        self.toolbox.register("population", tools.initRepeat, list, 
                             self.toolbox.individual, n=self.config['population_size'])
        self.toolbox.register("evaluate", self._evaluate_fitness)
        self.toolbox.register("mate", tools.cxTwoPoint)
        self.toolbox.register("mutate", self._custom_mutate)
        self.toolbox.register("select", tools.selTournament, tournsize=3)
    
    def _init_individual(self):
        """初始化个体（k, α）"""
        k_min, k_max = self.config['k_range']
        alpha_min, alpha_max = self.config['alpha_range']
        
        k = random.randint(k_min, k_max)
        alpha = random.uniform(alpha_min, alpha_max)
        
        return creator.Individual([k, alpha])
    
    def _evaluate_fitness(self, individual):
        """
        评估个体适应度
        
        使用ADASYN生成样本后，用RF计算F1-Score作为适应度
        """
        k, alpha = individual
        k = max(1, min(int(k), sum(self.y_train == 1) - 1))
        alpha = max(0.1, min(1.0, float(alpha)))
        
        try:
            # 使用ADASYN生成样本
            adasyn = ADASYN(sampling_strategy=alpha, n_neighbors=k, random_state=RANDOM_SEED)
            X_resampled, y_resampled = adasyn.fit_resample(self.X_train, self.y_train)
            
            # 使用RF评估性能
            rf = RandomForestClassifier(n_estimators=50, random_state=RANDOM_SEED)
            
            # 交叉验证评估
            cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=RANDOM_SEED)
            f1_scores = []
            
            for train_idx, val_idx in cv.split(X_resampled, y_resampled):
                X_train_cv, X_val_cv = X_resampled[train_idx], X_resampled[val_idx]
                y_train_cv, y_val_cv = y_resampled[train_idx], y_resampled[val_idx]
                
                rf.fit(X_train_cv, y_train_cv)
                y_pred = rf.predict(X_val_cv)
                f1_scores.append(f1_score(y_val_cv, y_pred, average='binary'))
            
            fitness = np.mean(f1_scores)
            return (fitness,)
            
        except Exception as e:
            print(f"ADASYN评估失败: {e}")
            return (0.0,)
    
    def _custom_mutate(self, individual, indpb=0.2):
        """自定义变异操作"""
        if random.random() < indpb:
            # k变异
            k_min, k_max = self.config['k_range']
            individual[0] = max(k_min, min(k_max, individual[0] + random.randint(-2, 2)))
        
        if random.random() < indpb:
            # α变异
            alpha_min, alpha_max = self.config['alpha_range']
            individual[1] = max(alpha_min, min(alpha_max, 
                                             individual[1] + random.uniform(-0.1, 0.1)))
        
        return individual,
    
    def optimize(self):
        """执行GA优化"""
        print("开始ADASYN参数GA优化...")
        
        # 初始化种群
        population = self.toolbox.population()
        hof = tools.HallOfFame(3)
        
        # 统计信息
        stats = tools.Statistics(lambda ind: ind.fitness.values)
        stats.register("avg", np.mean)
        stats.register("std", np.std)
        stats.register("min", np.min)
        stats.register("max", np.max)
        
        # 执行进化
        population, logbook = algorithms.eaSimple(
            population, self.toolbox,
            cxpb=self.config['crossover_prob'],
            mutpb=self.config['mutation_prob'],
            ngen=self.config['generations'],
            stats=stats,
            halloffame=hof,
            verbose=True
        )
        
        best_individual = hof[0]
        best_k, best_alpha = best_individual
        
        print(f"ADASYN最优参数: k={int(best_k)}, α={best_alpha:.4f}")
        print(f"最优适应度: {best_individual.fitness.values[0]:.4f}")
        
        return {
            'best_params': {'k': int(best_k), 'alpha': best_alpha},
            'best_fitness': best_individual.fitness.values[0],
            'logbook': logbook,
            'hall_of_fame': list(hof)
        }

class WGANGenerator(nn.Module):
    """WGAN生成器网络"""
    
    def __init__(self, input_dim, hidden_dims=[128, 256, 512]):
        super(WGANGenerator, self).__init__()
        
        layers = []
        prev_dim = input_dim
        
        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.BatchNorm1d(hidden_dim),
                nn.ReLU(inplace=True)
            ])
            prev_dim = hidden_dim
        
        # 输出层
        layers.extend([
            nn.Linear(prev_dim, input_dim),
            nn.Tanh()
        ])
        
        self.model = nn.Sequential(*layers)
    
    def forward(self, x):
        return self.model(x)

class WGANDiscriminator(nn.Module):
    """WGAN判别器网络"""
    
    def __init__(self, input_dim, hidden_dims=[256, 128, 64]):
        super(WGANDiscriminator, self).__init__()
        
        layers = []
        prev_dim = input_dim
        
        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.LeakyReLU(0.2, inplace=True)
            ])
            prev_dim = hidden_dim
        
        # 输出层
        layers.append(nn.Linear(prev_dim, 1))
        
        self.model = nn.Sequential(*layers)
    
    def forward(self, x):
        return self.model(x)

class WGANGAOptimizer:
    """
    WGAN参数的遗传算法优化器
    
    优化参数：
    - λ: 梯度惩罚系数 (0.1-10.0)
    - n_critic: 判别器训练次数 (1-10)
    - lr: 学习率 (0.0001-0.01)
    - batch_size: 批量大小 [32, 64, 128, 256]
    
    适应度函数：负Wasserstein距离（最小化生成器损失）
    """
    
    def __init__(self, X_adasyn, X_real, config):
        self.X_adasyn = torch.FloatTensor(X_adasyn)
        self.X_real = torch.FloatTensor(X_real)
        self.config = config
        self.input_dim = X_adasyn.shape[1]
        self.setup_ga()
    
    def setup_ga(self):
        """设置GA框架"""
        self.toolbox = base.Toolbox()
        self.toolbox.register("individual", self._init_individual)
        self.toolbox.register("population", tools.initRepeat, list, 
                             self.toolbox.individual, n=self.config['population_size'])
        self.toolbox.register("evaluate", self._evaluate_fitness)
        self.toolbox.register("mate", tools.cxTwoPoint)
        self.toolbox.register("mutate", self._custom_mutate)
        self.toolbox.register("select", tools.selTournament, tournsize=3)
    
    def _init_individual(self):
        """初始化个体（λ, n_critic, lr, batch_size）"""
        lambda_min, lambda_max = self.config['lambda_range']
        n_critic_min, n_critic_max = self.config['n_critic_range']
        lr_min, lr_max = self.config['lr_range']
        
        lambda_gp = random.uniform(lambda_min, lambda_max)
        n_critic = random.randint(n_critic_min, n_critic_max)
        lr = random.uniform(lr_min, lr_max)
        batch_size = random.choice(self.config['batch_size_options'])
        
        return creator.Individual([lambda_gp, n_critic, lr, batch_size])

    def _evaluate_fitness(self, individual):
        """
        评估WGAN参数适应度

        训练WGAN并返回负Wasserstein距离作为适应度
        """
        lambda_gp, n_critic, lr, batch_size = individual

        # 参数约束
        lambda_gp = max(0.1, min(10.0, float(lambda_gp)))
        n_critic = max(1, min(10, int(n_critic)))
        lr = max(0.0001, min(0.01, float(lr)))
        batch_size = int(batch_size)

        try:
            # 创建WGAN模型
            generator = WGANGenerator(self.input_dim)
            discriminator = WGANDiscriminator(self.input_dim)

            # 优化器
            optimizer_G = optim.RMSprop(generator.parameters(), lr=lr)
            optimizer_D = optim.RMSprop(discriminator.parameters(), lr=lr)

            # 训练循环（简化版，用于GA评估）
            num_epochs = 50  # 减少轮数以加快GA评估
            generator_losses = []

            for epoch in range(num_epochs):
                # 训练判别器
                for _ in range(int(n_critic)):
                    # 真实样本
                    real_batch_size = min(batch_size, len(self.X_real))
                    real_indices = torch.randperm(len(self.X_real))[:real_batch_size]
                    real_samples = self.X_real[real_indices]

                    # 生成假样本
                    adasyn_indices = torch.randperm(len(self.X_adasyn))[:real_batch_size]
                    adasyn_input = self.X_adasyn[adasyn_indices]
                    fake_samples = generator(adasyn_input)

                    # 计算判别器损失
                    d_real = discriminator(real_samples).mean()
                    d_fake = discriminator(fake_samples.detach()).mean()

                    # 梯度惩罚
                    gradient_penalty = self._compute_gradient_penalty(
                        discriminator, real_samples, fake_samples.detach(), lambda_gp)

                    d_loss = -d_real + d_fake + gradient_penalty

                    optimizer_D.zero_grad()
                    d_loss.backward()
                    optimizer_D.step()

                # 训练生成器
                adasyn_indices = torch.randperm(len(self.X_adasyn))[:batch_size]
                adasyn_input = self.X_adasyn[adasyn_indices]
                fake_samples = generator(adasyn_input)

                g_loss = -discriminator(fake_samples).mean()
                generator_losses.append(g_loss.item())

                optimizer_G.zero_grad()
                g_loss.backward()
                optimizer_G.step()

            # 适应度为负生成器损失（最小化）
            fitness = -np.mean(generator_losses[-10:])  # 取最后10轮的平均值
            return (fitness,)

        except Exception as e:
            print(f"WGAN评估失败: {e}")
            return (-999.0,)

    def _compute_gradient_penalty(self, discriminator, real_samples, fake_samples, lambda_gp):
        """计算梯度惩罚"""
        batch_size = real_samples.size(0)
        alpha = torch.rand(batch_size, 1)

        interpolates = alpha * real_samples + (1 - alpha) * fake_samples
        interpolates.requires_grad_(True)

        d_interpolates = discriminator(interpolates)

        gradients = torch.autograd.grad(
            outputs=d_interpolates,
            inputs=interpolates,
            grad_outputs=torch.ones_like(d_interpolates),
            create_graph=True,
            retain_graph=True,
            only_inputs=True
        )[0]

        gradient_penalty = ((gradients.norm(2, dim=1) - 1) ** 2).mean() * lambda_gp
        return gradient_penalty

    def _custom_mutate(self, individual, indpb=0.2):
        """自定义变异操作"""
        if random.random() < indpb:
            # λ变异
            lambda_min, lambda_max = self.config['lambda_range']
            individual[0] = max(lambda_min, min(lambda_max,
                                              individual[0] + random.uniform(-1, 1)))

        if random.random() < indpb:
            # n_critic变异
            n_critic_min, n_critic_max = self.config['n_critic_range']
            individual[1] = max(n_critic_min, min(n_critic_max,
                                                 individual[1] + random.randint(-1, 1)))

        if random.random() < indpb:
            # lr变异
            lr_min, lr_max = self.config['lr_range']
            individual[2] = max(lr_min, min(lr_max,
                                           individual[2] * random.uniform(0.5, 2.0)))

        if random.random() < indpb:
            # batch_size变异
            individual[3] = random.choice(self.config['batch_size_options'])

        return individual,

    def optimize(self):
        """执行GA优化"""
        print("开始WGAN参数GA优化...")

        # 初始化种群
        population = self.toolbox.population()
        hof = tools.HallOfFame(3)

        # 统计信息
        stats = tools.Statistics(lambda ind: ind.fitness.values)
        stats.register("avg", np.mean)
        stats.register("std", np.std)
        stats.register("min", np.min)
        stats.register("max", np.max)

        # 执行进化
        population, logbook = algorithms.eaSimple(
            population, self.toolbox,
            cxpb=self.config['crossover_prob'],
            mutpb=self.config['mutation_prob'],
            ngen=self.config['generations'],
            stats=stats,
            halloffame=hof,
            verbose=True
        )

        best_individual = hof[0]
        best_lambda, best_n_critic, best_lr, best_batch_size = best_individual

        print(f"WGAN最优参数:")
        print(f"  λ={best_lambda:.4f}, n_critic={int(best_n_critic)}")
        print(f"  lr={best_lr:.6f}, batch_size={int(best_batch_size)}")
        print(f"最优适应度: {best_individual.fitness.values[0]:.4f}")

        return {
            'best_params': {
                'lambda_gp': best_lambda,
                'n_critic': int(best_n_critic),
                'lr': best_lr,
                'batch_size': int(best_batch_size)
            },
            'best_fitness': best_individual.fitness.values[0],
            'logbook': logbook,
            'hall_of_fame': list(hof)
        }

class WGANTrainer:
    """
    完整的WGAN训练器

    使用GA优化的参数训练WGAN至纳什平衡
    输出训练损失函数图
    """

    def __init__(self, X_adasyn, X_real, wgan_params, config):
        self.X_adasyn = torch.FloatTensor(X_adasyn)
        self.X_real = torch.FloatTensor(X_real)
        self.wgan_params = wgan_params
        self.config = config
        self.input_dim = X_adasyn.shape[1]

        # 创建模型
        self.generator = WGANGenerator(self.input_dim, config['generator_layers'])
        self.discriminator = WGANDiscriminator(self.input_dim, config['discriminator_layers'])

        # 优化器
        self.optimizer_G = optim.RMSprop(self.generator.parameters(), lr=wgan_params['lr'])
        self.optimizer_D = optim.RMSprop(self.discriminator.parameters(), lr=wgan_params['lr'])

        # 训练记录
        self.training_history = {
            'generator_losses': [],
            'discriminator_losses': [],
            'epochs': []
        }

    def train(self):
        """
        训练WGAN至收敛

        Returns:
            dict: 训练结果和生成的样本
        """
        print("开始WGAN训练...")
        print(f"使用参数: {self.wgan_params}")

        lambda_gp = self.wgan_params['lambda_gp']
        n_critic = self.wgan_params['n_critic']
        batch_size = self.wgan_params['batch_size']
        max_epochs = self.config['max_epochs']
        convergence_threshold = self.config['convergence_threshold']

        prev_g_loss = float('inf')
        convergence_count = 0

        for epoch in range(max_epochs):
            epoch_d_losses = []

            # 训练判别器 n_critic 次
            for _ in range(n_critic):
                # 获取真实样本
                real_batch_size = min(batch_size, len(self.X_real))
                real_indices = torch.randperm(len(self.X_real))[:real_batch_size]
                real_samples = self.X_real[real_indices]

                # 生成假样本
                adasyn_indices = torch.randperm(len(self.X_adasyn))[:real_batch_size]
                adasyn_input = self.X_adasyn[adasyn_indices]
                fake_samples = self.generator(adasyn_input)

                # 计算判别器损失
                d_real = self.discriminator(real_samples).mean()
                d_fake = self.discriminator(fake_samples.detach()).mean()

                # 梯度惩罚
                gradient_penalty = self._compute_gradient_penalty(
                    real_samples, fake_samples.detach(), lambda_gp)

                d_loss = -d_real + d_fake + gradient_penalty
                epoch_d_losses.append(d_loss.item())

                # 更新判别器
                self.optimizer_D.zero_grad()
                d_loss.backward()
                self.optimizer_D.step()

            # 训练生成器
            adasyn_indices = torch.randperm(len(self.X_adasyn))[:batch_size]
            adasyn_input = self.X_adasyn[adasyn_indices]
            fake_samples = self.generator(adasyn_input)

            g_loss = -self.discriminator(fake_samples).mean()

            # 更新生成器
            self.optimizer_G.zero_grad()
            g_loss.backward()
            self.optimizer_G.step()

            # 记录损失
            avg_d_loss = np.mean(epoch_d_losses)
            self.training_history['generator_losses'].append(g_loss.item())
            self.training_history['discriminator_losses'].append(avg_d_loss)
            self.training_history['epochs'].append(epoch)

            # 检查收敛
            if abs(g_loss.item() - prev_g_loss) < convergence_threshold:
                convergence_count += 1
                if convergence_count >= 10:  # 连续10轮变化小于阈值
                    print(f"WGAN在第{epoch}轮收敛")
                    break
            else:
                convergence_count = 0

            prev_g_loss = g_loss.item()

            # 定期输出进度
            if epoch % 50 == 0:
                print(f"Epoch {epoch}: G_loss={g_loss.item():.4f}, D_loss={avg_d_loss:.4f}")

        print("WGAN训练完成")

        # 生成最终样本
        final_samples = self.generator(self.X_adasyn).detach().numpy()

        return {
            'generator': self.generator,
            'discriminator': self.discriminator,
            'training_history': self.training_history,
            'generated_samples': final_samples,
            'converged_epoch': epoch
        }

    def _compute_gradient_penalty(self, real_samples, fake_samples, lambda_gp):
        """计算梯度惩罚"""
        batch_size = real_samples.size(0)
        alpha = torch.rand(batch_size, 1)

        interpolates = alpha * real_samples + (1 - alpha) * fake_samples
        interpolates.requires_grad_(True)

        d_interpolates = self.discriminator(interpolates)

        gradients = torch.autograd.grad(
            outputs=d_interpolates,
            inputs=interpolates,
            grad_outputs=torch.ones_like(d_interpolates),
            create_graph=True,
            retain_graph=True,
            only_inputs=True
        )[0]

        gradient_penalty = ((gradients.norm(2, dim=1) - 1) ** 2).mean() * lambda_gp
        return gradient_penalty

    def plot_training_losses(self, save_path):
        """
        绘制训练损失函数图

        Args:
            save_path (str): 保存路径
        """
        plt.figure(figsize=(15, 10))

        # 主损失曲线
        plt.subplot(2, 3, 1)
        plt.plot(self.training_history['epochs'],
                self.training_history['generator_losses'],
                label='Generator Loss', color='blue', alpha=0.7)
        plt.plot(self.training_history['epochs'],
                self.training_history['discriminator_losses'],
                label='Discriminator Loss', color='red', alpha=0.7)
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.title('WGAN训练损失曲线')
        plt.legend()
        plt.grid(True, alpha=0.3)

        # 平滑损失曲线
        plt.subplot(2, 3, 2)
        window_size = 20
        if len(self.training_history['generator_losses']) > window_size:
            g_smooth = np.convolve(self.training_history['generator_losses'],
                                 np.ones(window_size)/window_size, mode='valid')
            d_smooth = np.convolve(self.training_history['discriminator_losses'],
                                 np.ones(window_size)/window_size, mode='valid')
            epochs_smooth = self.training_history['epochs'][window_size-1:]

            plt.plot(epochs_smooth, g_smooth, label='Generator (Smoothed)', color='blue')
            plt.plot(epochs_smooth, d_smooth, label='Discriminator (Smoothed)', color='red')
            plt.xlabel('Epoch')
            plt.ylabel('Smoothed Loss')
            plt.title('平滑损失曲线')
            plt.legend()
            plt.grid(True, alpha=0.3)

        # 损失分布
        plt.subplot(2, 3, 3)
        plt.hist(self.training_history['generator_losses'], bins=30, alpha=0.7,
                label='Generator', color='blue')
        plt.xlabel('Loss Value')
        plt.ylabel('Frequency')
        plt.title('生成器损失分布')
        plt.legend()
        plt.grid(True, alpha=0.3)

        plt.subplot(2, 3, 4)
        plt.hist(self.training_history['discriminator_losses'], bins=30, alpha=0.7,
                label='Discriminator', color='red')
        plt.xlabel('Loss Value')
        plt.ylabel('Frequency')
        plt.title('判别器损失分布')
        plt.legend()
        plt.grid(True, alpha=0.3)

        # 损失差值
        plt.subplot(2, 3, 5)
        loss_diff = np.array(self.training_history['generator_losses']) - \
                   np.array(self.training_history['discriminator_losses'])
        plt.plot(self.training_history['epochs'], loss_diff, color='green', alpha=0.7)
        plt.xlabel('Epoch')
        plt.ylabel('Loss Difference')
        plt.title('生成器-判别器损失差值')
        plt.grid(True, alpha=0.3)

        # 收敛分析
        plt.subplot(2, 3, 6)
        if len(self.training_history['generator_losses']) > 1:
            g_gradient = np.gradient(self.training_history['generator_losses'])
            plt.plot(self.training_history['epochs'], g_gradient, color='purple', alpha=0.7)
            plt.xlabel('Epoch')
            plt.ylabel('Loss Gradient')
            plt.title('生成器损失梯度（收敛指标）')
            plt.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"训练损失图已保存至: {save_path}")

class PerformanceEvaluator:
    """
    性能评估器

    使用RF分类器评估生成样本的质量
    计算F1-Score, G-mean, AUC等指标
    """

    def __init__(self, config):
        self.config = config
        self.cv_folds = config['cv_folds']
        self.random_state = config['random_state']

    def evaluate_method(self, X_train, y_train, X_test, y_test, method_name="Unknown"):
        """
        评估单个方法的性能

        Args:
            X_train, y_train: 训练数据
            X_test, y_test: 测试数据
            method_name: 方法名称

        Returns:
            dict: 评估结果
        """
        print(f"评估方法: {method_name}")

        # 使用随机森林分类器
        rf = RandomForestClassifier(
            n_estimators=100,
            random_state=self.random_state,
            n_jobs=self.config.get('n_jobs', -1)
        )

        # 交叉验证
        cv = StratifiedKFold(n_splits=self.cv_folds, shuffle=True,
                           random_state=self.random_state)

        cv_f1_scores = []
        cv_g_means = []
        cv_aucs = []

        for fold, (train_idx, val_idx) in enumerate(cv.split(X_train, y_train)):
            X_train_cv, X_val_cv = X_train[train_idx], X_train[val_idx]
            y_train_cv, y_val_cv = y_train[train_idx], y_train[val_idx]

            # 训练模型
            rf.fit(X_train_cv, y_train_cv)

            # 预测
            y_pred = rf.predict(X_val_cv)
            y_prob = rf.predict_proba(X_val_cv)[:, 1]

            # 计算指标
            f1 = f1_score(y_val_cv, y_pred, average='binary')

            # G-mean计算
            tn, fp, fn, tp = confusion_matrix(y_val_cv, y_pred).ravel()
            sensitivity = tp / (tp + fn) if (tp + fn) > 0 else 0
            specificity = tn / (tn + fp) if (tn + fp) > 0 else 0
            g_mean = np.sqrt(sensitivity * specificity)

            # AUC
            auc = roc_auc_score(y_val_cv, y_prob) if len(np.unique(y_val_cv)) > 1 else 0

            cv_f1_scores.append(f1)
            cv_g_means.append(g_mean)
            cv_aucs.append(auc)

        # 最终测试集评估
        rf.fit(X_train, y_train)
        y_test_pred = rf.predict(X_test)
        y_test_prob = rf.predict_proba(X_test)[:, 1]

        test_f1 = f1_score(y_test, y_test_pred, average='binary')

        tn, fp, fn, tp = confusion_matrix(y_test, y_test_pred).ravel()
        test_sensitivity = tp / (tp + fn) if (tp + fn) > 0 else 0
        test_specificity = tn / (tn + fp) if (tn + fp) > 0 else 0
        test_g_mean = np.sqrt(test_sensitivity * test_specificity)

        test_auc = roc_auc_score(y_test, y_test_prob) if len(np.unique(y_test)) > 1 else 0

        results = {
            'method': method_name,
            'cv_results': {
                'f1_scores': cv_f1_scores,
                'g_means': cv_g_means,
                'aucs': cv_aucs,
                'f1_mean': np.mean(cv_f1_scores),
                'f1_std': np.std(cv_f1_scores),
                'g_mean_mean': np.mean(cv_g_means),
                'g_mean_std': np.std(cv_g_means),
                'auc_mean': np.mean(cv_aucs),
                'auc_std': np.std(cv_aucs)
            },
            'test_results': {
                'f1_score': test_f1,
                'g_mean': test_g_mean,
                'auc': test_auc,
                'confusion_matrix': [tn, fp, fn, tp],
                'sensitivity': test_sensitivity,
                'specificity': test_specificity
            }
        }

        print(f"  测试集结果: F1={test_f1:.4f}, G-mean={test_g_mean:.4f}, AUC={test_auc:.4f}")

        return results

    def compare_methods(self, results_list):
        """
        比较多个方法的性能

        Args:
            results_list: 评估结果列表

        Returns:
            dict: 比较结果和统计检验
        """
        print("进行方法间性能比较...")

        # 创建比较表格
        comparison_data = []
        for result in results_list:
            comparison_data.append({
                'Method': result['method'],
                'F1-Score': f"{result['test_results']['f1_score']:.4f}",
                'G-mean': f"{result['test_results']['g_mean']:.4f}",
                'AUC': f"{result['test_results']['auc']:.4f}",
                'CV_F1_Mean': f"{result['cv_results']['f1_mean']:.4f}±{result['cv_results']['f1_std']:.4f}",
                'CV_G-mean_Mean': f"{result['cv_results']['g_mean_mean']:.4f}±{result['cv_results']['g_mean_std']:.4f}",
                'CV_AUC_Mean': f"{result['cv_results']['auc_mean']:.4f}±{result['cv_results']['auc_std']:.4f}"
            })

        comparison_df = pd.DataFrame(comparison_data)

        # 统计检验（Wilcoxon signed-rank test）
        statistical_tests = {}
        if len(results_list) >= 2:
            baseline_result = results_list[0]  # 假设第一个为基线方法

            for i, result in enumerate(results_list[1:], 1):
                # F1-Score检验
                statistic, p_value = stats.wilcoxon(
                    baseline_result['cv_results']['f1_scores'],
                    result['cv_results']['f1_scores']
                )

                statistical_tests[f"{baseline_result['method']}_vs_{result['method']}"] = {
                    'f1_statistic': statistic,
                    'f1_p_value': p_value,
                    'f1_significant': p_value < 0.05
                }

        return {
            'comparison_table': comparison_df,
            'statistical_tests': statistical_tests
        }

    def plot_comparison_results(self, results_list, save_path):
        """
        绘制方法比较结果图

        Args:
            results_list: 评估结果列表
            save_path: 保存路径
        """
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))

        methods = [result['method'] for result in results_list]

        # F1-Score比较
        f1_scores = [result['test_results']['f1_score'] for result in results_list]
        f1_cv_means = [result['cv_results']['f1_mean'] for result in results_list]
        f1_cv_stds = [result['cv_results']['f1_std'] for result in results_list]

        axes[0, 0].bar(methods, f1_scores, alpha=0.7, color='skyblue', label='Test F1')
        axes[0, 0].errorbar(methods, f1_cv_means, yerr=f1_cv_stds,
                           fmt='ro', label='CV F1 Mean±Std')
        axes[0, 0].set_title('F1-Score比较')
        axes[0, 0].set_ylabel('F1-Score')
        axes[0, 0].legend()
        axes[0, 0].tick_params(axis='x', rotation=45)

        # G-mean比较
        g_means = [result['test_results']['g_mean'] for result in results_list]
        g_cv_means = [result['cv_results']['g_mean_mean'] for result in results_list]
        g_cv_stds = [result['cv_results']['g_mean_std'] for result in results_list]

        axes[0, 1].bar(methods, g_means, alpha=0.7, color='lightgreen', label='Test G-mean')
        axes[0, 1].errorbar(methods, g_cv_means, yerr=g_cv_stds,
                           fmt='ro', label='CV G-mean Mean±Std')
        axes[0, 1].set_title('G-mean比较')
        axes[0, 1].set_ylabel('G-mean')
        axes[0, 1].legend()
        axes[0, 1].tick_params(axis='x', rotation=45)

        # AUC比较
        aucs = [result['test_results']['auc'] for result in results_list]
        auc_cv_means = [result['cv_results']['auc_mean'] for result in results_list]
        auc_cv_stds = [result['cv_results']['auc_std'] for result in results_list]

        axes[0, 2].bar(methods, aucs, alpha=0.7, color='salmon', label='Test AUC')
        axes[0, 2].errorbar(methods, auc_cv_means, yerr=auc_cv_stds,
                           fmt='ro', label='CV AUC Mean±Std')
        axes[0, 2].set_title('AUC比较')
        axes[0, 2].set_ylabel('AUC')
        axes[0, 2].legend()
        axes[0, 2].tick_params(axis='x', rotation=45)

        # 综合性能雷达图
        angles = np.linspace(0, 2 * np.pi, 3, endpoint=False).tolist()
        angles += angles[:1]  # 闭合

        ax_radar = plt.subplot(2, 2, 3, projection='polar')

        for i, result in enumerate(results_list):
            values = [
                result['test_results']['f1_score'],
                result['test_results']['g_mean'],
                result['test_results']['auc']
            ]
            values += values[:1]  # 闭合

            ax_radar.plot(angles, values, 'o-', linewidth=2,
                         label=result['method'], alpha=0.7)
            ax_radar.fill(angles, values, alpha=0.25)

        ax_radar.set_xticks(angles[:-1])
        ax_radar.set_xticklabels(['F1-Score', 'G-mean', 'AUC'])
        ax_radar.set_ylim(0, 1)
        ax_radar.set_title('综合性能雷达图')
        ax_radar.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))

        # 性能提升分析
        if len(results_list) >= 2:
            baseline = results_list[0]
            improvements = []

            for result in results_list[1:]:
                f1_improvement = ((result['test_results']['f1_score'] -
                                 baseline['test_results']['f1_score']) /
                                baseline['test_results']['f1_score'] * 100)
                g_improvement = ((result['test_results']['g_mean'] -
                                baseline['test_results']['g_mean']) /
                               baseline['test_results']['g_mean'] * 100)
                auc_improvement = ((result['test_results']['auc'] -
                                  baseline['test_results']['auc']) /
                                 baseline['test_results']['auc'] * 100)

                improvements.append({
                    'method': result['method'],
                    'f1_improvement': f1_improvement,
                    'g_improvement': g_improvement,
                    'auc_improvement': auc_improvement
                })

            if improvements:
                methods_imp = [imp['method'] for imp in improvements]
                f1_imps = [imp['f1_improvement'] for imp in improvements]
                g_imps = [imp['g_improvement'] for imp in improvements]
                auc_imps = [imp['auc_improvement'] for imp in improvements]

                x = np.arange(len(methods_imp))
                width = 0.25

                axes[1, 1].bar(x - width, f1_imps, width, label='F1-Score', alpha=0.7)
                axes[1, 1].bar(x, g_imps, width, label='G-mean', alpha=0.7)
                axes[1, 1].bar(x + width, auc_imps, width, label='AUC', alpha=0.7)

                axes[1, 1].set_xlabel('Methods')
                axes[1, 1].set_ylabel('Improvement (%)')
                axes[1, 1].set_title(f'相对于{baseline["method"]}的性能提升')
                axes[1, 1].set_xticks(x)
                axes[1, 1].set_xticklabels(methods_imp, rotation=45)
                axes[1, 1].legend()
                axes[1, 1].axhline(y=0, color='black', linestyle='-', alpha=0.3)

        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"性能比较图已保存至: {save_path}")

class ExperimentManager:
    """
    实验管理器

    管理完整的GA-ADASYN-WGAN研究流程
    """

    def __init__(self, research_framework):
        self.framework = research_framework
        self.evaluator = PerformanceEvaluator(research_framework.config['experiment'])

    def load_dataset(self, dataset_name, dataset_config):
        """
        加载和预处理数据集

        Args:
            dataset_name: 数据集名称
            dataset_config: 数据集配置

        Returns:
            tuple: (X, y) 预处理后的数据
        """
        self.framework.log_message(f"加载数据集: {dataset_name}")

        try:
            # 这里简化处理，实际应根据数据集格式调整
            if dataset_name == 'credit':
                # 加载信用数据集
                data = pd.read_csv(dataset_config['path'], header=None, na_values='?')
                data = data.dropna()

                X = data.iloc[:, :-1]
                y = data.iloc[:, -1]
                y = (y == dataset_config['target_class']).astype(int)

                # 编码分类特征
                for col in X.columns:
                    if X[col].dtype == 'object':
                        X[col] = LabelEncoder().fit_transform(X[col])

                X = X.values

            else:
                # 其他数据集的处理逻辑
                # 这里使用模拟数据作为示例
                from sklearn.datasets import make_classification
                X, y = make_classification(
                    n_samples=1000, n_features=10, n_informative=8,
                    n_redundant=2, n_clusters_per_class=1,
                    weights=[0.8, 0.2], random_state=RANDOM_SEED
                )

            # 归一化
            scaler = MinMaxScaler()
            X = scaler.fit_transform(X)

            # 数据集统计
            minority_count = sum(y == 1)
            majority_count = sum(y == 0)
            imbalance_ratio = majority_count / minority_count if minority_count > 0 else float('inf')

            self.framework.log_message(f"数据集统计:")
            self.framework.log_message(f"  总样本数: {len(X)}")
            self.framework.log_message(f"  特征数: {X.shape[1]}")
            self.framework.log_message(f"  多数类样本: {majority_count}")
            self.framework.log_message(f"  少数类样本: {minority_count}")
            self.framework.log_message(f"  不平衡率: {imbalance_ratio:.2f}")

            return X, y

        except Exception as e:
            self.framework.log_message(f"数据集加载失败: {e}", 'ERROR')
            raise

    def run_baseline_methods(self, X_train, y_train, X_test, y_test):
        """
        运行基线方法

        Args:
            X_train, y_train: 训练数据
            X_test, y_test: 测试数据

        Returns:
            list: 基线方法评估结果
        """
        baseline_results = []

        # 1. 原始数据
        self.framework.log_message("评估基线方法: 原始数据")
        original_result = self.evaluator.evaluate_method(
            X_train, y_train, X_test, y_test, "Original Data"
        )
        baseline_results.append(original_result)

        # 2. SMOTE
        self.framework.log_message("评估基线方法: SMOTE")
        try:
            smote = SMOTE(sampling_strategy='minority', random_state=RANDOM_SEED)
            X_smote, y_smote = smote.fit_resample(X_train, y_train)

            smote_result = self.evaluator.evaluate_method(
                X_smote, y_smote, X_test, y_test, "SMOTE"
            )
            baseline_results.append(smote_result)
        except Exception as e:
            self.framework.log_message(f"SMOTE评估失败: {e}", 'ERROR')

        # 3. ADASYN (默认参数)
        self.framework.log_message("评估基线方法: ADASYN")
        try:
            adasyn = ADASYN(sampling_strategy='minority', random_state=RANDOM_SEED)
            X_adasyn, y_adasyn = adasyn.fit_resample(X_train, y_train)

            adasyn_result = self.evaluator.evaluate_method(
                X_adasyn, y_adasyn, X_test, y_test, "ADASYN"
            )
            baseline_results.append(adasyn_result)
        except Exception as e:
            self.framework.log_message(f"ADASYN评估失败: {e}", 'ERROR')

        return baseline_results

    def run_ga_adasyn_wgan(self, X_train, y_train, X_test, y_test):
        """
        运行GA-ADASYN-WGAN方法

        Args:
            X_train, y_train: 训练数据
            X_test, y_test: 测试数据

        Returns:
            dict: GA-ADASYN-WGAN评估结果和训练信息
        """
        self.framework.log_message("开始GA-ADASYN-WGAN实验")

        # Step 1: GA优化ADASYN参数
        self.framework.log_message("Step 1: GA优化ADASYN参数")
        adasyn_optimizer = AdaSynGAOptimizer(
            X_train, y_train, self.framework.config['ga_adasyn']
        )
        adasyn_ga_result = adasyn_optimizer.optimize()

        # 使用优化参数生成ADASYN样本
        best_k = adasyn_ga_result['best_params']['k']
        best_alpha = adasyn_ga_result['best_params']['alpha']

        self.framework.log_message(f"最优ADASYN参数: k={best_k}, α={best_alpha:.4f}")

        try:
            adasyn_optimized = ADASYN(
                sampling_strategy=best_alpha,
                n_neighbors=best_k,
                random_state=RANDOM_SEED
            )
            X_adasyn_opt, y_adasyn_opt = adasyn_optimized.fit_resample(X_train, y_train)

            # 提取新生成的少数类样本
            X_adasyn_samples = X_adasyn_opt[len(X_train):][y_adasyn_opt[len(y_train):] == 1]

            self.framework.log_message(f"ADASYN生成样本数: {len(X_adasyn_samples)}")

        except Exception as e:
            self.framework.log_message(f"ADASYN采样失败: {e}", 'ERROR')
            return None

        # Step 2: GA优化WGAN参数
        self.framework.log_message("Step 2: GA优化WGAN参数")
        X_minority = X_train[y_train == 1]

        wgan_optimizer = WGANGAOptimizer(
            X_adasyn_samples, X_minority, self.framework.config['ga_wgan']
        )
        wgan_ga_result = wgan_optimizer.optimize()

        best_wgan_params = wgan_ga_result['best_params']
        self.framework.log_message(f"最优WGAN参数: {best_wgan_params}")

        # Step 3: 使用优化参数训练WGAN
        self.framework.log_message("Step 3: 训练WGAN至纳什平衡")
        wgan_trainer = WGANTrainer(
            X_adasyn_samples, X_minority,
            best_wgan_params, self.framework.config['wgan']
        )

        wgan_training_result = wgan_trainer.train()

        # 保存训练损失图
        loss_plot_path = os.path.join(
            self.framework.dirs['figures'],
            'wgan_training_losses.png'
        )
        wgan_trainer.plot_training_losses(loss_plot_path)

        # Step 4: 构建最终平衡数据集
        self.framework.log_message("Step 4: 构建平衡数据集并评估")
        X_generated = wgan_training_result['generated_samples']

        # 合并原始训练数据和生成样本
        X_balanced = np.vstack([X_train, X_generated])
        y_balanced = np.hstack([y_train, np.ones(len(X_generated))])

        # Step 5: 性能评估
        ga_adasyn_wgan_result = self.evaluator.evaluate_method(
            X_balanced, y_balanced, X_test, y_test, "GA-ADASYN-WGAN"
        )

        # 保存详细结果
        detailed_result = {
            'evaluation_result': ga_adasyn_wgan_result,
            'adasyn_ga_result': adasyn_ga_result,
            'wgan_ga_result': wgan_ga_result,
            'wgan_training_result': wgan_training_result,
            'generated_samples_count': len(X_generated),
            'balanced_dataset_size': len(X_balanced)
        }

        return detailed_result

    def run_complete_experiment(self, dataset_name):
        """
        运行完整实验

        Args:
            dataset_name: 数据集名称

        Returns:
            dict: 完整实验结果
        """
        self.framework.log_message("="*60)
        self.framework.log_message(f"开始完整实验: {dataset_name}")
        self.framework.log_message("="*60)

        # 加载数据集
        dataset_config = self.framework.config['datasets'][dataset_name]
        X, y = self.load_dataset(dataset_name, dataset_config)

        # 划分训练集和测试集
        X_train, X_test, y_train, y_test = train_test_split(
            X, y,
            test_size=self.framework.config['experiment']['test_size'],
            random_state=self.framework.config['experiment']['random_state'],
            stratify=y
        )

        self.framework.log_message(f"数据划分: 训练集{X_train.shape}, 测试集{X_test.shape}")

        # 运行基线方法
        baseline_results = self.run_baseline_methods(X_train, y_train, X_test, y_test)

        # 运行GA-ADASYN-WGAN
        ga_adasyn_wgan_detailed = self.run_ga_adasyn_wgan(X_train, y_train, X_test, y_test)

        if ga_adasyn_wgan_detailed is None:
            self.framework.log_message("GA-ADASYN-WGAN实验失败", 'ERROR')
            return None

        # 合并所有结果
        all_results = baseline_results + [ga_adasyn_wgan_detailed['evaluation_result']]

        # 性能比较
        comparison_result = self.evaluator.compare_methods(all_results)

        # 绘制比较图
        comparison_plot_path = os.path.join(
            self.framework.dirs['figures'],
            f'{dataset_name}_performance_comparison.png'
        )
        self.evaluator.plot_comparison_results(all_results, comparison_plot_path)

        # 保存结果
        experiment_result = {
            'dataset_name': dataset_name,
            'dataset_info': {
                'total_samples': len(X),
                'features': X.shape[1],
                'train_samples': len(X_train),
                'test_samples': len(X_test),
                'imbalance_ratio': sum(y == 0) / sum(y == 1) if sum(y == 1) > 0 else float('inf')
            },
            'baseline_results': baseline_results,
            'ga_adasyn_wgan_detailed': ga_adasyn_wgan_detailed,
            'comparison_result': comparison_result,
            'plots': {
                'training_losses': 'wgan_training_losses.png',
                'performance_comparison': f'{dataset_name}_performance_comparison.png'
            }
        }

        # 保存到文件
        result_file = os.path.join(
            self.framework.dirs['results'],
            f'{dataset_name}_experiment_results.pkl'
        )
        with open(result_file, 'wb') as f:
            pickle.dump(experiment_result, f)

        self.framework.log_message(f"实验结果已保存至: {result_file}")
        self.framework.log_message("实验完成!")

        return experiment_result

def main():
    """
    主函数 - 执行完整的GA-ADASYN-WGAN研究
    """
    print("="*80)
    print("GA-ADASYN-WGAN Research Framework")
    print("基于遗传算法优化ADASYN和WGAN参数的不平衡数据处理研究")
    print("="*80)

    # 初始化研究框架
    research_framework = GAAdaSynWGANResearch()
    experiment_manager = ExperimentManager(research_framework)

    # 可用数据集
    available_datasets = list(research_framework.config['datasets'].keys())

    print(f"可用数据集: {available_datasets}")

    # 运行实验
    all_experiment_results = {}

    for dataset_name in available_datasets:
        try:
            print(f"\n开始处理数据集: {dataset_name}")
            result = experiment_manager.run_complete_experiment(dataset_name)

            if result:
                all_experiment_results[dataset_name] = result
                print(f"数据集 {dataset_name} 实验成功完成")
            else:
                print(f"数据集 {dataset_name} 实验失败")

        except Exception as e:
            print(f"数据集 {dataset_name} 实验出错: {e}")
            research_framework.log_message(f"数据集 {dataset_name} 实验出错: {e}", 'ERROR')

    # 生成总结报告
    if all_experiment_results:
        print("\n" + "="*60)
        print("实验总结报告")
        print("="*60)

        for dataset_name, result in all_experiment_results.items():
            print(f"\n数据集: {dataset_name}")
            print(f"  样本数: {result['dataset_info']['total_samples']}")
            print(f"  特征数: {result['dataset_info']['features']}")
            print(f"  不平衡率: {result['dataset_info']['imbalance_ratio']:.2f}")

            # 找到最佳方法
            best_method = None
            best_f1 = 0

            for method_result in (result['baseline_results'] +
                                [result['ga_adasyn_wgan_detailed']['evaluation_result']]):
                f1_score = method_result['test_results']['f1_score']
                if f1_score > best_f1:
                    best_f1 = f1_score
                    best_method = method_result['method']

            print(f"  最佳方法: {best_method} (F1-Score: {best_f1:.4f})")

        print(f"\n所有结果已保存至: {research_framework.dirs['results']}")
        print(f"图表已保存至: {research_framework.dirs['figures']}")
        print(f"日志已保存至: {research_framework.dirs['logs']}")

    print("\n研究框架执行完成!")

if __name__ == "__main__":
    main()
