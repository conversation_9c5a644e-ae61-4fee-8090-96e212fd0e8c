#!/usr/bin/env python3
"""
GA-ADASYN-WGAN Research Framework - 使用示例
==========================================

展示如何使用研究框架进行不平衡数据分类实验

作者：Research Team
日期：2025-07-12
"""

import numpy as np
import pandas as pd
from sklearn.datasets import make_classification
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import MinMaxScaler
import matplotlib.pyplot as plt
import os

# 导入研究框架
from ga_adasyn_wgan_research import (
    GAAdaSynWGANResearch, 
    ExperimentManager,
    AdaSynGAOptimizer,
    WGANGAOptimizer,
    WGANTrainer,
    PerformanceEvaluator
)
from config import get_config

def create_sample_dataset():
    """
    创建示例不平衡数据集
    """
    print("创建示例不平衡数据集...")
    
    # 生成不平衡数据
    X, y = make_classification(
        n_samples=1000,
        n_features=10,
        n_informative=8,
        n_redundant=2,
        n_clusters_per_class=1,
        weights=[0.85, 0.15],  # 不平衡比例约5.67:1
        random_state=42
    )
    
    # 数据预处理
    scaler = MinMaxScaler()
    X = scaler.fit_transform(X)
    
    print(f"数据集大小: {X.shape}")
    print(f"多数类样本: {sum(y == 0)}")
    print(f"少数类样本: {sum(y == 1)}")
    print(f"不平衡率: {sum(y == 0) / sum(y == 1):.2f}")
    
    return X, y

def example_1_basic_usage():
    """
    示例1：基本使用方法
    """
    print("\n" + "="*60)
    print("示例1：基本使用方法")
    print("="*60)
    
    # 创建数据集
    X, y = create_sample_dataset()
    
    # 划分数据
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42, stratify=y
    )
    
    # 初始化研究框架（使用简化配置以加快演示）
    config = get_config()
    config['research']['ga_adasyn']['generations'] = 10  # 减少代数
    config['research']['ga_wgan']['generations'] = 5
    config['research']['wgan']['max_epochs'] = 100
    
    research_framework = GAAdaSynWGANResearch(config['research'])
    
    # 创建性能评估器
    evaluator = PerformanceEvaluator(config['research']['experiment'])
    
    # 评估原始数据
    print("\n1. 评估原始数据性能...")
    original_result = evaluator.evaluate_method(
        X_train, y_train, X_test, y_test, "Original Data"
    )
    
    # GA优化ADASYN参数
    print("\n2. GA优化ADASYN参数...")
    adasyn_optimizer = AdaSynGAOptimizer(
        X_train, y_train, config['research']['ga_adasyn']
    )
    adasyn_result = adasyn_optimizer.optimize()
    
    print(f"最优ADASYN参数: k={adasyn_result['best_params']['k']}, "
          f"α={adasyn_result['best_params']['alpha']:.4f}")
    
    # 使用优化参数生成ADASYN样本
    from imblearn.over_sampling import ADASYN
    adasyn = ADASYN(
        sampling_strategy=adasyn_result['best_params']['alpha'],
        n_neighbors=adasyn_result['best_params']['k'],
        random_state=42
    )
    X_adasyn, y_adasyn = adasyn.fit_resample(X_train, y_train)
    X_adasyn_samples = X_adasyn[len(X_train):][y_adasyn[len(y_train):] == 1]
    
    print(f"ADASYN生成样本数: {len(X_adasyn_samples)}")
    
    # 评估ADASYN结果
    adasyn_eval_result = evaluator.evaluate_method(
        X_adasyn, y_adasyn, X_test, y_test, "GA-ADASYN"
    )
    
    # 输出结果比较
    print("\n3. 结果比较:")
    print(f"原始数据 - F1: {original_result['test_results']['f1_score']:.4f}, "
          f"G-mean: {original_result['test_results']['g_mean']:.4f}, "
          f"AUC: {original_result['test_results']['auc']:.4f}")
    print(f"GA-ADASYN - F1: {adasyn_eval_result['test_results']['f1_score']:.4f}, "
          f"G-mean: {adasyn_eval_result['test_results']['g_mean']:.4f}, "
          f"AUC: {adasyn_eval_result['test_results']['auc']:.4f}")

def example_2_wgan_training():
    """
    示例2：WGAN训练和优化
    """
    print("\n" + "="*60)
    print("示例2：WGAN训练和优化")
    print("="*60)
    
    # 创建数据集
    X, y = create_sample_dataset()
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42, stratify=y
    )
    
    # 生成ADASYN样本（使用默认参数）
    from imblearn.over_sampling import ADASYN
    adasyn = ADASYN(sampling_strategy=0.8, n_neighbors=5, random_state=42)
    X_adasyn, y_adasyn = adasyn.fit_resample(X_train, y_train)
    X_adasyn_samples = X_adasyn[len(X_train):][y_adasyn[len(y_train):] == 1]
    X_minority = X_train[y_train == 1]
    
    print(f"ADASYN样本数: {len(X_adasyn_samples)}")
    print(f"原始少数类样本数: {len(X_minority)}")
    
    # GA优化WGAN参数
    print("\n1. GA优化WGAN参数...")
    config = get_config()
    config['research']['ga_wgan']['generations'] = 5  # 简化演示
    
    wgan_optimizer = WGANGAOptimizer(
        X_adasyn_samples, X_minority, config['research']['ga_wgan']
    )
    wgan_result = wgan_optimizer.optimize()
    
    print(f"最优WGAN参数: {wgan_result['best_params']}")
    
    # 使用优化参数训练WGAN
    print("\n2. 训练WGAN...")
    config['research']['wgan']['max_epochs'] = 200  # 简化演示
    
    wgan_trainer = WGANTrainer(
        X_adasyn_samples, X_minority,
        wgan_result['best_params'],
        config['research']['wgan']
    )
    
    training_result = wgan_trainer.train()
    
    print(f"WGAN训练完成，收敛于第{training_result['converged_epoch']}轮")
    print(f"生成样本数: {len(training_result['generated_samples'])}")
    
    # 绘制训练损失图
    os.makedirs('example_results', exist_ok=True)
    wgan_trainer.plot_training_losses('example_results/wgan_losses.png')
    print("训练损失图已保存至: example_results/wgan_losses.png")
    
    # 评估最终结果
    print("\n3. 评估最终结果...")
    X_generated = training_result['generated_samples']
    X_balanced = np.vstack([X_train, X_generated])
    y_balanced = np.hstack([y_train, np.ones(len(X_generated))])
    
    evaluator = PerformanceEvaluator(config['research']['experiment'])
    final_result = evaluator.evaluate_method(
        X_balanced, y_balanced, X_test, y_test, "GA-ADASYN-WGAN"
    )
    
    print(f"最终结果 - F1: {final_result['test_results']['f1_score']:.4f}, "
          f"G-mean: {final_result['test_results']['g_mean']:.4f}, "
          f"AUC: {final_result['test_results']['auc']:.4f}")

def example_3_complete_experiment():
    """
    示例3：完整实验流程
    """
    print("\n" + "="*60)
    print("示例3：完整实验流程")
    print("="*60)
    
    # 创建自定义配置
    config = get_config()
    
    # 简化参数以加快演示
    config['research']['ga_adasyn']['generations'] = 8
    config['research']['ga_wgan']['generations'] = 5
    config['research']['wgan']['max_epochs'] = 150
    config['research']['experiment']['cv_folds'] = 5
    
    # 添加示例数据集
    config['research']['datasets']['example'] = {
        'path': 'example_dataset',  # 特殊标识
        'target_class': 1,
        'description': 'Example Imbalanced Dataset'
    }
    
    # 初始化研究框架
    research_framework = GAAdaSynWGANResearch(config['research'])
    experiment_manager = ExperimentManager(research_framework)
    
    # 创建示例数据集并保存
    X, y = create_sample_dataset()
    
    # 模拟数据集加载（实际使用中会从文件加载）
    def mock_load_dataset(dataset_name, dataset_config):
        if dataset_name == 'example':
            return X, y
        else:
            return experiment_manager.load_dataset(dataset_name, dataset_config)
    
    # 替换加载函数
    experiment_manager.load_dataset = mock_load_dataset
    
    # 运行完整实验
    print("\n运行完整实验...")
    result = experiment_manager.run_complete_experiment('example')
    
    if result:
        print("\n实验完成！结果摘要:")
        print(f"数据集: {result['dataset_name']}")
        print(f"样本数: {result['dataset_info']['total_samples']}")
        print(f"不平衡率: {result['dataset_info']['imbalance_ratio']:.2f}")
        
        # 显示各方法性能
        print("\n各方法性能:")
        for method_result in result['baseline_results']:
            print(f"  {method_result['method']}: "
                  f"F1={method_result['test_results']['f1_score']:.4f}")
        
        ga_result = result['ga_adasyn_wgan_detailed']['evaluation_result']
        print(f"  {ga_result['method']}: "
              f"F1={ga_result['test_results']['f1_score']:.4f}")
        
        print(f"\n结果已保存至: {research_framework.dirs['results']}")
        print(f"图表已保存至: {research_framework.dirs['figures']}")

def example_4_custom_configuration():
    """
    示例4：自定义配置和参数
    """
    print("\n" + "="*60)
    print("示例4：自定义配置和参数")
    print("="*60)
    
    # 创建自定义配置
    custom_config = {
        'ga_adasyn': {
            'population_size': 20,      # 较小的种群
            'generations': 15,          # 较少的代数
            'crossover_prob': 0.7,
            'mutation_prob': 0.15,
            'k_range': (3, 8),          # 较小的k范围
            'alpha_range': (0.5, 1.0)   # 较高的alpha范围
        },
        'ga_wgan': {
            'population_size': 20,
            'generations': 10,
            'crossover_prob': 0.7,
            'mutation_prob': 0.15,
            'lambda_range': (1.0, 5.0),
            'n_critic_range': (3, 7),
            'lr_range': (0.0005, 0.005),
            'batch_size_options': [32, 64, 128]
        },
        'wgan': {
            'max_epochs': 100,
            'convergence_threshold': 0.005,
            'generator_layers': [64, 128, 256],     # 较小的网络
            'discriminator_layers': [128, 64, 32]
        },
        'experiment': {
            'cv_folds': 5,
            'test_size': 0.2,
            'random_state': 42,
            'n_jobs': -1
        }
    }
    
    print("使用自定义配置:")
    print(f"  ADASYN GA: 种群{custom_config['ga_adasyn']['population_size']}, "
          f"代数{custom_config['ga_adasyn']['generations']}")
    print(f"  WGAN GA: 种群{custom_config['ga_wgan']['population_size']}, "
          f"代数{custom_config['ga_wgan']['generations']}")
    print(f"  WGAN训练: 最大{custom_config['wgan']['max_epochs']}轮")
    
    # 创建数据集
    X, y = create_sample_dataset()
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42, stratify=y
    )
    
    # 使用自定义配置运行实验
    print("\n运行自定义配置实验...")
    
    # ADASYN优化
    adasyn_optimizer = AdaSynGAOptimizer(X_train, y_train, custom_config['ga_adasyn'])
    adasyn_result = adasyn_optimizer.optimize()
    
    print(f"自定义ADASYN结果: k={adasyn_result['best_params']['k']}, "
          f"α={adasyn_result['best_params']['alpha']:.4f}")
    
    # 评估结果
    from imblearn.over_sampling import ADASYN
    adasyn = ADASYN(
        sampling_strategy=adasyn_result['best_params']['alpha'],
        n_neighbors=adasyn_result['best_params']['k'],
        random_state=42
    )
    X_adasyn, y_adasyn = adasyn.fit_resample(X_train, y_train)
    
    evaluator = PerformanceEvaluator(custom_config['experiment'])
    result = evaluator.evaluate_method(
        X_adasyn, y_adasyn, X_test, y_test, "Custom GA-ADASYN"
    )
    
    print(f"自定义配置结果: F1={result['test_results']['f1_score']:.4f}, "
          f"G-mean={result['test_results']['g_mean']:.4f}, "
          f"AUC={result['test_results']['auc']:.4f}")

def main():
    """
    主函数 - 运行所有示例
    """
    print("GA-ADASYN-WGAN Research Framework - 使用示例")
    print("="*80)
    
    try:
        # 示例1：基本使用
        example_1_basic_usage()
        
        # 示例2：WGAN训练
        example_2_wgan_training()
        
        # 示例3：完整实验
        example_3_complete_experiment()
        
        # 示例4：自定义配置
        example_4_custom_configuration()
        
        print("\n" + "="*80)
        print("所有示例运行完成！")
        print("="*80)
        
    except Exception as e:
        print(f"示例运行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
