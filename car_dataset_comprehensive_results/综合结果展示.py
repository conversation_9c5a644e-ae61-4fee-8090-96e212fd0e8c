"""
Car数据集综合实验结果展示

显示DAG-WGAN vs 七种最先进基线方法的对比结果
"""

import json
import numpy as np

def display_comprehensive_results():
    """显示综合实验结果"""
    
    # 读取结果文件
    import os
    current_dir = os.path.dirname(os.path.abspath(__file__))
    results_path = os.path.join(current_dir, 'detailed_results.json')
    
    with open(results_path, 'r', encoding='utf-8') as f:
        results = json.load(f)
    
    print("=" * 80)
    print("Car数据集: DAG-WGAN vs 七种最先进基线方法 - 综合实验结果")
    print("=" * 80)
    
    # 数据集信息
    dataset_info = results['dataset_info']
    print(f"\n📊 数据集统计:")
    print(f"总样本数: 1,728")
    print(f"少数类(vgood)数目: {dataset_info['minority_count']}")
    print(f"多数类数目: {dataset_info['majority_count']}")
    print(f"不平衡比例: {dataset_info['imbalance_ratio']:.2f}:1")
    
    # 方法结果对比
    all_method_results = results['all_method_results']
    
    print(f"\n🏆 综合性能对比表:")
    print(f"{'方法':<25} {'F-measure':<12} {'AUC':<12} {'G-means':<12}")
    print("-" * 65)
    
    # 方法顺序和显示名称
    method_order = [
        'baseline_no_sampling', 'SMOTE (k=5)', 'ADASYN (default)', 
        'Focal Loss (α=0.25, γ=2)', 'WGAN-GP (λ=10)', 'BAGAN',
        'ADASYN-GAN', 'Density-Weighted WGAN-GP', 'DAG-WGAN'
    ]
    
    method_display_names = {
        'baseline_no_sampling': '无过采样基线',
        'SMOTE (k=5)': 'SMOTE (k=5)',
        'ADASYN (default)': 'ADASYN (默认)',
        'Focal Loss (α=0.25, γ=2)': 'Focal Loss (α=0.25,γ=2)',
        'WGAN-GP (λ=10)': 'WGAN-GP (λ=10)',
        'BAGAN': 'BAGAN',
        'ADASYN-GAN': 'ADASYN-GAN',
        'Density-Weighted WGAN-GP': 'Density-WGAN-GP',
        'DAG-WGAN': 'DAG-WGAN'
    }
    
    # 收集所有结果用于排名
    all_scores = {'f_measure': [], 'auc': [], 'g_means': []}
    valid_methods = []
    
    for method in method_order:
        if method in all_method_results and 'error' not in all_method_results[method]:
            result = all_method_results[method]
            valid_methods.append(method)
            all_scores['f_measure'].append(result['f_measure'])
            all_scores['auc'].append(result['auc'])
            all_scores['g_means'].append(result['g_means'])
    
    # 显示结果
    for method in method_order:
        if method in all_method_results and 'error' not in all_method_results[method]:
            result = all_method_results[method]
            display_name = method_display_names.get(method, method)
            
            f_score = result['f_measure']
            auc_score = result['auc']
            g_score = result['g_means']
            
            # 标记最佳结果
            f_mark = " 🥇" if f_score == max(all_scores['f_measure']) else ""
            auc_mark = " 🥇" if auc_score == max(all_scores['auc']) else ""
            g_mark = " 🥇" if g_score == max(all_scores['g_means']) else ""
            
            print(f"{display_name:<25} {f_score:<12.4f}{f_mark:<3} {auc_score:<12.4f}{auc_mark:<3} {g_score:<12.4f}{g_mark}")
        else:
            display_name = method_display_names.get(method, method)
            print(f"{display_name:<25} {'ERROR':<12} {'ERROR':<12} {'ERROR':<12}")
    
    # 最佳性能统计
    best_scores = results.get('best_scores', {})
    best_methods = results.get('best_methods', {})
    
    print(f"\n🏅 最佳性能方法:")
    print(f"  F-measure: {best_methods.get('f_measure', 'N/A')} ({best_scores.get('f_measure', 0):.4f})")
    print(f"  AUC: {best_methods.get('auc', 'N/A')} ({best_scores.get('auc', 0):.4f})")
    print(f"  G-means: {best_methods.get('g_means', 'N/A')} ({best_scores.get('g_means', 0):.4f})")
    
    # DAG-WGAN排名
    dag_wgan_rankings = results.get('dag_wgan_rankings', {})
    total_methods = len(valid_methods)
    
    print(f"\n📊 DAG-WGAN排名分析:")
    print(f"  F-measure: 第{dag_wgan_rankings.get('f_measure', 'N/A')}名 / {total_methods}个方法")
    print(f"  AUC: 第{dag_wgan_rankings.get('auc', 'N/A')}名 / {total_methods}个方法")
    print(f"  G-means: 第{dag_wgan_rankings.get('g_means', 'N/A')}名 / {total_methods}个方法")
    
    # 性能分组分析
    print(f"\n🎯 性能分组分析:")
    
    # F-measure分组
    f_scores = [all_method_results[m]['f_measure'] for m in valid_methods if m in all_method_results]
    f_scores_unique = sorted(list(set(f_scores)), reverse=True)
    
    print(f"\nF-measure分组:")
    for i, score in enumerate(f_scores_unique):
        methods_in_group = []
        for method in valid_methods:
            if method in all_method_results and all_method_results[method]['f_measure'] == score:
                methods_in_group.append(method_display_names.get(method, method))
        
        if i == 0:
            print(f"  🥇 第一梯队 ({score:.4f}): {', '.join(methods_in_group)}")
        elif i == 1:
            print(f"  🥈 第二梯队 ({score:.4f}): {', '.join(methods_in_group)}")
        elif i == 2:
            print(f"  🥉 第三梯队 ({score:.4f}): {', '.join(methods_in_group)}")
        else:
            print(f"  📊 第{i+1}梯队 ({score:.4f}): {', '.join(methods_in_group)}")
    
    # 混淆矩阵对比
    print(f"\n🎯 关键方法混淆矩阵对比:")
    
    key_methods = ['baseline_no_sampling', 'DAG-WGAN']
    for method in key_methods:
        if method in all_method_results:
            result = all_method_results[method]
            display_name = method_display_names.get(method, method)
            print(f"\n{display_name}:")
            print(f"  TP={result['tp']}, TN={result['tn']}, FP={result['fp']}, FN={result['fn']}")
            print(f"  Precision: {result['precision']:.4f}")
            print(f"  Recall: {result['recall']:.4f}")
            print(f"  Specificity: {result['specificity']:.4f}")
    
    # DAG-WGAN训练信息
    training_info = results.get('training_info', {})
    if 'history' in training_info:
        history = training_info['history']
        print(f"\n⚙️ DAG-WGAN训练详情:")
        print(f"  训练状态: {training_info['status']}")
        print(f"  ADASYN生成样本: {history['adasyn_samples']}")
        print(f"  训练轮数: 100 epochs")
        print(f"  数据平衡: 从25.58:1 → 1.00:1")
        print(f"  动态λ_gp调整: 10.00 → 9.29")
    
    # 关键发现
    print(f"\n🔍 关键发现:")
    print(f"  📈 Car数据集原始质量极高，基线F-measure达97.44%")
    print(f"  🎯 五种方法(包括基线)达到最高F-measure: 0.9744")
    print(f"  📊 DAG-WGAN在第二梯队，F-measure: 0.9500")
    print(f"  ⚖️ 所有方法AUC都超过99%，显示数据可分性优秀")
    print(f"  🔄 DAG-WGAN成功实现完美数据平衡(1:1)")
    
    # 实际意义
    print(f"\n💡 实际意义:")
    print(f"  🎯 在高质量数据集上，简单方法可能已经最优")
    print(f"  📊 DAG-WGAN的主要价值在于数据平衡而非性能提升")
    print(f"  🔬 复杂方法在低质量或极度不平衡数据上可能更有价值")
    print(f"  ⚖️ 需要综合考虑性能、平衡性、泛化能力等多个维度")
    
    # 生成文件
    print(f"\n📁 生成文件:")
    print(f"  📈 comprehensive_comparison.png - 综合性能对比图")
    print(f"  🔍 tsne_visualization.png - t-SNE样本分布可视化")
    print(f"  📋 detailed_results.json - 完整实验数据")
    print(f"  📄 综合实验结果分析.md - 详细分析报告")
    
    print("=" * 80)

if __name__ == '__main__':
    display_comprehensive_results()
