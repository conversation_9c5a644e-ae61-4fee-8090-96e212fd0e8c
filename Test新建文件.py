"""
Car数据集处理测试文件

处理data/car.data数据
将vgood作为少数类(1)，其他合并为多数类(0)

使用方法:
- SMOTE [2] (k=5个邻居)
- ADASYN [3] (默认参数)

评估指标:
- F-measure
- AUC
- G-means

作者: 研究团队
日期: 2024-08-07
"""

import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import LabelEncoder
from sklearn.metrics import f1_score, roc_auc_score, confusion_matrix, classification_report
from imblearn.over_sampling import SMOTE, ADASYN
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Agg')  # 非交互式后端
import os
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def load_car_data(data_path='data/car.data'):
    """
    加载Car数据集
    
    参数:
        data_path: 数据文件路径
        
    返回:
        X, y, feature_names, label_info
    """
    logger.info(f"加载数据集: {data_path}")
    
    # 定义列名
    columns = ['buying', 'maint', 'doors', 'persons', 'lug_boot', 'safety', 'class']
    
    try:
        # 读取数据
        df = pd.read_csv(data_path, names=columns)
        logger.info(f"原始数据集形状: {df.shape}")
        
        # 显示原始类别分布
        print("原始类别分布:")
        class_counts = df['class'].value_counts()
        for class_name, count in class_counts.items():
            print(f"  {class_name}: {count}")
        
        # 将vgood作为少数类(1)，其他合并为多数类(0)
        df['binary_class'] = (df['class'] == 'vgood').astype(int)
        
        # 分离特征和标签
        X = df.drop(['class', 'binary_class'], axis=1)
        y = df['binary_class'].values
        
        # 对分类特征进行标签编码
        label_encoders = {}
        for column in X.columns:
            le = LabelEncoder()
            X[column] = le.fit_transform(X[column])
            label_encoders[column] = le
            print(f"特征 {column}: {list(le.classes_)}")
        
        X = X.values
        
        # 统计信息
        minority_count = np.sum(y == 1)
        majority_count = np.sum(y == 0)
        imbalance_ratio = majority_count / minority_count
        
        label_info = {
            'total_samples': len(y),
            'minority_count': minority_count,
            'majority_count': majority_count,
            'imbalance_ratio': imbalance_ratio,
            'class_distribution': class_counts.to_dict()
        }
        
        print(f"\n二分类转换后:")
        print(f"总样本数: {len(y)}")
        print(f"少数类(vgood)数目: {minority_count}")
        print(f"多数类数目: {majority_count}")
        print(f"不平衡比例: {imbalance_ratio:.2f}:1")
        
        return X, y, X.shape[1], label_info
        
    except FileNotFoundError:
        logger.error(f"数据文件未找到: {data_path}")
        raise
    except Exception as e:
        logger.error(f"数据加载失败: {e}")
        raise


def calculate_gmean(y_true, y_pred):
    """计算G-means指标"""
    tn, fp, fn, tp = confusion_matrix(y_true, y_pred).ravel()
    sensitivity = tp / (tp + fn) if (tp + fn) > 0 else 0
    specificity = tn / (tn + fp) if (tn + fp) > 0 else 0
    return np.sqrt(sensitivity * specificity)


def evaluate_method(X_train, y_train, X_test, y_test, method_name="方法"):
    """
    评估方法性能
    
    参数:
        X_train: 训练特征
        y_train: 训练标签
        X_test: 测试特征
        y_test: 测试标签
        method_name: 方法名称
        
    返回:
        评估结果字典
    """
    # 使用随机森林分类器
    clf = RandomForestClassifier(random_state=42)
    clf.fit(X_train, y_train)
    
    # 预测
    y_pred = clf.predict(X_test)
    y_pred_proba = clf.predict_proba(X_test)[:, 1]
    
    # 计算指标
    f_measure = f1_score(y_test, y_pred)
    auc_score = roc_auc_score(y_test, y_pred_proba)
    g_means = calculate_gmean(y_test, y_pred)
    
    # 混淆矩阵
    tn, fp, fn, tp = confusion_matrix(y_test, y_pred).ravel()
    
    # 其他指标
    precision = tp / (tp + fp) if (tp + fp) > 0 else 0
    recall = tp / (tp + fn) if (tp + fn) > 0 else 0
    specificity = tn / (tn + fp) if (tn + fp) > 0 else 0
    
    results = {
        'method_name': method_name,
        'f_measure': f_measure,
        'auc': auc_score,
        'g_means': g_means,
        'precision': precision,
        'recall': recall,
        'specificity': specificity,
        'tp': tp, 'tn': tn, 'fp': fp, 'fn': fn
    }
    
    print(f"\n{method_name} 评估结果:")
    print(f"  F-measure: {f_measure:.4f}")
    print(f"  AUC: {auc_score:.4f}")
    print(f"  G-means: {g_means:.4f}")
    print(f"  Precision: {precision:.4f}")
    print(f"  Recall: {recall:.4f}")
    print(f"  Specificity: {specificity:.4f}")
    print(f"  混淆矩阵: TP={tp}, TN={tn}, FP={fp}, FN={fn}")
    
    return results


def create_comparison_visualization(results_list, save_path):
    """创建对比可视化"""
    methods = [r['method_name'] for r in results_list]
    f_scores = [r['f_measure'] for r in results_list]
    auc_scores = [r['auc'] for r in results_list]
    g_scores = [r['g_means'] for r in results_list]
    
    # 创建子图
    fig, axes = plt.subplots(1, 3, figsize=(15, 5))
    
    # F-measure对比
    ax1 = axes[0]
    bars1 = ax1.bar(range(len(methods)), f_scores, alpha=0.8, color='skyblue')
    ax1.set_title('F-measure 对比', fontsize=12, fontweight='bold')
    ax1.set_ylabel('F-measure')
    ax1.set_xticks(range(len(methods)))
    ax1.set_xticklabels(methods, rotation=45, ha='right')
    ax1.grid(True, alpha=0.3)
    
    # 添加数值标签
    for i, bar in enumerate(bars1):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 0.005,
                f'{height:.3f}', ha='center', va='bottom', fontsize=9)
    
    # AUC对比
    ax2 = axes[1]
    bars2 = ax2.bar(range(len(methods)), auc_scores, alpha=0.8, color='lightgreen')
    ax2.set_title('AUC 对比', fontsize=12, fontweight='bold')
    ax2.set_ylabel('AUC')
    ax2.set_xticks(range(len(methods)))
    ax2.set_xticklabels(methods, rotation=45, ha='right')
    ax2.grid(True, alpha=0.3)
    
    for i, bar in enumerate(bars2):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 0.005,
                f'{height:.3f}', ha='center', va='bottom', fontsize=9)
    
    # G-means对比
    ax3 = axes[2]
    bars3 = ax3.bar(range(len(methods)), g_scores, alpha=0.8, color='orange')
    ax3.set_title('G-means 对比', fontsize=12, fontweight='bold')
    ax3.set_ylabel('G-means')
    ax3.set_xticks(range(len(methods)))
    ax3.set_xticklabels(methods, rotation=45, ha='right')
    ax3.grid(True, alpha=0.3)
    
    for i, bar in enumerate(bars3):
        height = bar.get_height()
        ax3.text(bar.get_x() + bar.get_width()/2., height + 0.005,
                f'{height:.3f}', ha='center', va='bottom', fontsize=9)
    
    plt.suptitle('Car数据集: SMOTE vs ADASYN 性能对比\n(vgood作为少数类)', 
                fontsize=14, fontweight='bold')
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"对比可视化保存至: {save_path}")


def main():
    """主函数"""
    print("=== Car数据集处理测试 ===")
    print("vgood作为少数类(1)，其他合并为多数类(0)")
    print("使用方法: SMOTE (k=5), ADASYN (默认参数)")
    print("评估指标: F-measure, AUC, G-means\n")
    
    # 创建输出目录
    output_dir = "car_test_results"
    os.makedirs(output_dir, exist_ok=True)
    
    # 1. 加载数据
    X, y, n_features, label_info = load_car_data('data/car.data')
    
    # 2. 数据分割
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.3, random_state=42, stratify=y
    )
    
    print(f"\n数据分割:")
    print(f"  训练集: {len(X_train)} 样本")
    print(f"  测试集: {len(X_test)} 样本")
    print(f"  训练集少数类比例: {np.sum(y_train==1)/len(y_train)*100:.1f}%")
    print(f"  测试集少数类比例: {np.sum(y_test==1)/len(y_test)*100:.1f}%")
    
    # 存储所有结果
    all_results = []
    
    # 3. 基线方法(无过采样)
    logger.info("评估基线方法(无过采样)...")
    baseline_results = evaluate_method(X_train, y_train, X_test, y_test, "基线方法(无过采样)")
    all_results.append(baseline_results)
    
    # 4. SMOTE方法 (k=5个邻居)
    logger.info("评估SMOTE方法 (k=5个邻居)...")
    try:
        smote = SMOTE(k_neighbors=5, random_state=42)
        X_smote, y_smote = smote.fit_resample(X_train, y_train)
        
        print(f"\nSMOTE重采样后:")
        print(f"  样本数: {len(X_smote)} (原始: {len(X_train)})")
        print(f"  类别分布: {np.bincount(y_smote)}")
        
        smote_results = evaluate_method(X_smote, y_smote, X_test, y_test, "SMOTE (k=5)")
        all_results.append(smote_results)
        
    except Exception as e:
        logger.error(f"SMOTE处理失败: {e}")
    
    # 5. ADASYN方法 (默认参数)
    logger.info("评估ADASYN方法 (默认参数)...")
    try:
        adasyn = ADASYN(random_state=42)
        X_adasyn, y_adasyn = adasyn.fit_resample(X_train, y_train)
        
        print(f"\nADASYN重采样后:")
        print(f"  样本数: {len(X_adasyn)} (原始: {len(X_train)})")
        print(f"  类别分布: {np.bincount(y_adasyn)}")
        
        adasyn_results = evaluate_method(X_adasyn, y_adasyn, X_test, y_test, "ADASYN (默认)")
        all_results.append(adasyn_results)
        
    except Exception as e:
        logger.error(f"ADASYN处理失败: {e}")
    
    # 6. 结果对比
    print("\n" + "="*60)
    print("方法对比结果")
    print("="*60)
    
    print(f"{'方法':<20} {'F-measure':<12} {'AUC':<12} {'G-means':<12}")
    print("-" * 60)
    
    for result in all_results:
        print(f"{result['method_name']:<20} {result['f_measure']:<12.4f} "
              f"{result['auc']:<12.4f} {result['g_means']:<12.4f}")
    
    # 找出最佳方法
    best_f_measure = max(all_results, key=lambda x: x['f_measure'])
    best_auc = max(all_results, key=lambda x: x['auc'])
    best_g_means = max(all_results, key=lambda x: x['g_means'])
    
    print(f"\n最佳性能:")
    print(f"  F-measure: {best_f_measure['method_name']} ({best_f_measure['f_measure']:.4f})")
    print(f"  AUC: {best_auc['method_name']} ({best_auc['auc']:.4f})")
    print(f"  G-means: {best_g_means['method_name']} ({best_g_means['g_means']:.4f})")
    
    # 7. 生成可视化
    viz_path = os.path.join(output_dir, 'method_comparison.png')
    create_comparison_visualization(all_results, viz_path)
    
    # 8. 保存结果
    import json
    results_summary = {
        'dataset_info': label_info,
        'experiment_config': {
            'test_size': 0.3,
            'random_state': 42,
            'classifier': 'RandomForest',
            'smote_k_neighbors': 5,
            'adasyn_params': 'default'
        },
        'results': all_results,
        'best_performance': {
            'f_measure': {'method': best_f_measure['method_name'], 'score': best_f_measure['f_measure']},
            'auc': {'method': best_auc['method_name'], 'score': best_auc['auc']},
            'g_means': {'method': best_g_means['method_name'], 'score': best_g_means['g_means']}
        }
    }
    
    results_path = os.path.join(output_dir, 'test_results.json')
    with open(results_path, 'w', encoding='utf-8') as f:
        json.dump(results_summary, f, indent=2, ensure_ascii=False, default=str)
    
    print(f"\n🎉 实验完成!")
    print(f"📊 结果保存至: {output_dir}")
    print(f"📈 对比图: method_comparison.png")
    print(f"📋 详细结果: test_results.json")


if __name__ == '__main__':
    main()
