"""
CPU版本的快速演示测试
"""

import os
import sys
import numpy as np
import warnings
warnings.filterwarnings('ignore')

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 强制使用CPU
import config
config.wgan_config.FORCE_CPU = True

from data_utils import create_synthetic_imbalanced_dataset, DataProcessor
from genetic_algorithm import GeneticAlgorithm, decode_best_individual
from fitness_function import fitness_function
from config import ga_config, param_ranges

def main():
    print("CPU版本快速演示测试")
    print("=" * 40)
    
    # 创建小规模测试数据
    print("1. 创建测试数据...")
    X, y = create_synthetic_imbalanced_dataset(
        n_samples=300,  # 更小的数据集
        n_features=6,
        imbalance_ratio=0.2,
        random_state=42
    )
    
    processor = DataProcessor()
    X_processed, y_processed = processor.preprocess_data(X, y, fit_transform=True)
    X_train_sub, X_val, X_test, y_train_sub, y_val, y_test = processor.split_data(X_processed, y_processed)
    
    print(f"数据准备完成: 训练{len(X_train_sub)}, 验证{len(X_val)}, 测试{len(X_test)}")
    
    # 运行小规模优化
    print("\n2. 运行遗传算法优化...")
    ga = GeneticAlgorithm(
        fitness_func=fitness_function,
        param_bounds=param_ranges.get_param_bounds(),
        param_types=param_ranges.get_param_types(),
        population_size=8,   # 很小的种群
        max_generations=5,   # 很少的代数
        elite_size=2
    )
    
    best_individual, optimization_result = ga.optimize(
        X_train_sub, y_train_sub, X_val, y_val
    )
    
    print(f"\n3. 优化完成!")
    print(f"最佳适应度: {best_individual.fitness:.4f}")
    
    # 解码参数
    best_params = decode_best_individual(best_individual)
    print(f"\n最优参数:")
    for key, value in best_params.items():
        if key != 'fitness':
            print(f"  {key}: {value}")
    
    print("\n✅ CPU版本测试成功!")

if __name__ == "__main__":
    main()
