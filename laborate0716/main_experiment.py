"""
主实验脚本：执行遗传算法优化ADASYN和WGAN-GP参数的完整实验
"""

import os
import sys
import logging
import time
import json
import pickle
from datetime import datetime
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import classification_report, confusion_matrix
import warnings
warnings.filterwarnings('ignore')

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import (ga_config, param_ranges, exp_config, 
                   dataset_config, logging_config)
from data_utils import load_and_preprocess_data, create_synthetic_imbalanced_dataset
from genetic_algorithm import GeneticAlgorithm, decode_best_individual
from fitness_function import fitness_function
from evaluation import comprehensive_evaluation, plot_results
from utils import setup_logging, save_results, create_results_directory

def setup_experiment_environment():
    """设置实验环境"""
    # 创建结果目录
    results_dir = create_results_directory()
    
    # 设置日志
    setup_logging(results_dir)
    
    logger = logging.getLogger(__name__)
    logger.info("="*60)
    logger.info("遗传算法优化ADASYN+WGAN-GP参数实验开始")
    logger.info("="*60)
    
    return results_dir, logger

def run_single_experiment(dataset_name: str, results_dir: str) -> dict:
    """
    运行单个数据集的实验
    
    Args:
        dataset_name: 数据集名称
        results_dir: 结果保存目录
        
    Returns:
        实验结果字典
    """
    logger = logging.getLogger(__name__)
    logger.info(f"\n开始实验: {dataset_name}")
    logger.info("-" * 40)
    
    start_time = time.time()
    
    try:
        # 1. 加载和预处理数据
        logger.info("1. 数据加载和预处理")
        (X_train_sub, X_val, X_test, 
         y_train_sub, y_val, y_test), processor = load_and_preprocess_data(dataset_name)
        
        # 2. 初始化遗传算法
        logger.info("2. 初始化遗传算法")
        ga = GeneticAlgorithm(
            fitness_func=fitness_function,
            param_bounds=param_ranges.get_param_bounds(),
            param_types=param_ranges.get_param_types(),
            population_size=ga_config.POPULATION_SIZE,
            max_generations=ga_config.MAX_GENERATIONS,
            elite_size=ga_config.ELITE_SIZE
        )
        
        # 3. 执行优化
        logger.info("3. 执行遗传算法优化")
        best_individual, optimization_result = ga.optimize(
            X_train_sub, y_train_sub, X_val, y_val
        )
        
        # 4. 解码最优参数
        best_params = decode_best_individual(best_individual)
        logger.info(f"4. 最优参数: {best_params}")
        
        # 5. 在测试集上进行最终评估
        logger.info("5. 测试集最终评估")
        test_results = comprehensive_evaluation(
            best_individual.genes, 
            X_train_sub, y_train_sub, 
            X_test, y_test,
            processor
        )
        
        # 6. 保存结果
        experiment_result = {
            'dataset': dataset_name,
            'best_params': best_params,
            'optimization_result': {
                'best_fitness': best_individual.fitness,
                'generations_run': optimization_result['generations_run'],
                'best_fitness_history': optimization_result['best_fitness_history'],
                'avg_fitness_history': optimization_result['avg_fitness_history']
            },
            'test_results': test_results,
            'execution_time': time.time() - start_time,
            'timestamp': datetime.now().isoformat()
        }
        
        # 保存详细结果
        dataset_results_dir = os.path.join(results_dir, dataset_name)
        os.makedirs(dataset_results_dir, exist_ok=True)
        
        save_results(experiment_result, dataset_results_dir)
        
        # 生成可视化图表
        if exp_config.SAVE_PLOTS:
            plot_results(experiment_result, dataset_results_dir)
        
        logger.info(f"实验完成: {dataset_name}, 用时: {time.time() - start_time:.2f}秒")
        logger.info(f"最佳适应度: {best_individual.fitness:.4f}")
        logger.info(f"测试集F1分数: {test_results.get('f1_weighted', 'N/A'):.4f}")
        
        return experiment_result
        
    except Exception as e:
        logger.error(f"实验失败: {dataset_name}, 错误: {str(e)}")
        return {
            'dataset': dataset_name,
            'error': str(e),
            'execution_time': time.time() - start_time,
            'timestamp': datetime.now().isoformat()
        }

def run_all_experiments(results_dir: str) -> dict:
    """运行所有数据集的实验"""
    logger = logging.getLogger(__name__)
    
    all_results = {}
    summary_stats = {
        'total_datasets': 0,
        'successful_experiments': 0,
        'failed_experiments': 0,
        'total_time': 0,
        'best_performances': {}
    }
    
    total_start_time = time.time()
    
    # 遍历所有数据集
    for dataset_name in dataset_config.DATASETS.keys():
        logger.info(f"\n{'='*20} {dataset_name.upper()} {'='*20}")
        
        try:
            result = run_single_experiment(dataset_name, results_dir)
            all_results[dataset_name] = result
            
            if 'error' not in result:
                summary_stats['successful_experiments'] += 1
                summary_stats['best_performances'][dataset_name] = {
                    'fitness': result['best_params']['fitness'],
                    'test_f1': result['test_results'].get('f1_weighted', 0)
                }
            else:
                summary_stats['failed_experiments'] += 1
                
        except Exception as e:
            logger.error(f"数据集 {dataset_name} 实验异常: {str(e)}")
            all_results[dataset_name] = {
                'dataset': dataset_name,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
            summary_stats['failed_experiments'] += 1
        
        summary_stats['total_datasets'] += 1
    
    summary_stats['total_time'] = time.time() - total_start_time
    
    # 保存汇总结果
    final_results = {
        'summary': summary_stats,
        'detailed_results': all_results,
        'experiment_config': {
            'ga_config': vars(ga_config),
            'exp_config': vars(exp_config),
            'param_ranges': vars(param_ranges)
        }
    }
    
    # 保存到文件
    with open(os.path.join(results_dir, 'all_experiments_results.json'), 'w') as f:
        json.dump(final_results, f, indent=2, default=str)
    
    with open(os.path.join(results_dir, 'all_experiments_results.pkl'), 'wb') as f:
        pickle.dump(final_results, f)
    
    return final_results

def generate_summary_report(results: dict, results_dir: str):
    """生成实验总结报告"""
    logger = logging.getLogger(__name__)
    
    logger.info("\n" + "="*60)
    logger.info("实验总结报告")
    logger.info("="*60)
    
    summary = results['summary']
    
    logger.info(f"总数据集数量: {summary['total_datasets']}")
    logger.info(f"成功实验数量: {summary['successful_experiments']}")
    logger.info(f"失败实验数量: {summary['failed_experiments']}")
    logger.info(f"总执行时间: {summary['total_time']:.2f} 秒")
    
    if summary['best_performances']:
        logger.info("\n各数据集最佳性能:")
        for dataset, perf in summary['best_performances'].items():
            logger.info(f"  {dataset}: 适应度={perf['fitness']:.4f}, "
                       f"测试F1={perf['test_f1']:.4f}")
        
        # 计算平均性能
        avg_fitness = np.mean([p['fitness'] for p in summary['best_performances'].values()])
        avg_test_f1 = np.mean([p['test_f1'] for p in summary['best_performances'].values()])
        
        logger.info(f"\n平均性能:")
        logger.info(f"  平均适应度: {avg_fitness:.4f}")
        logger.info(f"  平均测试F1: {avg_test_f1:.4f}")
    
    # 生成汇总图表
    if exp_config.SAVE_PLOTS and summary['best_performances']:
        create_summary_plots(results, results_dir)
    
    logger.info("\n实验完成！结果已保存到: " + results_dir)

def create_summary_plots(results: dict, results_dir: str):
    """创建汇总可视化图表"""
    try:
        plt.style.use('seaborn-v0_8')
    except OSError:
        try:
            plt.style.use('seaborn')
        except OSError:
            # 如果seaborn样式不可用，使用默认样式
            pass
    
    # 提取数据
    datasets = list(results['summary']['best_performances'].keys())
    fitness_scores = [results['summary']['best_performances'][d]['fitness'] for d in datasets]
    test_f1_scores = [results['summary']['best_performances'][d]['test_f1'] for d in datasets]
    
    # 创建子图
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('遗传算法优化ADASYN+WGAN-GP实验结果汇总', fontsize=16)
    
    # 1. 适应度分数对比
    axes[0, 0].bar(datasets, fitness_scores, color='skyblue', alpha=0.7)
    axes[0, 0].set_title('各数据集最佳适应度分数')
    axes[0, 0].set_ylabel('适应度')
    axes[0, 0].tick_params(axis='x', rotation=45)
    
    # 2. 测试F1分数对比
    axes[0, 1].bar(datasets, test_f1_scores, color='lightgreen', alpha=0.7)
    axes[0, 1].set_title('各数据集测试F1分数')
    axes[0, 1].set_ylabel('F1分数')
    axes[0, 1].tick_params(axis='x', rotation=45)
    
    # 3. 适应度vs测试F1散点图
    axes[1, 0].scatter(fitness_scores, test_f1_scores, s=100, alpha=0.7)
    for i, dataset in enumerate(datasets):
        axes[1, 0].annotate(dataset, (fitness_scores[i], test_f1_scores[i]), 
                           xytext=(5, 5), textcoords='offset points')
    axes[1, 0].set_xlabel('适应度分数')
    axes[1, 0].set_ylabel('测试F1分数')
    axes[1, 0].set_title('适应度 vs 测试F1分数')
    
    # 4. 性能分布箱线图
    performance_data = [fitness_scores, test_f1_scores]
    axes[1, 1].boxplot(performance_data, labels=['适应度', '测试F1'])
    axes[1, 1].set_title('性能指标分布')
    axes[1, 1].set_ylabel('分数')
    
    plt.tight_layout()
    plt.savefig(os.path.join(results_dir, 'experiment_summary.png'), dpi=300, bbox_inches='tight')
    plt.close()

def main():
    """主函数"""
    # 设置实验环境
    results_dir, logger = setup_experiment_environment()
    
    try:
        # 运行所有实验
        all_results = run_all_experiments(results_dir)
        
        # 生成总结报告
        generate_summary_report(all_results, results_dir)
        
        logger.info("所有实验成功完成！")
        
    except Exception as e:
        logger.error(f"实验执行失败: {str(e)}")
        raise
    
    finally:
        logger.info("实验结束")

if __name__ == "__main__":
    main()
