"""
快速演示脚本：展示遗传算法优化ADASYN+WGAN-GP的核心功能
使用合成数据集进行快速演示，适合初次使用和功能验证
"""

import os
import sys
import time
import numpy as np
import matplotlib.pyplot as plt
from sklearn.metrics import classification_report, f1_score
import warnings
warnings.filterwarnings('ignore')

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import ga_config, param_ranges
from data_utils import create_synthetic_imbalanced_dataset, DataProcessor
from genetic_algorithm import GeneticAlgorithm, decode_best_individual
from fitness_function import fitness_function
from utils import setup_logging, create_results_directory

def create_demo_data():
    """创建演示用的不平衡数据集"""
    print("创建演示数据集...")
    
    # 创建不平衡数据集
    X, y = create_synthetic_imbalanced_dataset(
        n_samples=800,
        n_features=8,
        imbalance_ratio=0.15,  # 15%的少数类
        random_state=42
    )
    
    # 数据预处理
    processor = DataProcessor()
    X_processed, y_processed = processor.preprocess_data(X, y, fit_transform=True)
    
    # 数据划分
    X_train_sub, X_val, X_test, y_train_sub, y_val, y_test = processor.split_data(X_processed, y_processed)
    
    print(f"数据集创建完成:")
    print(f"  训练集: {X_train_sub.shape[0]} 样本 (少数类: {np.sum(y_train_sub == 1)})")
    print(f"  验证集: {X_val.shape[0]} 样本 (少数类: {np.sum(y_val == 1)})")
    print(f"  测试集: {X_test.shape[0]} 样本 (少数类: {np.sum(y_test == 1)})")
    
    return (X_train_sub, X_val, X_test, y_train_sub, y_val, y_test), processor

def run_quick_optimization(X_train_sub, y_train_sub, X_val, y_val):
    """运行快速优化演示"""
    print("\n开始遗传算法优化...")
    
    # 使用较小的参数进行快速演示
    ga = GeneticAlgorithm(
        fitness_func=fitness_function,
        param_bounds=param_ranges.get_param_bounds(),
        param_types=param_ranges.get_param_types(),
        population_size=20,  # 较小的种群
        max_generations=15,  # 较少的代数
        elite_size=3
    )
    
    start_time = time.time()
    
    # 执行优化
    best_individual, optimization_result = ga.optimize(
        X_train_sub, y_train_sub, X_val, y_val
    )
    
    optimization_time = time.time() - start_time
    
    print(f"优化完成，用时: {optimization_time:.1f} 秒")
    print(f"最佳适应度: {best_individual.fitness:.4f}")
    
    # 解码最优参数
    best_params = decode_best_individual(best_individual)
    print(f"\n最优参数:")
    for key, value in best_params.items():
        if key != 'fitness':
            print(f"  {key}: {value}")
    
    return best_individual, optimization_result, best_params

def evaluate_on_test_set(best_individual, X_train_sub, y_train_sub, X_test, y_test):
    """在测试集上评估最优参数"""
    print("\n在测试集上评估最优参数...")
    
    try:
        from fitness_function import decode_individual
        from imblearn.over_sampling import ADASYN
        from wgan_gp import WGAN_GP
        from sklearn.ensemble import RandomForestClassifier
        from config import wgan_config
        
        # 解码参数
        k, alpha, lambda_gp, n_critic, lr, batch_size = decode_individual(best_individual.genes)
        
        # 生成合成数据
        X_min = X_train_sub[y_train_sub == 1]
        X_maj = X_train_sub[y_train_sub == 0]
        N_min = len(X_min)
        N_maj = len(X_maj)
        G_total = N_maj - N_min
        
        print(f"原始数据: 多数类 {N_maj}, 少数类 {N_min}")
        
        # ADASYN生成
        G_adasyn = int(alpha * G_total)
        X_synthetic = np.zeros((0, X_train_sub.shape[1]))
        
        if G_adasyn > 0:
            try:
                adasyn = ADASYN(
                    sampling_strategy={1: N_min + G_adasyn},
                    n_neighbors=min(k, N_min-1) if N_min > 1 else 1,
                    random_state=42
                )
                X_adasyn_res, y_adasyn_res = adasyn.fit_resample(X_train_sub, y_train_sub)
                X_adasyn_only = X_adasyn_res[len(X_train_sub):]
                X_synthetic = np.vstack([X_synthetic, X_adasyn_only]) if len(X_synthetic) > 0 else X_adasyn_only
                print(f"ADASYN生成: {len(X_adasyn_only)} 个样本")
            except Exception as e:
                print(f"ADASYN生成失败: {e}")
        
        # WGAN-GP生成
        G_wgan = G_total - G_adasyn
        if G_wgan > 0:
            try:
                real_minority = np.vstack([X_min, X_synthetic]) if len(X_synthetic) > 0 else X_min
                
                if len(real_minority) < batch_size:
                    batch_size = max(1, len(real_minority) // 2)
                
                wgan_gp = WGAN_GP(
                    latent_dim=wgan_config.LATENT_DIM,
                    data_dim=X_train_sub.shape[1],
                    lambda_gp=lambda_gp,
                    lr=lr,
                    batch_size=batch_size,
                    n_critic=n_critic
                )
                
                print(f"训练WGAN-GP...")
                wgan_gp.train(real_minority, epochs=50)  # 减少训练轮数用于演示
                
                X_wgan = wgan_gp.generate_samples(G_wgan)
                X_synthetic = np.vstack([X_synthetic, X_wgan]) if len(X_synthetic) > 0 else X_wgan
                print(f"WGAN-GP生成: {len(X_wgan)} 个样本")
                
            except Exception as e:
                print(f"WGAN-GP生成失败: {e}")
        
        # 构建增强训练集
        if len(X_synthetic) > 0:
            X_train_augmented = np.vstack([X_train_sub, X_synthetic])
            y_train_augmented = np.concatenate([y_train_sub, np.ones(len(X_synthetic))])
        else:
            X_train_augmented = X_train_sub
            y_train_augmented = y_train_sub
        
        print(f"增强后训练集: {len(X_train_augmented)} 样本")
        print(f"类别分布: 多数类 {np.sum(y_train_augmented == 0)}, 少数类 {np.sum(y_train_augmented == 1)}")
        
        # 训练最终分类器
        classifier = RandomForestClassifier(n_estimators=100, random_state=42)
        classifier.fit(X_train_augmented, y_train_augmented)
        
        # 在测试集上预测
        y_pred = classifier.predict(X_test)
        
        # 计算性能指标
        f1_weighted = f1_score(y_test, y_pred, average='weighted')
        f1_minority = f1_score(y_test, y_pred, pos_label=1)
        
        print(f"\n测试集性能:")
        print(f"  加权F1分数: {f1_weighted:.4f}")
        print(f"  少数类F1分数: {f1_minority:.4f}")
        
        # 详细分类报告
        print(f"\n详细分类报告:")
        print(classification_report(y_test, y_pred, target_names=['多数类', '少数类']))
        
        return {
            'f1_weighted': f1_weighted,
            'f1_minority': f1_minority,
            'synthetic_samples': len(X_synthetic),
            'adasyn_samples': G_adasyn,
            'wgan_samples': G_wgan
        }
        
    except Exception as e:
        print(f"测试集评估失败: {e}")
        return None

def plot_optimization_history(optimization_result, save_path=None):
    """绘制优化历史"""
    try:
        plt.figure(figsize=(12, 5))
        
        # 最佳适应度历史
        plt.subplot(1, 2, 1)
        plt.plot(optimization_result['best_fitness_history'], 'b-', linewidth=2, label='最佳适应度')
        plt.plot(optimization_result['avg_fitness_history'], 'r--', linewidth=2, label='平均适应度')
        plt.xlabel('代数')
        plt.ylabel('适应度')
        plt.title('优化历史')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 适应度改进
        plt.subplot(1, 2, 2)
        best_fitness = optimization_result['best_fitness_history']
        improvements = [best_fitness[i] - best_fitness[i-1] if i > 0 else 0 
                       for i in range(len(best_fitness))]
        plt.plot(improvements, 'g-', linewidth=2)
        plt.xlabel('代数')
        plt.ylabel('适应度改进')
        plt.title('收敛分析')
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"优化历史图已保存: {save_path}")
        else:
            plt.show()
        
        plt.close()
        
    except Exception as e:
        print(f"绘图失败: {e}")

def main():
    """主演示函数"""
    print("=" * 60)
    print("遗传算法优化ADASYN+WGAN-GP快速演示")
    print("=" * 60)
    
    # 设置简单日志
    import logging
    logging.basicConfig(level=logging.WARNING)
    
    try:
        # 1. 创建演示数据
        data_splits, processor = create_demo_data()
        X_train_sub, X_val, X_test, y_train_sub, y_val, y_test = data_splits
        
        # 2. 运行优化
        best_individual, optimization_result, best_params = run_quick_optimization(
            X_train_sub, y_train_sub, X_val, y_val
        )
        
        # 3. 测试集评估
        test_results = evaluate_on_test_set(
            best_individual, X_train_sub, y_train_sub, X_test, y_test
        )
        
        # 4. 结果可视化
        print("\n生成优化历史图...")
        plot_optimization_history(optimization_result, 'demo_optimization_history.png')
        
        # 5. 总结
        print("\n" + "=" * 60)
        print("演示总结")
        print("=" * 60)
        print(f"优化代数: {optimization_result['generations_run']}")
        print(f"最佳验证适应度: {best_individual.fitness:.4f}")
        
        if test_results:
            print(f"测试集加权F1: {test_results['f1_weighted']:.4f}")
            print(f"测试集少数类F1: {test_results['f1_minority']:.4f}")
            print(f"合成样本总数: {test_results['synthetic_samples']}")
        
        print(f"\n最优参数组合:")
        print(f"  ADASYN k邻居: {best_params['adasyn_k']}")
        print(f"  ADASYN α平衡: {best_params['adasyn_alpha']:.3f}")
        print(f"  WGAN λ梯度惩罚: {best_params['wgan_lambda']:.2f}")
        print(f"  WGAN 判别器训练次数: {best_params['wgan_n_critic']}")
        print(f"  WGAN 学习率: {best_params['wgan_lr']:.2e}")
        print(f"  WGAN 批量大小: {best_params['wgan_batch_size']}")
        
        print("\n🎉 快速演示完成！")
        print("如需运行完整实验，请执行: python main_experiment.py")
        
    except Exception as e:
        print(f"演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
