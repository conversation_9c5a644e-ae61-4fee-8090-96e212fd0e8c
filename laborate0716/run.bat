@echo off
chcp 65001 >nul
title 遗传算法优化ADASYN+WGAN-GP参数系统

echo ============================================================
echo 遗传算法优化ADASYN+WGAN-GP参数系统
echo ============================================================
echo.

if "%1"=="" goto menu

python run.py %1
goto end

:menu
echo 请选择要执行的操作:
echo.
echo 1. 兼容性检查 (推荐首次使用)
echo 2. 运行系统测试
echo 3. 运行快速演示
echo 4. 运行完整实验
echo 5. 显示帮助信息
echo 6. 退出
echo.

set /p choice=请输入选项 (1-6): 

if "%choice%"=="1" (
    echo.
    echo 运行兼容性检查...
    python run.py check
    goto pause_and_menu
)

if "%choice%"=="2" (
    echo.
    echo 运行系统测试...
    python run.py test
    goto pause_and_menu
)

if "%choice%"=="3" (
    echo.
    echo 运行快速演示...
    python run.py demo
    goto pause_and_menu
)

if "%choice%"=="4" (
    echo.
    echo 运行完整实验...
    python run.py experiment
    goto pause_and_menu
)

if "%choice%"=="5" (
    echo.
    python run.py help
    goto pause_and_menu
)

if "%choice%"=="6" (
    goto end
)

echo 无效选项，请重新选择
goto menu

:pause_and_menu
echo.
pause
goto menu

:end
echo.
echo 程序结束
pause
