"""
DDAG-WGAN测试脚本
快速验证DDAG-WGAN实现的正确性
"""

import os
import sys
import numpy as np
import pandas as pd
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import f1_score, roc_auc_score, classification_report
import warnings
warnings.filterwarnings('ignore')

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ddag_wgan import (
    load_yeast_data, 
    generate_balanced_data_ddag_wgan, 
    plot_training_losses,
    geometric_mean_score,
    evaluate_with_cross_validation
)

def test_data_loading():
    """测试数据加载功能"""
    print("🔍 测试数据加载功能...")
    
    X, y, minority_count, majority_count, imbalance_ratio = load_yeast_data()
    
    if X is not None:
        print(f"✅ 数据加载成功")
        print(f"   数据形状: {X.shape}")
        print(f"   类别分布: 少数类{minority_count}, 多数类{majority_count}")
        print(f"   不平衡比例: {imbalance_ratio:.2f}:1")
        return True, X, y
    else:
        print("❌ 数据加载失败")
        return False, None, None

def test_ddag_wgan_generation():
    """测试DDAG-WGAN数据生成功能"""
    print("\n🔍 测试DDAG-WGAN数据生成功能...")
    
    # 加载数据
    X, y, _, _, _ = load_yeast_data()
    if X is None:
        print("❌ 数据加载失败，跳过测试")
        return False
    
    # 划分数据
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.3, stratify=y, random_state=42
    )
    
    print(f"训练集形状: {X_train.shape}")
    print(f"训练集类别分布: {np.bincount(y_train)}")
    
    try:
        # 使用默认参数测试DDAG-WGAN
        X_balanced, y_balanced, losses = generate_balanced_data_ddag_wgan(
            X_train, y_train,
            k=5,           # ADASYN邻居数
            alpha=0.6,     # ADASYN生成比例
            lambda_gp=10,  # 梯度惩罚系数
            n_critic=3,    # 判别器训练次数（减少以加快测试）
            lr=1e-4,       # 学习率
            batch_size=32  # 批量大小
        )
        
        print(f"✅ DDAG-WGAN数据生成成功")
        print(f"   平衡后数据形状: {X_balanced.shape}")
        print(f"   平衡后类别分布: {np.bincount(y_balanced)}")
        
        # 测试损失函数可视化
        if losses['d_losses'] and losses['g_losses']:
            plot_training_losses(losses, 'test_ddag_wgan_losses.png')
            print(f"   损失函数图已保存")
        
        return True, X_balanced, y_balanced, X_test, y_test
        
    except Exception as e:
        print(f"❌ DDAG-WGAN数据生成失败: {e}")
        return False, None, None, None, None

def test_classification_performance():
    """测试分类性能"""
    print("\n🔍 测试分类性能...")
    
    # 生成平衡数据
    success, X_balanced, y_balanced, X_test, y_test = test_ddag_wgan_generation()
    
    if not success:
        print("❌ 无法生成平衡数据，跳过分类测试")
        return False
    
    try:
        # 训练随机森林分类器
        rf = RandomForestClassifier(n_estimators=50, random_state=42)
        rf.fit(X_balanced, y_balanced)
        
        # 在测试集上预测
        y_pred = rf.predict(X_test)
        y_pred_proba = rf.predict_proba(X_test)[:, 1]
        
        # 计算评估指标
        f1 = f1_score(y_test, y_pred)
        auc = roc_auc_score(y_test, y_pred_proba)
        gmean = geometric_mean_score(y_test, y_pred)
        
        print(f"✅ 分类性能测试完成")
        print(f"   F1-Score: {f1:.4f}")
        print(f"   AUC-ROC:  {auc:.4f}")
        print(f"   G-mean:   {gmean:.4f}")
        
        # 显示详细分类报告
        print("\n分类报告:")
        print(classification_report(y_test, y_pred, target_names=['多数类', '少数类']))
        
        return True
        
    except Exception as e:
        print(f"❌ 分类性能测试失败: {e}")
        return False

def test_cross_validation():
    """测试交叉验证功能（简化版）"""
    print("\n🔍 测试交叉验证功能（3折快速测试）...")
    
    # 加载数据
    X, y, _, _, _ = load_yeast_data()
    if X is None:
        print("❌ 数据加载失败，跳过测试")
        return False
    
    try:
        # 使用3折交叉验证进行快速测试
        results = evaluate_with_cross_validation(X, y, "DDAG-WGAN测试", n_folds=3)
        
        print(f"✅ 交叉验证测试完成")
        print(f"   平均F1-Score: {np.mean(results['f1_scores']):.4f}")
        print(f"   平均AUC-ROC:  {np.mean(results['auc_scores']):.4f}")
        print(f"   平均G-mean:   {np.mean(results['gmean_scores']):.4f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 交叉验证测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 80)
    print("DDAG-WGAN实现测试脚本")
    print("=" * 80)
    
    tests = [
        ("数据加载", test_data_loading),
        ("DDAG-WGAN数据生成", test_ddag_wgan_generation),
        ("分类性能", test_classification_performance),
        ("交叉验证", test_cross_validation)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"运行测试: {test_name}")
        print(f"{'='*60}")
        
        try:
            if test_name == "数据加载":
                result, _, _ = test_func()
            elif test_name == "DDAG-WGAN数据生成":
                result, _, _, _, _ = test_func()
            else:
                result = test_func()
            
            results.append((test_name, result))
            
            if result:
                print(f"✅ {test_name}测试通过")
            else:
                print(f"❌ {test_name}测试失败")
                
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 总结测试结果
    print(f"\n{'='*80}")
    print("测试结果总结")
    print(f"{'='*80}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<20}: {status}")
    
    print(f"\n总体结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！DDAG-WGAN实现正常工作")
    else:
        print("⚠️  部分测试失败，请检查实现")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
