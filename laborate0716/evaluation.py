"""
评估模块：综合评估优化结果，包括TSTR测试、分布分析等
"""

import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.manifold import TSNE
from sklearn.decomposition import PCA
from sklearn.metrics import (f1_score, roc_auc_score, precision_score, 
                           recall_score, classification_report, confusion_matrix)
from sklearn.ensemble import RandomForestClassifier
from imblearn.over_sampling import ADASYN
import logging
import os
from scipy.stats import entropy, ks_2samp
try:
    from scipy.spatial.distance import wasserstein_distance
except ImportError:
    # 如果scipy版本较旧，使用自定义实现
    def wasserstein_distance(u_values, v_values):
        """
        简化的Wasserstein距离实现（1维）
        """
        import numpy as np
        u_values = np.asarray(u_values)
        v_values = np.asarray(v_values)

        # 排序
        u_sorted = np.sort(u_values)
        v_sorted = np.sort(v_values)

        # 计算累积分布函数
        n, m = len(u_sorted), len(v_sorted)
        u_cdf = np.arange(1, n+1) / n
        v_cdf = np.arange(1, m+1) / m

        # 合并并排序所有值
        all_values = np.concatenate([u_sorted, v_sorted])
        all_values = np.sort(all_values)

        # 计算每个点的CDF值
        u_cdf_interp = np.searchsorted(u_sorted, all_values, side='right') / n
        v_cdf_interp = np.searchsorted(v_sorted, all_values, side='right') / m

        # 计算Wasserstein距离
        return np.mean(np.abs(u_cdf_interp - v_cdf_interp))

from wgan_gp import WGAN_GP
from fitness_function import decode_individual, geometric_mean_score, calculate_kl_divergence
from config import wgan_config, exp_config

logger = logging.getLogger(__name__)

def comprehensive_evaluation(individual_genes, X_train, y_train, X_test, y_test, processor):
    """
    综合评估：包括TSTR测试、分布分析、参数敏感性分析等
    
    Args:
        individual_genes: 最优个体基因
        X_train, y_train: 训练数据
        X_test, y_test: 测试数据
        processor: 数据处理器
        
    Returns:
        evaluation_results: 评估结果字典
    """
    logger.info("开始综合评估...")
    
    # 解码参数
    k, alpha, lambda_gp, n_critic, lr, batch_size = decode_individual(individual_genes)
    
    evaluation_results = {
        'parameters': {
            'adasyn_k': k,
            'adasyn_alpha': alpha,
            'wgan_lambda': lambda_gp,
            'wgan_n_critic': n_critic,
            'wgan_lr': lr,
            'wgan_batch_size': batch_size
        }
    }
    
    try:
        # 1. 生成合成数据
        logger.info("1. 生成合成数据")
        X_synthetic, generation_info = generate_synthetic_data(
            X_train, y_train, k, alpha, lambda_gp, n_critic, lr, batch_size
        )
        evaluation_results['generation_info'] = generation_info
        
        # 2. TSTR测试
        logger.info("2. TSTR (Train on Synthetic, Test on Real) 测试")
        tstr_results = tstr_evaluation(X_synthetic, y_train, X_test, y_test)
        evaluation_results['tstr_results'] = tstr_results
        
        # 3. 分布质量分析
        logger.info("3. 合成数据质量分析")
        quality_results = analyze_synthetic_quality(X_train, y_train, X_synthetic)
        evaluation_results['quality_analysis'] = quality_results
        
        # 4. 最终模型性能评估
        logger.info("4. 最终模型性能评估")
        final_performance = evaluate_final_model(X_train, y_train, X_test, y_test, 
                                                X_synthetic)
        evaluation_results.update(final_performance)
        
        # 5. 可视化分析
        logger.info("5. 生成可视化分析")
        visualization_info = create_evaluation_visualizations(
            X_train, y_train, X_synthetic, X_test, y_test
        )
        evaluation_results['visualization_info'] = visualization_info
        
        logger.info("综合评估完成")
        
    except Exception as e:
        logger.error(f"综合评估失败: {str(e)}")
        evaluation_results['error'] = str(e)
    
    return evaluation_results

def generate_synthetic_data(X_train, y_train, k, alpha, lambda_gp, n_critic, lr, batch_size):
    """生成合成数据"""
    X_min = X_train[y_train == 1]
    X_maj = X_train[y_train == 0]
    N_min = len(X_min)
    N_maj = len(X_maj)
    G_total = N_maj - N_min
    
    generation_info = {
        'original_minority': N_min,
        'original_majority': N_maj,
        'target_generation': G_total,
        'adasyn_generated': 0,
        'wgan_generated': 0
    }
    
    # ADASYN生成
    G_adasyn = int(alpha * G_total)
    X_adasyn_only = np.zeros((0, X_train.shape[1]))
    
    if G_adasyn > 0:
        try:
            adasyn = ADASYN(
                sampling_strategy={1: N_min + G_adasyn},
                n_neighbors=min(k, N_min-1) if N_min > 1 else 1,
                random_state=42
            )
            X_adasyn_res, y_adasyn_res = adasyn.fit_resample(X_train, y_train)
            X_adasyn_only = X_adasyn_res[len(X_train):]
            generation_info['adasyn_generated'] = len(X_adasyn_only)
        except Exception as e:
            logger.warning(f"ADASYN生成失败: {e}")
    
    # WGAN-GP生成
    G_wgan = G_total - G_adasyn
    X_wgan = np.zeros((0, X_train.shape[1]))
    
    if G_wgan > 0:
        try:
            real_minority = np.vstack([X_min, X_adasyn_only]) if len(X_adasyn_only) > 0 else X_min
            
            if len(real_minority) < batch_size:
                batch_size = max(1, len(real_minority) // 2)
            
            wgan_gp = WGAN_GP(
                latent_dim=wgan_config.LATENT_DIM,
                data_dim=X_train.shape[1],
                lambda_gp=lambda_gp,
                lr=lr,
                batch_size=batch_size,
                n_critic=n_critic
            )
            
            wgan_gp.train(real_minority, epochs=wgan_config.EPOCHS)
            X_wgan = wgan_gp.generate_samples(G_wgan)
            generation_info['wgan_generated'] = len(X_wgan)
            
        except Exception as e:
            logger.warning(f"WGAN-GP生成失败: {e}")
    
    # 合并合成数据
    X_synthetic = np.vstack([X_adasyn_only, X_wgan]) if len(X_adasyn_only) > 0 or len(X_wgan) > 0 else np.zeros((0, X_train.shape[1]))
    
    return X_synthetic, generation_info

def tstr_evaluation(X_synthetic, y_train, X_test, y_test):
    """TSTR (Train on Synthetic, Test on Real) 评估"""
    if len(X_synthetic) == 0:
        return {'error': 'No synthetic data generated'}
    
    try:
        # 构建合成训练集
        y_synthetic = np.ones(len(X_synthetic))  # 所有合成样本都是少数类
        
        # 训练分类器
        classifier = RandomForestClassifier(n_estimators=100, random_state=42)
        classifier.fit(X_synthetic, y_synthetic)
        
        # 在真实测试集上评估
        y_pred = classifier.predict(X_test)
        y_pred_proba = classifier.predict_proba(X_test)[:, 1] if len(np.unique(y_synthetic)) > 1 else np.ones(len(X_test)) * 0.5
        
        # 计算指标（只针对少数类）
        minority_indices = y_test == 1
        if np.sum(minority_indices) > 0:
            minority_precision = precision_score(y_test[minority_indices], y_pred[minority_indices], pos_label=1, zero_division=0)
            minority_recall = recall_score(y_test[minority_indices], y_pred[minority_indices], pos_label=1, zero_division=0)
            minority_f1 = f1_score(y_test[minority_indices], y_pred[minority_indices], pos_label=1, zero_division=0)
        else:
            minority_precision = minority_recall = minority_f1 = 0.0
        
        return {
            'minority_precision': minority_precision,
            'minority_recall': minority_recall,
            'minority_f1': minority_f1,
            'synthetic_samples_used': len(X_synthetic)
        }
        
    except Exception as e:
        return {'error': str(e)}

def analyze_synthetic_quality(X_train, y_train, X_synthetic):
    """分析合成数据质量"""
    if len(X_synthetic) == 0:
        return {'error': 'No synthetic data to analyze'}
    
    X_real_minority = X_train[y_train == 1]
    
    quality_metrics = {}
    
    try:
        # 1. KL散度
        kl_div = calculate_kl_divergence(X_real_minority, X_synthetic)
        quality_metrics['kl_divergence'] = kl_div
        
        # 2. Wasserstein距离
        wd_distances = []
        for i in range(X_real_minority.shape[1]):
            wd = wasserstein_distance(X_real_minority[:, i], X_synthetic[:, i])
            wd_distances.append(wd)
        quality_metrics['wasserstein_distance'] = np.mean(wd_distances)
        quality_metrics['wasserstein_distances_per_feature'] = wd_distances
        
        # 3. Kolmogorov-Smirnov测试
        ks_statistics = []
        ks_pvalues = []
        for i in range(X_real_minority.shape[1]):
            ks_stat, ks_pval = ks_2samp(X_real_minority[:, i], X_synthetic[:, i])
            ks_statistics.append(ks_stat)
            ks_pvalues.append(ks_pval)
        
        quality_metrics['ks_statistics'] = ks_statistics
        quality_metrics['ks_pvalues'] = ks_pvalues
        quality_metrics['ks_mean_statistic'] = np.mean(ks_statistics)
        quality_metrics['ks_mean_pvalue'] = np.mean(ks_pvalues)
        
        # 4. 统计特征比较
        real_mean = np.mean(X_real_minority, axis=0)
        real_std = np.std(X_real_minority, axis=0)
        synthetic_mean = np.mean(X_synthetic, axis=0)
        synthetic_std = np.std(X_synthetic, axis=0)
        
        quality_metrics['mean_difference'] = np.mean(np.abs(real_mean - synthetic_mean))
        quality_metrics['std_difference'] = np.mean(np.abs(real_std - synthetic_std))
        
        # 5. 质量评级
        quality_score = 0
        if kl_div < 0.05:
            quality_score += 1
        if quality_metrics['wasserstein_distance'] < 0.1:
            quality_score += 1
        if quality_metrics['ks_mean_pvalue'] > 0.05:  # 不能拒绝同分布假设
            quality_score += 1
        
        quality_metrics['quality_score'] = quality_score
        quality_metrics['quality_grade'] = ['Poor', 'Fair', 'Good', 'Excellent'][quality_score]
        
    except Exception as e:
        quality_metrics['error'] = str(e)
    
    return quality_metrics

def evaluate_final_model(X_train, y_train, X_test, y_test, X_synthetic):
    """评估最终模型性能"""
    try:
        # 构建增强训练集
        if len(X_synthetic) > 0:
            X_train_augmented = np.vstack([X_train, X_synthetic])
            y_train_augmented = np.concatenate([y_train, np.ones(len(X_synthetic))])
        else:
            X_train_augmented = X_train
            y_train_augmented = y_train
        
        # 训练最终分类器
        classifier = RandomForestClassifier(
            n_estimators=exp_config.RF_N_ESTIMATORS,
            max_depth=exp_config.RF_MAX_DEPTH,
            random_state=42
        )
        classifier.fit(X_train_augmented, y_train_augmented)
        
        # 预测
        y_pred = classifier.predict(X_test)
        y_pred_proba = classifier.predict_proba(X_test)[:, 1]
        
        # 计算各种指标
        results = {
            'f1_weighted': f1_score(y_test, y_pred, average='weighted'),
            'f1_macro': f1_score(y_test, y_pred, average='macro'),
            'f1_minority': f1_score(y_test, y_pred, pos_label=1),
            'precision_minority': precision_score(y_test, y_pred, pos_label=1, zero_division=0),
            'recall_minority': recall_score(y_test, y_pred, pos_label=1, zero_division=0),
            'auc_roc': roc_auc_score(y_test, y_pred_proba),
            'geometric_mean': geometric_mean_score(y_test, y_pred),
            'augmented_training_size': len(X_train_augmented),
            'synthetic_samples_added': len(X_synthetic)
        }
        
        # 混淆矩阵
        cm = confusion_matrix(y_test, y_pred)
        results['confusion_matrix'] = cm.tolist()
        
        # 分类报告
        results['classification_report'] = classification_report(y_test, y_pred, output_dict=True)
        
        return results
        
    except Exception as e:
        return {'error': str(e)}

def create_evaluation_visualizations(X_train, y_train, X_synthetic, X_test, y_test):
    """创建评估可视化"""
    visualization_info = {'plots_created': []}
    
    try:
        # 1. t-SNE可视化
        if len(X_synthetic) > 0:
            create_tsne_visualization(X_train, y_train, X_synthetic)
            visualization_info['plots_created'].append('tsne_plot')
        
        # 2. 特征分布比较
        if len(X_synthetic) > 0:
            create_feature_distribution_plots(X_train, y_train, X_synthetic)
            visualization_info['plots_created'].append('feature_distributions')
        
        # 3. 质量指标可视化
        # 这部分在plot_results函数中实现
        
    except Exception as e:
        visualization_info['error'] = str(e)
    
    return visualization_info

def create_tsne_visualization(X_train, y_train, X_synthetic):
    """创建t-SNE可视化"""
    try:
        # 准备数据
        X_real_minority = X_train[y_train == 1]
        X_real_majority = X_train[y_train == 0]
        
        # 采样以减少计算量
        max_samples = 500
        if len(X_real_minority) > max_samples:
            indices = np.random.choice(len(X_real_minority), max_samples, replace=False)
            X_real_minority = X_real_minority[indices]
        
        if len(X_real_majority) > max_samples:
            indices = np.random.choice(len(X_real_majority), max_samples, replace=False)
            X_real_majority = X_real_majority[indices]
        
        if len(X_synthetic) > max_samples:
            indices = np.random.choice(len(X_synthetic), max_samples, replace=False)
            X_synthetic_sample = X_synthetic[indices]
        else:
            X_synthetic_sample = X_synthetic
        
        # 合并数据
        X_combined = np.vstack([X_real_minority, X_real_majority, X_synthetic_sample])
        labels = (['Real Minority'] * len(X_real_minority) + 
                 ['Real Majority'] * len(X_real_majority) + 
                 ['Synthetic'] * len(X_synthetic_sample))
        
        # t-SNE降维
        tsne = TSNE(n_components=2, random_state=42, perplexity=min(30, len(X_combined)//4))
        X_tsne = tsne.fit_transform(X_combined)
        
        # 绘图
        plt.figure(figsize=(10, 8))
        colors = {'Real Minority': 'red', 'Real Majority': 'blue', 'Synthetic': 'green'}
        
        for label in ['Real Minority', 'Real Majority', 'Synthetic']:
            mask = np.array(labels) == label
            plt.scatter(X_tsne[mask, 0], X_tsne[mask, 1], 
                       c=colors[label], label=label, alpha=0.6, s=50)
        
        plt.title('t-SNE Visualization: Real vs Synthetic Data')
        plt.xlabel('t-SNE Component 1')
        plt.ylabel('t-SNE Component 2')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        return plt.gcf()
        
    except Exception as e:
        logger.error(f"t-SNE可视化失败: {e}")
        return None

def create_feature_distribution_plots(X_train, y_train, X_synthetic, max_features=6):
    """创建特征分布对比图"""
    try:
        X_real_minority = X_train[y_train == 1]
        n_features = min(max_features, X_train.shape[1])
        
        fig, axes = plt.subplots(2, 3, figsize=(15, 10))
        axes = axes.flatten()
        
        for i in range(n_features):
            ax = axes[i]
            
            # 绘制直方图
            ax.hist(X_real_minority[:, i], bins=20, alpha=0.5, label='Real Minority', 
                   color='red', density=True)
            ax.hist(X_synthetic[:, i], bins=20, alpha=0.5, label='Synthetic', 
                   color='green', density=True)
            
            ax.set_title(f'Feature {i+1} Distribution')
            ax.set_xlabel('Value')
            ax.set_ylabel('Density')
            ax.legend()
            ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        return fig
        
    except Exception as e:
        logger.error(f"特征分布图创建失败: {e}")
        return None

def plot_results(experiment_result, save_dir):
    """绘制实验结果图表"""
    try:
        # 1. 优化历史图
        plot_optimization_history(experiment_result, save_dir)
        
        # 2. 参数重要性图
        plot_parameter_importance(experiment_result, save_dir)
        
        # 3. 性能指标雷达图
        plot_performance_radar(experiment_result, save_dir)
        
    except Exception as e:
        logger.error(f"结果绘图失败: {e}")

def plot_optimization_history(experiment_result, save_dir):
    """绘制优化历史"""
    try:
        opt_result = experiment_result['optimization_result']
        
        plt.figure(figsize=(12, 5))
        
        # 最佳适应度历史
        plt.subplot(1, 2, 1)
        plt.plot(opt_result['best_fitness_history'], 'b-', linewidth=2, label='Best Fitness')
        plt.plot(opt_result['avg_fitness_history'], 'r--', linewidth=2, label='Average Fitness')
        plt.xlabel('Generation')
        plt.ylabel('Fitness')
        plt.title('Optimization History')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 收敛分析
        plt.subplot(1, 2, 2)
        best_fitness = opt_result['best_fitness_history']
        improvements = [best_fitness[i] - best_fitness[i-1] if i > 0 else 0 
                       for i in range(len(best_fitness))]
        plt.plot(improvements, 'g-', linewidth=2)
        plt.xlabel('Generation')
        plt.ylabel('Fitness Improvement')
        plt.title('Convergence Analysis')
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(os.path.join(save_dir, 'optimization_history.png'), dpi=300, bbox_inches='tight')
        plt.close()
        
    except Exception as e:
        logger.error(f"优化历史绘图失败: {e}")

def plot_parameter_importance(experiment_result, save_dir):
    """绘制参数重要性（简化版）"""
    try:
        params = experiment_result['best_params']
        
        param_names = ['ADASYN k', 'ADASYN α', 'WGAN λ', 'WGAN n_critic', 'WGAN lr', 'WGAN batch_size']
        param_values = [
            params['adasyn_k'],
            params['adasyn_alpha'],
            params['wgan_lambda'],
            params['wgan_n_critic'],
            params['wgan_lr'],
            params['wgan_batch_size']
        ]
        
        # 标准化值用于可视化
        normalized_values = [(v - min(param_values)) / (max(param_values) - min(param_values)) 
                           for v in param_values]
        
        plt.figure(figsize=(10, 6))
        bars = plt.bar(param_names, normalized_values, color='skyblue', alpha=0.7)
        plt.title('Optimized Parameter Values (Normalized)')
        plt.ylabel('Normalized Value')
        plt.xticks(rotation=45)
        
        # 添加实际值标签
        for bar, value in zip(bars, param_values):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                    f'{value:.3f}' if isinstance(value, float) else str(value),
                    ha='center', va='bottom')
        
        plt.tight_layout()
        plt.savefig(os.path.join(save_dir, 'parameter_values.png'), dpi=300, bbox_inches='tight')
        plt.close()
        
    except Exception as e:
        logger.error(f"参数重要性绘图失败: {e}")

def plot_performance_radar(experiment_result, save_dir):
    """绘制性能指标雷达图"""
    try:
        if 'test_results' not in experiment_result:
            return
        
        test_results = experiment_result['test_results']
        
        # 提取指标
        metrics = ['F1 Weighted', 'F1 Minority', 'Precision', 'Recall', 'AUC-ROC', 'G-mean']
        values = [
            test_results.get('f1_weighted', 0),
            test_results.get('f1_minority', 0),
            test_results.get('precision_minority', 0),
            test_results.get('recall_minority', 0),
            test_results.get('auc_roc', 0),
            test_results.get('geometric_mean', 0)
        ]
        
        # 雷达图
        angles = np.linspace(0, 2 * np.pi, len(metrics), endpoint=False).tolist()
        values += values[:1]  # 闭合图形
        angles += angles[:1]
        
        fig, ax = plt.subplots(figsize=(8, 8), subplot_kw=dict(projection='polar'))
        ax.plot(angles, values, 'o-', linewidth=2, color='blue', alpha=0.7)
        ax.fill(angles, values, alpha=0.25, color='blue')
        ax.set_xticks(angles[:-1])
        ax.set_xticklabels(metrics)
        ax.set_ylim(0, 1)
        ax.set_title('Performance Metrics Radar Chart', pad=20)
        ax.grid(True)
        
        plt.savefig(os.path.join(save_dir, 'performance_radar.png'), dpi=300, bbox_inches='tight')
        plt.close()
        
    except Exception as e:
        logger.error(f"雷达图绘制失败: {e}")
