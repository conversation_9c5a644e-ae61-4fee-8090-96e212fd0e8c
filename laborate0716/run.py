"""
启动脚本：提供简单的命令行界面来运行不同的实验模式
"""

import sys
import os
import argparse
import logging

def setup_basic_logging():
    """设置基本日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )

def run_system_test():
    """运行系统测试"""
    print("运行系统测试...")
    try:
        from test_system import run_all_tests
        success = run_all_tests()
        return success
    except Exception as e:
        print(f"系统测试失败: {e}")
        return False

def run_quick_demo():
    """运行快速演示"""
    print("运行快速演示...")
    try:
        from quick_demo import main
        main()
        return True
    except Exception as e:
        print(f"快速演示失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_full_experiment():
    """运行完整实验"""
    print("运行完整实验...")
    try:
        from main_experiment import main
        main()
        return True
    except Exception as e:
        print(f"完整实验失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_dependencies():
    """检查依赖包"""
    print("运行兼容性检查...")
    try:
        from check_compatibility import main as check_main
        return check_main()
    except Exception as e:
        print(f"兼容性检查失败: {e}")
        return False

def show_help():
    """显示帮助信息"""
    help_text = """
遗传算法优化ADASYN+WGAN-GP参数系统

使用方法:
    python run.py [命令]

可用命令:
    check       - 运行兼容性检查 (推荐首次使用)
    test        - 运行系统测试
    demo        - 运行快速演示
    experiment  - 运行完整实验
    deps        - 检查依赖包 (同check)
    help        - 显示此帮助信息

示例:
    python run.py test          # 测试系统是否正常
    python run.py demo          # 快速演示 (5-10分钟)
    python run.py experiment    # 完整实验 (可能需要几小时)

注意:
    - 首次使用建议先运行 'test' 检查系统
    - 然后运行 'demo' 了解系统功能
    - 最后运行 'experiment' 进行完整实验
    """
    print(help_text)

def main():
    """主函数"""
    # 设置基本日志
    setup_basic_logging()
    
    # 解析命令行参数
    parser = argparse.ArgumentParser(
        description='遗传算法优化ADASYN+WGAN-GP参数系统',
        add_help=False
    )
    parser.add_argument('command', nargs='?', default='help',
                       choices=['test', 'demo', 'experiment', 'deps', 'check', 'help'],
                       help='要执行的命令')
    
    args = parser.parse_args()
    
    print("=" * 60)
    print("遗传算法优化ADASYN+WGAN-GP参数系统")
    print("=" * 60)
    
    # 执行相应命令
    if args.command == 'help':
        show_help()
    
    elif args.command == 'deps' or args.command == 'check':
        success = check_dependencies()
        if success:
            print("\n✅ 兼容性检查通过！")
            print("下一步建议: python run.py test")
        else:
            print("\n❌ 发现兼容性问题，请根据建议进行修复。")
        sys.exit(0 if success else 1)
    
    elif args.command == 'test':
        success = run_system_test()
        if success:
            print("\n✅ 系统测试通过！可以继续使用其他功能。")
            print("下一步建议: python run.py demo")
        else:
            print("\n❌ 系统测试失败，请检查错误信息。")
        sys.exit(0 if success else 1)
    
    elif args.command == 'demo':
        print("开始快速演示...")
        print("注意: 这将使用合成数据集运行简化的优化过程")
        print("预计用时: 5-10分钟")
        print("-" * 40)
        
        success = run_quick_demo()
        if success:
            print("\n✅ 快速演示完成！")
            print("下一步建议: python run.py experiment (运行完整实验)")
        else:
            print("\n❌ 快速演示失败，请检查错误信息。")
        sys.exit(0 if success else 1)
    
    elif args.command == 'experiment':
        print("开始完整实验...")
        print("注意: 这将在所有配置的数据集上运行完整优化")
        print("预计用时: 几小时 (取决于数据集数量和硬件性能)")
        print("-" * 40)
        
        # 确认用户想要继续
        try:
            confirm = input("确认要开始完整实验吗? (y/N): ").strip().lower()
            if confirm not in ['y', 'yes', '是']:
                print("实验已取消")
                sys.exit(0)
        except KeyboardInterrupt:
            print("\n实验已取消")
            sys.exit(0)
        
        success = run_full_experiment()
        if success:
            print("\n✅ 完整实验完成！")
            print("结果已保存在 results/ 目录中")
        else:
            print("\n❌ 完整实验失败，请检查错误信息。")
        sys.exit(0 if success else 1)
    
    else:
        print(f"未知命令: {args.command}")
        show_help()
        sys.exit(1)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n程序被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n程序执行出错: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
