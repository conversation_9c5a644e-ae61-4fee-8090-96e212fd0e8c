# 遗传算法优化ADASYN+WGAN-GP参数项目依赖

# 核心机器学习库
numpy>=1.19.0
pandas>=1.2.0
scikit-learn>=0.24.0
scipy>=1.5.0

# 不平衡学习
imbalanced-learn>=0.8.0

# 深度学习框架 (WGAN-GP)
torch>=1.9.0
torchvision>=0.10.0

# 可视化
matplotlib>=3.4.0
seaborn>=0.11.0

# 遗传算法库 (可选，我们自己实现了)
deap>=1.3.0

# 数据处理
openpyxl>=3.0.0

# 进度条
tqdm>=4.62.0

# 统计分析
statsmodels>=0.12.0

# 并行处理
joblib>=1.0.0

# 配置文件处理
pyyaml>=5.4.0

# 日志增强
colorlog>=6.0.0

# 内存分析 (可选)
memory-profiler>=0.60.0

# GPU加速 (如果有CUDA)
# torch-audio>=0.9.0  # 可选
# torch-text>=0.10.0  # 可选

# 实验跟踪 (可选)
# mlflow>=1.20.0
# wandb>=0.12.0

# 代码质量
# black>=21.0.0
# flake8>=3.9.0
# pytest>=6.2.0
