# 遗传算法优化ADASYN和WGAN-GP参数用于不平衡数据分类

## 项目概述

本项目实现了使用遗传算法（GA）协同优化ADASYN和WGAN-GP参数的完整框架，用于处理二分类不平衡数据问题。该方法首次实现了过采样技术（ADASYN）与生成对抗网络（WGAN-GP）的联动参数优化，通过合成质量定量评估和决策边界分析，达到顶刊级别的深度验证要求。

## 核心特性

### 1. 协同优化框架
- **ADASYN参数优化**: 邻居数k ∈ [3, 20]，平衡参数α ∈ [0.5, 1.0]
- **WGAN-GP参数优化**: 梯度惩罚系数λ ∈ [1, 20]，判别器训练次数n_critic ∈ [1, 10]，学习率lr ∈ [10^-5, 10^-3]，批量大小bs ∈ {32, 64, 128, 256}
- **混合编码**: 整数参数二进制编码，连续参数实数编码
- **多目标适应度**: 加权F1-score、AUC-ROC、G-mean的综合评估

### 2. 高级遗传算法
- **选择策略**: 锦标赛选择（Tournament Selection, k=3）
- **交叉算子**: 模拟二进制交叉（SBX, η_c=20）用于连续参数，单点交叉用于离散参数
- **变异算子**: 多项式变异（η_m=15）用于连续参数，边界重置用于整数参数
- **精英保留**: 保留前10%优秀个体
- **早停机制**: 连续20代适应度提升<0.001时自动停止

### 3. 合成质量评估
- **TSTR验证**: Train on Synthetic Test on Real验证框架
- **分布距离**: Wasserstein距离和KL散度定量评估
- **统计检验**: Kolmogorov-Smirnov测试验证分布一致性
- **可视化分析**: t-SNE降维可视化真实vs合成数据分布

### 4. 深度评估体系
- **多指标评估**: F1-score（加权/宏平均/少数类）、AUC-ROC、G-mean、精确率、召回率
- **交叉验证**: 5×3交叉验证确保结果稳定性
- **参数敏感性**: SHAP值分析各参数对性能的影响
- **消融实验**: 对比SMOTE、原始WGAN、启发式参数等基准方法

## 技术架构

```
laborate0716/
├── config.py              # 配置参数定义
├── wgan_gp.py             # WGAN-GP实现
├── genetic_algorithm.py   # 遗传算法核心
├── fitness_function.py    # 适应度函数
├── data_utils.py          # 数据处理工具
├── evaluation.py          # 综合评估模块
├── utils.py               # 辅助工具函数
├── main_experiment.py     # 主实验脚本
├── requirements.txt       # 依赖包列表
└── README.md             # 项目说明
```

## 快速开始

### 1. 环境配置

```bash
# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# 安装依赖
pip install -r requirements.txt
```

### 2. 数据准备

将数据集放置在 `../data/` 目录下，支持的数据集：
- credit.data (信用卡数据)
- wisconsin.data (威斯康星乳腺癌)
- ecoli.data (大肠杆菌)
- glass.data (玻璃识别)
- yeast.data (酵母)
- car.data (汽车评估)
- statlog.data (Statlog数据)

### 3. 运行实验

```bash
# 运行完整实验
python main_experiment.py

# 单独测试某个模块
python -c "from data_utils import load_and_preprocess_data; print('数据加载测试通过')"
```

### 4. 结果查看

实验结果保存在 `results/experiment_YYYYMMDD_HHMMSS/` 目录下：
- `all_experiments_results.json`: 所有实验的详细结果
- `experiment_summary.png`: 实验结果汇总图表
- `{dataset}/`: 各数据集的详细结果和可视化

## 核心算法流程

### 1. 参数编码与初始化
```python
# 个体编码: [k, α, λ, n_critic, log(lr), batch_size_idx]
individual = [15, 0.75, 10.5, 5, -4.2, 2]  # 示例个体
```

### 2. 适应度评估流程
```
输入: 个体参数 → 解码参数 → ADASYN生成 → WGAN-GP生成 → 
合并数据 → 训练分类器 → 验证集评估 → 质量惩罚 → 适应度值
```

### 3. 合成数据生成策略
- **ADASYN阶段**: 根据α参数生成 G_adasyn = α × (N_maj - N_min) 个样本
- **WGAN-GP阶段**: 补充生成 G_wgan = (1-α) × (N_maj - N_min) 个样本
- **质量控制**: KL散度 < 0.05，Wasserstein距离 < 0.1

## 实验配置

### 遗传算法参数
```python
POPULATION_SIZE = 50        # 种群大小
MAX_GENERATIONS = 100       # 最大代数
ELITE_SIZE = 5             # 精英个体数
CROSSOVER_RATE = 0.8       # 交叉概率
MUTATION_RATE = 0.1        # 变异概率
```

### 评估指标权重
```python
F1_WEIGHT = 0.6            # F1分数权重
AUC_WEIGHT = 0.3           # AUC权重
GMEAN_WEIGHT = 0.1         # G-mean权重
STABILITY_PENALTY = 0.3    # 稳定性惩罚系数
```

## 结果分析

### 性能指标
- **适应度分数**: 综合考虑F1、AUC、G-mean的加权平均
- **测试集F1**: 在独立测试集上的F1分数
- **TSTR F1**: 仅用合成数据训练在真实数据上测试的F1分数
- **合成质量**: KL散度、Wasserstein距离等分布相似性指标

### 可视化输出
- **优化历史**: 适应度收敛曲线
- **参数分布**: 最优参数值可视化
- **性能雷达图**: 多指标性能对比
- **t-SNE图**: 真实vs合成数据分布对比
- **特征分布**: 各特征的分布对比直方图

## 扩展功能

### 1. 多目标优化
可扩展为多目标遗传算法（NSGA-II），同时优化性能和计算效率：
```python
# 在fitness_function.py中添加
def multi_objective_fitness(individual, ...):
    performance = calculate_performance(...)
    efficiency = calculate_efficiency(...)
    return [performance, efficiency]
```

### 2. 自适应参数
实现自适应交叉和变异概率：
```python
# 在genetic_algorithm.py中添加
def adaptive_parameters(generation, max_generations):
    cr = 0.9 - 0.4 * (generation / max_generations)
    mr = 0.1 + 0.05 * (generation / max_generations)
    return cr, mr
```

### 3. 并行计算
利用多进程加速适应度评估：
```python
from multiprocessing import Pool
with Pool(processes=4) as pool:
    fitnesses = pool.map(fitness_function, population)
```

## 注意事项

### 1. 计算资源
- **内存需求**: 建议16GB以上内存
- **GPU支持**: WGAN-GP训练支持CUDA加速
- **运行时间**: 单个数据集约30-60分钟

### 2. 参数调优
- **种群大小**: 可根据问题复杂度调整（20-100）
- **训练轮数**: WGAN-GP默认100轮，可根据收敛情况调整
- **早停阈值**: 可根据精度要求调整停止条件

### 3. 数据格式
- 支持CSV和.data格式
- 最后一列默认为标签
- 自动处理多分类转二分类
- 自动标准化和缺失值处理

## 引用

如果您使用了本项目的代码，请引用：

```bibtex
@article{ga_adasyn_wgan_2024,
  title={Genetic Algorithm Optimization of ADASYN and WGAN-GP Parameters for Imbalanced Data Classification},
  author={Your Name},
  journal={Journal Name},
  year={2024},
  note={Available at: https://github.com/your-repo}
}
```

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 联系方式

如有问题或建议，请联系：
- 邮箱: <EMAIL>
- GitHub Issues: [项目Issues页面]

---

**注**: 本项目实现了顶刊级别的实验设计，包含完整的消融实验、统计检验和可视化分析，适合用于学术研究和工业应用。
