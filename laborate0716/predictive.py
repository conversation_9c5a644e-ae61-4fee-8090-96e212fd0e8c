"""
Predictive数据集专门实验脚本
对比不同方法处理不平衡数据的效果，包括：
1. GA优化ADASYN-WGAN-GP参数方法
2. 不使用GA优化ADASYN-WGAN-GP参数方法
3. GA只优化ADASYN参数方法
4. GA只优化WGAN-GP参数方法
5. 传统ADASYN方法

其中散热失效(HDF)作为少数类，其他合并为多数类
"""

import os
import sys
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import StratifiedKFold, train_test_split
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import LabelEncoder, StandardScaler
from sklearn.metrics import f1_score, classification_report, confusion_matrix
from imblearn.over_sampling import ADASYN, SMOTE
import warnings
warnings.filterwarnings('ignore')

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from wgan_gp import WGAN_GP
from genetic_algorithm import GeneticAlgorithm
from config import ga_config, param_ranges, wgan_config
import time

def geometric_mean_score(y_true, y_pred):
    """计算G-mean (几何平均数)"""
    from sklearn.metrics import confusion_matrix
    cm = confusion_matrix(y_true, y_pred)
    if cm.shape == (2, 2):
        tn, fp, fn, tp = cm.ravel()
        sensitivity = tp / (tp + fn) if (tp + fn) > 0 else 0
        specificity = tn / (tn + fp) if (tn + fp) > 0 else 0
        return np.sqrt(sensitivity * specificity)
    return 0

def load_predictive_data():
    """加载并预处理predictive数据集"""
    print("加载Predictive数据集...")
    
    # 数据路径
    data_path = "C:/Users/<USER>/Desktop/GAAD/data/predictive.csv"
    
    # 加载数据
    try:
        data = pd.read_csv(data_path)
        print(f"数据加载成功，形状: {data.shape}")
        print(f"列名: {list(data.columns)}")
    except FileNotFoundError:
        print(f"数据文件未找到: {data_path}")
        print("创建模拟predictive数据集...")
        # 创建模拟数据
        np.random.seed(42)
        n_samples = 1000
        data = pd.DataFrame({
            'Air_temperature': np.random.normal(25, 5, n_samples),
            'Process_temperature': np.random.normal(35, 8, n_samples),
            'Rotational_speed': np.random.normal(1500, 200, n_samples),
            'Torque': np.random.normal(40, 10, n_samples),
            'Tool_wear': np.random.uniform(0, 300, n_samples),
            'Type': np.random.choice(['L', 'M', 'H'], n_samples, p=[0.6, 0.3, 0.1]),
            'Machine_failure': np.random.choice([0, 1], n_samples, p=[0.97, 0.03]),
            'TWF': np.random.choice([0, 1], n_samples, p=[0.99, 0.01]),
            'HDF': np.random.choice([0, 1], n_samples, p=[0.98, 0.02]),  # 散热失效
            'PWF': np.random.choice([0, 1], n_samples, p=[0.99, 0.01]),
            'OSF': np.random.choice([0, 1], n_samples, p=[0.995, 0.005]),
            'RNF': np.random.choice([0, 1], n_samples, p=[0.995, 0.005])
        })
    
    print("原始数据概览:")
    print(data.head())
    
    # 检查是否有HDF列（散热失效）
    if 'HDF' not in data.columns:
        print("警告: 数据中未找到HDF列，尝试查找相关列...")
        # 查找可能的失效类型列
        failure_cols = [col for col in data.columns if 'failure' in col.lower() or 'HDF' in col or 'heat' in col.lower()]
        print(f"可能的失效相关列: {failure_cols}")
        
        if failure_cols:
            # 使用第一个找到的失效列
            target_col = failure_cols[0]
            print(f"使用 {target_col} 作为目标列")
        else:
            # 如果没有找到，创建一个模拟的HDF列
            print("创建模拟HDF列")
            data['HDF'] = np.random.choice([0, 1], len(data), p=[0.98, 0.02])
            target_col = 'HDF'
    else:
        target_col = 'HDF'
    
    print(f"\n目标列 '{target_col}' 的分布:")
    print(data[target_col].value_counts())
    
    # 准备特征和标签
    # 排除目标列和其他可能的标识列
    exclude_cols = [target_col, 'Machine_failure', 'TWF', 'PWF', 'OSF', 'RNF']
    if 'UDI' in data.columns:
        exclude_cols.append('UDI')  # 唯一标识符
    if 'Product_ID' in data.columns:
        exclude_cols.append('Product_ID')  # 产品ID
    
    feature_cols = [col for col in data.columns if col not in exclude_cols]
    X = data[feature_cols].copy()
    
    print(f"\n使用的特征列: {feature_cols}")
    
    # 处理分类变量
    categorical_cols = X.select_dtypes(include=['object']).columns
    if len(categorical_cols) > 0:
        print(f"处理分类变量: {list(categorical_cols)}")
        for col in categorical_cols:
            le = LabelEncoder()
            X[col] = le.fit_transform(X[col].astype(str))
    
    # 处理标签：HDF=1作为少数类，其他作为多数类
    y = data[target_col].astype(int)
    
    # 标准化特征
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)
    
    # 统计信息
    minority_count = np.sum(y == 1)
    majority_count = np.sum(y == 0)
    imbalance_ratio = majority_count / minority_count if minority_count > 0 else float('inf')
    
    print(f"\n数据集统计:")
    print(f"总样本数: {len(y)}")
    print(f"特征数: {X_scaled.shape[1]}")
    print(f"少数类(HDF=1)数目: {minority_count}")
    print(f"多数类(HDF=0)数目: {majority_count}")
    print(f"不平衡比例: {imbalance_ratio:.2f}:1")
    
    return X_scaled, y, minority_count, majority_count, imbalance_ratio

def fitness_function_adasyn_only(individual, X_train, y_train, X_val, y_val):
    """只优化ADASYN参数的适应度函数"""
    try:
        k = int(individual[0])
        alpha = individual[1]
        
        # 提取少数类样本
        X_min = X_train[y_train == 1]
        N_min = len(X_min)
        N_maj = len(X_train[y_train == 0])
        
        if N_min == 0:
            return 0.0
        
        # 使用ADASYN生成样本
        target_samples = int(N_min + alpha * (N_maj - N_min))
        
        try:
            adasyn = ADASYN(
                sampling_strategy={1: target_samples},
                n_neighbors=min(k, N_min-1) if N_min > 1 else 1,
                random_state=42
            )
            X_resampled, y_resampled = adasyn.fit_resample(X_train, y_train)
            
            # 训练分类器
            clf = RandomForestClassifier(random_state=42)
            clf.fit(X_resampled, y_resampled)
            
            # 在验证集上评估
            y_pred = clf.predict(X_val)
            f1 = f1_score(y_val, y_pred, average='weighted')
            gmean = geometric_mean_score(y_val, y_pred)
            
            return 0.7 * f1 + 0.3 * gmean
            
        except Exception as e:
            print(f"ADASYN生成失败: {e}")
            return 0.0
            
    except Exception as e:
        print(f"适应度函数执行失败: {e}")
        return 0.0

def fitness_function_wgan_only(individual, X_train, y_train, X_val, y_val):
    """只优化WGAN-GP参数的适应度函数"""
    try:
        lambda_gp = individual[0]
        n_critic = int(individual[1])
        lr = 10 ** individual[2]
        batch_size_idx = int(individual[3])
        batch_size = param_ranges.WGAN_BATCH_SIZES[batch_size_idx]
        
        # 提取少数类样本
        X_min = X_train[y_train == 1]
        N_min = len(X_min)
        N_maj = len(X_train[y_train == 0])
        
        if N_min == 0:
            return 0.0
        
        # 使用WGAN-GP生成样本
        try:
            if len(X_min) < batch_size:
                batch_size = max(1, len(X_min) // 2)
            
            wgan_gp = WGAN_GP(
                latent_dim=wgan_config.LATENT_DIM,
                data_dim=X_train.shape[1],
                lambda_gp=lambda_gp,
                lr=lr,
                batch_size=batch_size,
                n_critic=n_critic,
                force_cpu=True  # 使用CPU避免CUDA问题
            )
            
            # 训练WGAN-GP
            wgan_gp.train(X_min, epochs=50)  # 减少训练轮数
            
            # 生成样本
            n_generate = N_maj - N_min
            X_synthetic = wgan_gp.generate_samples(n_generate)
            
            # 构建平衡数据集
            X_balanced = np.vstack([X_train, X_synthetic])
            y_balanced = np.concatenate([y_train, np.ones(len(X_synthetic))])
            
            # 训练分类器
            clf = RandomForestClassifier(random_state=42)
            clf.fit(X_balanced, y_balanced)
            
            # 在验证集上评估
            y_pred = clf.predict(X_val)
            f1 = f1_score(y_val, y_pred, average='weighted')
            gmean = geometric_mean_score(y_val, y_pred)
            
            return 0.7 * f1 + 0.3 * gmean
            
        except Exception as e:
            print(f"WGAN-GP生成失败: {e}")
            return 0.0
            
    except Exception as e:
        print(f"适应度函数执行失败: {e}")
        return 0.0

def method_ga_adasyn_wgan(X_train, y_train, X_test, y_test):
    """方法1: GA优化ADASYN-WGAN-GP参数"""
    print("\n=== 方法1: GA优化ADASYN-WGAN-GP参数 ===")
    
    from fitness_function import fitness_function
    
    # 数据划分用于GA优化
    X_train_sub, X_val, y_train_sub, y_val = train_test_split(
        X_train, y_train, test_size=0.3, stratify=y_train, random_state=42
    )
    
    print(f"数据划分完成:")
    print(f"  训练子集: {len(X_train_sub)} 样本 (少数类: {np.sum(y_train_sub == 1)})")
    print(f"  验证集: {len(X_val)} 样本 (少数类: {np.sum(y_val == 1)})")
    
    # 初始化遗传算法
    ga = GeneticAlgorithm(
        fitness_func=fitness_function,
        param_bounds=param_ranges.get_param_bounds(),
        param_types=param_ranges.get_param_types(),
        population_size=20,  # 减小种群大小加速
        max_generations=15,  # 减少代数
        elite_size=3
    )
    
    print(f"\n遗传算法配置:")
    print(f"  种群大小: {ga.population_size}")
    print(f"  最大代数: {ga.max_generations}")
    print(f"  精英个体数: {ga.elite_size}")
    print(f"  参数维度: {len(param_ranges.get_param_bounds())}")
    
    print(f"\n开始遗传算法优化...")
    print(f"参数搜索空间:")
    print(f"  ADASYN k邻居: [3, 20]")
    print(f"  ADASYN α平衡: [0.5, 1.0]")
    print(f"  WGAN λ梯度惩罚: [1, 20]")
    print(f"  WGAN 判别器训练次数: [1, 10]")
    print(f"  WGAN 学习率: [1e-5, 1e-3]")
    print(f"  WGAN 批量大小: {param_ranges.WGAN_BATCH_SIZES}")
    
    start_time = time.time()
    best_individual, optimization_result = ga.optimize_with_progress(X_train_sub, y_train_sub, X_val, y_val)
    optimization_time = time.time() - start_time
    
    print(f"\n优化完成，用时: {optimization_time:.1f}秒")
    print(f"最佳适应度: {best_individual.fitness:.4f}")
    print(f"运行代数: {optimization_result['generations_run']}")
    
    # 解码并显示最优参数
    from fitness_function import decode_individual
    k, alpha, lambda_gp, n_critic, lr, batch_size = decode_individual(best_individual.genes)
    
    print(f"\n最优参数组合:")
    print(f"  ADASYN k邻居: {k}")
    print(f"  ADASYN α平衡: {alpha:.4f}")
    print(f"  WGAN λ梯度惩罚: {lambda_gp:.4f}")
    print(f"  WGAN 判别器训练次数: {n_critic}")
    print(f"  WGAN 学习率: {lr:.2e}")
    print(f"  WGAN 批量大小: {batch_size}")
    
    # 生成最终的平衡数据集
    print(f"\n使用最优参数生成平衡数据集...")
    X_balanced, y_balanced, losses = generate_balanced_data_adasyn_wgan(
        X_train, y_train, k, alpha, lambda_gp, n_critic, lr, batch_size
    )
    
    return X_balanced, y_balanced, losses, best_individual.fitness

def method_no_ga_adasyn_wgan(X_train, y_train, X_test, y_test):
    """方法2: 不使用GA优化ADASYN-WGAN-GP参数（使用默认参数）"""
    print("\n=== 方法2: 不使用GA优化ADASYN-WGAN-GP参数 ===")
    
    # 使用默认参数
    k = 5
    alpha = 0.7
    lambda_gp = 10.0
    n_critic = 5
    lr = 1e-4
    batch_size = 64
    
    print(f"使用默认参数: k={k}, α={alpha}, λ={lambda_gp}, n_critic={n_critic}")
    
    X_balanced, y_balanced, losses = generate_balanced_data_adasyn_wgan(
        X_train, y_train, k, alpha, lambda_gp, n_critic, lr, batch_size
    )
    
    return X_balanced, y_balanced, losses, 0.0

def method_ga_adasyn_only(X_train, y_train, X_test, y_test):
    """方法3: GA只优化ADASYN参数"""
    print("\n=== 方法3: GA只优化ADASYN参数 ===")

    X_train_sub, X_val, y_train_sub, y_val = train_test_split(
        X_train, y_train, test_size=0.3, stratify=y_train, random_state=42
    )

    # ADASYN参数范围
    adasyn_bounds = [
        (3, 20),    # k
        (0.5, 1.0)  # alpha
    ]
    adasyn_types = ['int', 'float']

    ga = GeneticAlgorithm(
        fitness_func=fitness_function_adasyn_only,
        param_bounds=adasyn_bounds,
        param_types=adasyn_types,
        population_size=15,
        max_generations=10,
        elite_size=2
    )

    start_time = time.time()
    best_individual, _ = ga.optimize_with_progress(X_train_sub, y_train_sub, X_val, y_val)
    optimization_time = time.time() - start_time

    print(f"优化完成，用时: {optimization_time:.1f}秒")
    print(f"最佳适应度: {best_individual.fitness:.4f}")

    # 使用最优ADASYN参数
    k = int(best_individual.genes[0])
    alpha = best_individual.genes[1]

    print(f"最优ADASYN参数: k={k}, α={alpha:.4f}")

    X_balanced, y_balanced = generate_balanced_data_adasyn_only(X_train, y_train, k, alpha)

    return X_balanced, y_balanced, None, best_individual.fitness

def method_ga_wgan_only(X_train, y_train, X_test, y_test):
    """方法4: GA只优化WGAN-GP参数"""
    print("\n=== 方法4: GA只优化WGAN-GP参数 ===")

    X_train_sub, X_val, y_train_sub, y_val = train_test_split(
        X_train, y_train, test_size=0.3, stratify=y_train, random_state=42
    )

    # WGAN-GP参数范围
    wgan_bounds = [
        (1.0, 20.0),  # lambda_gp
        (1, 10),      # n_critic
        (-5, -3),     # log(lr)
        (0, 3)        # batch_size_idx
    ]
    wgan_types = ['float', 'int', 'float', 'int']

    ga = GeneticAlgorithm(
        fitness_func=fitness_function_wgan_only,
        param_bounds=wgan_bounds,
        param_types=wgan_types,
        population_size=15,
        max_generations=10,
        elite_size=2
    )

    start_time = time.time()
    best_individual, _ = ga.optimize_with_progress(X_train_sub, y_train_sub, X_val, y_val)
    optimization_time = time.time() - start_time

    print(f"优化完成，用时: {optimization_time:.1f}秒")
    print(f"最佳适应度: {best_individual.fitness:.4f}")

    # 使用最优WGAN-GP参数
    lambda_gp = best_individual.genes[0]
    n_critic = int(best_individual.genes[1])
    lr = 10 ** best_individual.genes[2]
    batch_size_idx = int(best_individual.genes[3])
    batch_size = param_ranges.WGAN_BATCH_SIZES[batch_size_idx]

    print(f"最优WGAN-GP参数: λ={lambda_gp:.2f}, n_critic={n_critic}, lr={lr:.2e}, batch_size={batch_size}")

    X_balanced, y_balanced, losses = generate_balanced_data_wgan_only(
        X_train, y_train, lambda_gp, n_critic, lr, batch_size
    )

    return X_balanced, y_balanced, losses, best_individual.fitness

def method_traditional_adasyn(X_train, y_train, X_test, y_test):
    """方法5: 传统ADASYN方法"""
    print("\n=== 方法5: 传统ADASYN方法 ===")

    try:
        adasyn = ADASYN(random_state=42)
        X_balanced, y_balanced = adasyn.fit_resample(X_train, y_train)

        # 确保返回numpy数组
        X_balanced = np.asarray(X_balanced)
        y_balanced = np.asarray(y_balanced, dtype=int)

        print(f"ADASYN处理完成")
        print(f"原始数据: {len(X_train)} 样本")
        print(f"平衡后数据: {len(X_balanced)} 样本")
        print(f"平衡后类别分布: {np.bincount(y_balanced)}")

        return X_balanced, y_balanced, None, 0.0

    except Exception as e:
        print(f"传统ADASYN失败: {e}")
        return np.asarray(X_train), np.asarray(y_train, dtype=int), None, 0.0

def generate_balanced_data_adasyn_wgan(X_train, y_train, k, alpha, lambda_gp, n_critic, lr, batch_size):
    """使用ADASYN+WGAN-GP生成平衡数据"""
    try:
        # 提取少数类和多数类
        X_min = X_train[y_train == 1]
        X_maj = X_train[y_train == 0]
        N_min = len(X_min)
        N_maj = len(X_maj)
        G_total = N_maj - N_min

        print(f"原始数据: 多数类{N_maj}, 少数类{N_min}")

        # ADASYN生成
        G_adasyn = int(alpha * G_total)
        X_synthetic = np.zeros((0, X_train.shape[1]))

        if G_adasyn > 0:
            try:
                adasyn = ADASYN(
                    sampling_strategy={1: N_min + G_adasyn},
                    n_neighbors=min(k, N_min-1) if N_min > 1 else 1,
                    random_state=42
                )
                X_adasyn_res, y_adasyn_res = adasyn.fit_resample(X_train, y_train)
                X_adasyn_only = X_adasyn_res[len(X_train):]
                X_synthetic = X_adasyn_only
                print(f"ADASYN生成: {len(X_adasyn_only)} 个样本")
            except Exception as e:
                print(f"ADASYN生成失败: {e}")

        # WGAN-GP生成
        G_wgan = G_total - G_adasyn
        losses = {'d_losses': [], 'g_losses': [], 'w_distances': []}

        if G_wgan > 0:
            try:
                real_minority = np.vstack([X_min, X_synthetic]) if len(X_synthetic) > 0 else X_min

                if len(real_minority) < batch_size:
                    batch_size = max(1, len(real_minority) // 2)

                wgan_gp = WGAN_GP(
                    latent_dim=wgan_config.LATENT_DIM,
                    data_dim=X_train.shape[1],
                    lambda_gp=lambda_gp,
                    lr=lr,
                    batch_size=batch_size,
                    n_critic=n_critic,
                    force_cpu=True
                )

                print(f"训练WGAN-GP (共{80}轮)...")
                print(f"  训练数据: {len(real_minority)} 个少数类样本")
                print(f"  网络配置: 潜在维度={wgan_config.LATENT_DIM}, 数据维度={X_train.shape[1]}")
                print(f"  训练参数: λ={lambda_gp:.2f}, n_critic={n_critic}, lr={lr:.2e}, batch_size={batch_size}")

                wgan_gp.train_with_progress(real_minority, epochs=80)

                # 获取训练损失
                losses = wgan_gp.get_training_history()

                X_wgan = wgan_gp.generate_samples(G_wgan)
                X_synthetic = np.vstack([X_synthetic, X_wgan]) if len(X_synthetic) > 0 else X_wgan
                print(f"WGAN-GP生成: {len(X_wgan)} 个样本")

            except Exception as e:
                print(f"WGAN-GP生成失败: {e}")

        # 构建平衡数据集
        X_balanced = np.vstack([X_train, X_synthetic]) if len(X_synthetic) > 0 else X_train
        y_balanced = np.concatenate([y_train, np.ones(len(X_synthetic))]) if len(X_synthetic) > 0 else y_train

        # 确保返回numpy数组
        X_balanced = np.asarray(X_balanced)
        y_balanced = np.asarray(y_balanced, dtype=int)

        print(f"最终平衡数据: {len(X_balanced)} 样本, 类别分布: {np.bincount(y_balanced)}")

        return X_balanced, y_balanced, losses

    except Exception as e:
        print(f"数据生成失败: {e}")
        return np.asarray(X_train), np.asarray(y_train, dtype=int), {'d_losses': [], 'g_losses': [], 'w_distances': []}

def generate_balanced_data_adasyn_only(X_train, y_train, k, alpha):
    """只使用ADASYN生成平衡数据"""
    try:
        N_min = np.sum(y_train == 1)
        N_maj = np.sum(y_train == 0)
        target_samples = int(N_min + alpha * (N_maj - N_min))

        adasyn = ADASYN(
            sampling_strategy={1: target_samples},
            n_neighbors=min(k, N_min-1) if N_min > 1 else 1,
            random_state=42
        )
        X_balanced, y_balanced = adasyn.fit_resample(X_train, y_train)

        # 确保返回numpy数组
        X_balanced = np.asarray(X_balanced)
        y_balanced = np.asarray(y_balanced, dtype=int)

        print(f"ADASYN生成完成: {len(X_balanced)} 样本, 类别分布: {np.bincount(y_balanced)}")

        return X_balanced, y_balanced

    except Exception as e:
        print(f"ADASYN生成失败: {e}")
        return np.asarray(X_train), np.asarray(y_train, dtype=int)

def generate_balanced_data_wgan_only(X_train, y_train, lambda_gp, n_critic, lr, batch_size):
    """只使用WGAN-GP生成平衡数据"""
    try:
        X_min = X_train[y_train == 1]
        N_min = len(X_min)
        N_maj = len(X_train[y_train == 0])
        n_generate = N_maj - N_min

        if len(X_min) < batch_size:
            batch_size = max(1, len(X_min) // 2)

        wgan_gp = WGAN_GP(
            latent_dim=wgan_config.LATENT_DIM,
            data_dim=X_train.shape[1],
            lambda_gp=lambda_gp,
            lr=lr,
            batch_size=batch_size,
            n_critic=n_critic,
            force_cpu=True
        )

        print(f"训练WGAN-GP...")
        wgan_gp.train_with_progress(X_min, epochs=80)

        # 获取训练损失
        losses = wgan_gp.get_training_history()

        X_synthetic = wgan_gp.generate_samples(n_generate)

        # 构建平衡数据集
        X_balanced = np.vstack([X_train, X_synthetic])
        y_balanced = np.concatenate([y_train, np.ones(len(X_synthetic))])

        # 确保返回numpy数组
        X_balanced = np.asarray(X_balanced)
        y_balanced = np.asarray(y_balanced, dtype=int)

        print(f"WGAN-GP生成完成: {len(X_balanced)} 样本, 类别分布: {np.bincount(y_balanced)}")

        return X_balanced, y_balanced, losses

    except Exception as e:
        print(f"WGAN-GP生成失败: {e}")
        return np.asarray(X_train), np.asarray(y_train, dtype=int), {'d_losses': [], 'g_losses': [], 'w_distances': []}

def evaluate_method(X_balanced, y_balanced, X_original, y_original, method_name):
    """使用十折交叉验证评估方法"""
    print(f"\n--- {method_name} 十折交叉验证评估 ---")

    # 确保输入数据是numpy数组并重置索引
    if hasattr(X_balanced, 'values'):  # pandas DataFrame
        X_balanced = X_balanced.values
    if hasattr(y_balanced, 'values'):  # pandas Series
        y_balanced = y_balanced.values

    X_balanced = np.asarray(X_balanced)
    y_balanced = np.asarray(y_balanced, dtype=int)

    # 使用随机森林分类器（默认参数）
    clf = RandomForestClassifier(random_state=42)

    # 十折交叉验证
    cv = StratifiedKFold(n_splits=10, shuffle=True, random_state=42)

    f1_scores = []
    gmean_scores = []

    print(f"数据集信息: {len(X_balanced)} 样本, {X_balanced.shape[1]} 特征")
    print(f"类别分布: {np.bincount(y_balanced)}")

    for fold, (train_idx, test_idx) in enumerate(cv.split(X_balanced, y_balanced)):
        X_train_fold = X_balanced[train_idx]
        y_train_fold = y_balanced[train_idx]
        X_test_fold = X_balanced[test_idx]
        y_test_fold = y_balanced[test_idx]

        # 训练模型
        clf.fit(X_train_fold, y_train_fold)

        # 预测
        y_pred = clf.predict(X_test_fold)

        # 计算指标
        f1 = f1_score(y_test_fold, y_pred, average='weighted')
        gmean = geometric_mean_score(y_test_fold, y_pred)

        f1_scores.append(f1)
        gmean_scores.append(gmean)

    # 计算平均值和标准差
    f1_mean = np.mean(f1_scores)
    f1_std = np.std(f1_scores)
    gmean_mean = np.mean(gmean_scores)
    gmean_std = np.std(gmean_scores)

    print(f"F1-Score: {f1_mean:.4f} ± {f1_std:.4f}")
    print(f"G-mean: {gmean_mean:.4f} ± {gmean_std:.4f}")

    return {
        'method': method_name,
        'f1_mean': f1_mean,
        'f1_std': f1_std,
        'gmean_mean': gmean_mean,
        'gmean_std': gmean_std,
        'f1_scores': f1_scores,
        'gmean_scores': gmean_scores
    }

def plot_training_losses(all_losses, save_path='predictive_training_losses.png'):
    """绘制生成器和判别器的训练损失"""
    plt.figure(figsize=(15, 10))

    methods_with_losses = []
    for method_name, losses in all_losses.items():
        if losses and len(losses.get('d_losses', [])) > 0:
            methods_with_losses.append((method_name, losses))

    if not methods_with_losses:
        print("没有可用的训练损失数据")
        return

    n_methods = len(methods_with_losses)

    for i, (method_name, losses) in enumerate(methods_with_losses):
        # 判别器损失
        plt.subplot(n_methods, 3, i*3 + 1)
        if losses['d_losses']:
            plt.plot(losses['d_losses'], 'b-', linewidth=1.5, label='Discriminator Loss')
            plt.title(f'{method_name} - Discriminator Loss')
            plt.xlabel('Epoch')
            plt.ylabel('Loss')
            plt.grid(True, alpha=0.3)
            plt.legend()

        # 生成器损失
        plt.subplot(n_methods, 3, i*3 + 2)
        if losses['g_losses']:
            plt.plot(losses['g_losses'], 'r-', linewidth=1.5, label='Generator Loss')
            plt.title(f'{method_name} - Generator Loss')
            plt.xlabel('Epoch')
            plt.ylabel('Loss')
            plt.grid(True, alpha=0.3)
            plt.legend()

        # Wasserstein距离
        plt.subplot(n_methods, 3, i*3 + 3)
        if losses['w_distances']:
            plt.plot(losses['w_distances'], 'g-', linewidth=1.5, label='Wasserstein Distance')
            plt.title(f'{method_name} - Wasserstein Distance')
            plt.xlabel('Epoch')
            plt.ylabel('Distance')
            plt.grid(True, alpha=0.3)
            plt.legend()

    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.show()
    print(f"训练损失图已保存: {save_path}")

def plot_results_comparison(results, save_path='predictive_results_comparison.png'):
    """绘制结果对比图"""
    methods = [r['method'] for r in results]
    f1_means = [r['f1_mean'] for r in results]
    f1_stds = [r['f1_std'] for r in results]
    gmean_means = [r['gmean_mean'] for r in results]
    gmean_stds = [r['gmean_std'] for r in results]

    x = np.arange(len(methods))
    width = 0.35

    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

    # F1-Score对比
    bars1 = ax1.bar(x - width/2, f1_means, width, yerr=f1_stds,
                    label='F1-Score', alpha=0.8, capsize=5)
    ax1.set_xlabel('方法')
    ax1.set_ylabel('F1-Score')
    ax1.set_title('F1-Score对比 (十折交叉验证)')
    ax1.set_xticks(x)
    ax1.set_xticklabels(methods, rotation=45, ha='right')
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # 添加数值标签
    for bar, mean, std in zip(bars1, f1_means, f1_stds):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + std + 0.01,
                f'{mean:.3f}±{std:.3f}', ha='center', va='bottom', fontsize=8)

    # G-mean对比
    bars2 = ax2.bar(x - width/2, gmean_means, width, yerr=gmean_stds,
                    label='G-mean', alpha=0.8, capsize=5, color='orange')
    ax2.set_xlabel('方法')
    ax2.set_ylabel('G-mean')
    ax2.set_title('G-mean对比 (十折交叉验证)')
    ax2.set_xticks(x)
    ax2.set_xticklabels(methods, rotation=45, ha='right')
    ax2.legend()
    ax2.grid(True, alpha=0.3)

    # 添加数值标签
    for bar, mean, std in zip(bars2, gmean_means, gmean_stds):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + std + 0.01,
                f'{mean:.3f}±{std:.3f}', ha='center', va='bottom', fontsize=8)

    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.show()
    print(f"结果对比图已保存: {save_path}")

def main():
    """主函数：运行所有实验"""
    print("=" * 80)
    print("Predictive数据集不平衡数据处理方法对比实验")
    print("=" * 80)

    # 1. 加载数据
    X, y, minority_count, majority_count, imbalance_ratio = load_predictive_data()

    # 2. 数据划分
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.3, stratify=y, random_state=42
    )

    print(f"\n训练集: {len(X_train)} 样本")
    print(f"测试集: {len(X_test)} 样本")
    print(f"训练集类别分布: {np.bincount(y_train)}")
    print(f"测试集类别分布: {np.bincount(y_test)}")

    # 3. 定义所有方法
    methods = [
        ("GA优化ADASYN-WGAN-GP", method_ga_adasyn_wgan),
        ("不使用GA优化ADASYN-WGAN-GP", method_no_ga_adasyn_wgan),
        ("GA只优化ADASYN", method_ga_adasyn_only),
        ("GA只优化WGAN-GP", method_ga_wgan_only),
        ("传统ADASYN", method_traditional_adasyn)
    ]

    # 4. 运行所有方法
    all_results = []
    all_losses = {}

    for method_name, method_func in methods:
        print(f"\n{'='*60}")
        print(f"运行方法: {method_name}")
        print(f"{'='*60}")

        try:
            start_time = time.time()
            X_balanced, y_balanced, losses, fitness = method_func(X_train, y_train, X_test, y_test)
            method_time = time.time() - start_time

            print(f"方法执行时间: {method_time:.1f}秒")

            # 保存损失信息
            if losses:
                all_losses[method_name] = losses

            # 评估方法
            result = evaluate_method(X_balanced, y_balanced, X_train, y_train, method_name)
            result['execution_time'] = method_time
            result['fitness'] = fitness
            all_results.append(result)

        except Exception as e:
            print(f"方法 {method_name} 执行失败: {e}")
            import traceback
            traceback.print_exc()

    # 5. 原始数据集评估（作为基准）
    print(f"\n{'='*60}")
    print("原始数据集基准评估")
    print(f"{'='*60}")

    # 确保数据类型正确
    X_train_array = np.asarray(X_train)
    y_train_array = np.asarray(y_train)

    baseline_result = evaluate_method(X_train_array, y_train_array, X_train_array, y_train_array, "原始数据集")
    baseline_result['execution_time'] = 0.0
    baseline_result['fitness'] = 0.0
    all_results.insert(0, baseline_result)  # 插入到开头作为基准

    # 6. 结果汇总
    print(f"\n{'='*80}")
    print("实验结果汇总")
    print(f"{'='*80}")

    print(f"{'方法':<25} {'F1-Score':<15} {'G-mean':<15} {'执行时间(s)':<12} {'适应度':<10}")
    print("-" * 80)

    for result in all_results:
        print(f"{result['method']:<25} "
              f"{result['f1_mean']:.4f}±{result['f1_std']:.3f}  "
              f"{result['gmean_mean']:.4f}±{result['gmean_std']:.3f}  "
              f"{result['execution_time']:<12.1f} "
              f"{result['fitness']:<10.4f}")

    # 7. 生成可视化
    print(f"\n生成可视化图表...")

    # 绘制训练损失
    if all_losses:
        plot_training_losses(all_losses)

    # 绘制结果对比
    plot_results_comparison(all_results)

    # 8. 保存详细结果
    results_df = pd.DataFrame([
        {
            'Method': r['method'],
            'F1_Mean': r['f1_mean'],
            'F1_Std': r['f1_std'],
            'GMean_Mean': r['gmean_mean'],
            'GMean_Std': r['gmean_std'],
            'Execution_Time': r['execution_time'],
            'Fitness': r['fitness']
        }
        for r in all_results
    ])

    results_df.to_csv('predictive_experiment_results.csv', index=False)
    print(f"详细结果已保存: predictive_experiment_results.csv")

    # 9. 最佳方法分析
    best_f1_idx = np.argmax([r['f1_mean'] for r in all_results[1:]])  # 排除基准
    best_gmean_idx = np.argmax([r['gmean_mean'] for r in all_results[1:]])  # 排除基准

    print(f"\n{'='*80}")
    print("最佳方法分析")
    print(f"{'='*80}")
    print(f"F1-Score最佳方法: {all_results[best_f1_idx+1]['method']} "
          f"(F1: {all_results[best_f1_idx+1]['f1_mean']:.4f})")
    print(f"G-mean最佳方法: {all_results[best_gmean_idx+1]['method']} "
          f"(G-mean: {all_results[best_gmean_idx+1]['gmean_mean']:.4f})")

    print(f"\n实验完成！")
    print(f"数据集信息:")
    print(f"  - 总样本数: {len(X)}")
    print(f"  - 特征数: {X.shape[1]}")
    print(f"  - 少数类(HDF=1)数目: {minority_count}")
    print(f"  - 不平衡比例: {imbalance_ratio:.2f}:1")

if __name__ == "__main__":
    # 设置随机种子确保可复现性
    np.random.seed(42)

    # 设置matplotlib中文显示
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False

    # 运行主函数
    main()
