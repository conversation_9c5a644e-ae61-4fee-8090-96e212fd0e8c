"""
测试car.py修复后的evaluate_method函数
"""

import os
import sys
import numpy as np
import pandas as pd
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import LabelEncoder, StandardScaler
import warnings
warnings.filterwarnings('ignore')

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_evaluate_method_fix():
    """测试修复后的evaluate_method函数"""
    print("=" * 60)
    print("测试car.py修复后的evaluate_method函数")
    print("=" * 60)
    
    try:
        from car import load_car_data, evaluate_method
        
        # 加载数据
        X, y, minority_count, majority_count, imbalance_ratio = load_car_data()
        
        print(f"数据加载成功:")
        print(f"  数据形状: {X.shape}")
        print(f"  少数类数目: {minority_count}")
        print(f"  不平衡比例: {imbalance_ratio:.2f}:1")
        
        # 数据划分
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.3, stratify=y, random_state=42
        )
        
        print(f"\n数据划分:")
        print(f"  训练集: {len(X_train)} 样本")
        print(f"  测试集: {len(X_test)} 样本")
        print(f"  训练集类别分布: {np.bincount(y_train)}")
        
        # 测试原始数据集评估
        print(f"\n测试原始数据集评估...")
        result = evaluate_method(X_train, y_train, X_train, y_train, "原始数据集测试")
        
        print(f"✓ evaluate_method函数测试成功!")
        print(f"  F1-Score: {result['f1_mean']:.4f} ± {result['f1_std']:.4f}")
        print(f"  G-mean: {result['gmean_mean']:.4f} ± {result['gmean_std']:.4f}")
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_traditional_adasyn():
    """测试传统ADASYN方法"""
    print("\n" + "=" * 60)
    print("测试传统ADASYN方法")
    print("=" * 60)
    
    try:
        from car import load_car_data, method_traditional_adasyn, evaluate_method
        from sklearn.model_selection import train_test_split
        
        # 加载数据
        X, y, _, _, _ = load_car_data()
        
        # 数据划分
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.3, stratify=y, random_state=42
        )
        
        print(f"运行传统ADASYN方法...")
        
        # 运行传统ADASYN
        X_balanced, y_balanced, losses, fitness = method_traditional_adasyn(
            X_train, y_train, X_test, y_test
        )
        
        print(f"ADASYN处理完成:")
        print(f"  平衡后数据: {len(X_balanced)} 样本")
        print(f"  类别分布: {np.bincount(y_balanced)}")
        print(f"  数据类型: X={type(X_balanced)}, y={type(y_balanced)}")
        
        # 测试评估
        print(f"\n测试ADASYN结果评估...")
        result = evaluate_method(X_balanced, y_balanced, X_train, y_train, "传统ADASYN测试")
        
        print(f"✓ 传统ADASYN方法测试成功!")
        print(f"  F1-Score: {result['f1_mean']:.4f} ± {result['f1_std']:.4f}")
        print(f"  G-mean: {result['gmean_mean']:.4f} ± {result['gmean_std']:.4f}")
        
        return True
        
    except Exception as e:
        print(f"✗ 传统ADASYN测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_types():
    """测试不同数据类型的处理"""
    print("\n" + "=" * 60)
    print("测试不同数据类型的处理")
    print("=" * 60)
    
    try:
        from car import evaluate_method
        
        # 创建测试数据
        np.random.seed(42)
        X_array = np.random.normal(0, 1, (200, 5))
        y_array = np.random.choice([0, 1], 200, p=[0.7, 0.3])
        
        # 转换为pandas格式
        X_df = pd.DataFrame(X_array, columns=[f'feature_{i}' for i in range(5)])
        y_series = pd.Series(y_array, name='target')
        
        print(f"测试数据类型:")
        print(f"  X_array: {type(X_array)}, shape: {X_array.shape}")
        print(f"  y_array: {type(y_array)}, shape: {y_array.shape}")
        print(f"  X_df: {type(X_df)}, shape: {X_df.shape}")
        print(f"  y_series: {type(y_series)}, shape: {y_series.shape}")
        
        # 测试numpy数组
        print(f"\n测试numpy数组...")
        result1 = evaluate_method(X_array, y_array, X_array, y_array, "numpy数组测试")
        print(f"✓ numpy数组测试成功: F1={result1['f1_mean']:.4f}")
        
        # 测试pandas格式
        print(f"\n测试pandas格式...")
        result2 = evaluate_method(X_df, y_series, X_array, y_array, "pandas格式测试")
        print(f"✓ pandas格式测试成功: F1={result2['f1_mean']:.4f}")
        
        # 测试混合格式
        print(f"\n测试混合格式...")
        result3 = evaluate_method(X_df, y_array, X_array, y_array, "混合格式测试")
        print(f"✓ 混合格式测试成功: F1={result3['f1_mean']:.4f}")
        
        return True
        
    except Exception as e:
        print(f"✗ 数据类型测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("Car.py修复验证测试")
    print("=" * 80)
    
    tests = [
        ("evaluate_method函数修复", test_evaluate_method_fix),
        ("传统ADASYN方法", test_traditional_adasyn),
        ("数据类型处理", test_data_types)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n运行测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 测试结果汇总
    print("\n" + "=" * 80)
    print("测试结果汇总")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name:25}: {status}")
        if result:
            passed += 1
    
    print("-" * 80)
    print(f"总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！car.py修复成功")
        print("\n可以重新运行: python car.py")
    else:
        print("⚠️  部分测试失败，需要进一步修复")
    
    return passed == total

if __name__ == "__main__":
    # 设置随机种子
    np.random.seed(42)
    
    # 运行测试
    success = main()
    sys.exit(0 if success else 1)
