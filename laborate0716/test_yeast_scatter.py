"""
测试Yeast数据集散点图功能
"""

import os
import sys
import numpy as np
import pandas as pd
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
from sklearn.preprocessing import LabelEncoder, StandardScaler
from sklearn.decomposition import PCA
from imblearn.over_sampling import ADASYN, SMOTE
import warnings
warnings.filterwarnings('ignore')

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入yeast.py中的函数
from yeast import load_yeast_data, plot_scatter_comparison

def test_yeast_scatter_plot():
    """测试Yeast数据集散点图生成功能"""
    print("=" * 60)
    print("测试Yeast数据集散点图生成功能")
    print("=" * 60)
    
    # 1. 加载数据
    X, y, minority_count, majority_count, imbalance_ratio = load_yeast_data()
    
    # 2. 创建一些示例数据集
    all_datasets = {}
    
    # 传统ADASYN
    try:
        adasyn = ADASYN(random_state=42)
        X_adasyn, y_adasyn = adasyn.fit_resample(X, y)
        all_datasets['传统ADASYN'] = (X_adasyn, y_adasyn)
        print(f"ADASYN生成完成: {len(X_adasyn)} 样本")
    except Exception as e:
        print(f"ADASYN生成失败: {e}")
        all_datasets['传统ADASYN'] = (X, y)
    
    # GA只优化ADASYN (模拟)
    try:
        adasyn2 = ADASYN(n_neighbors=8, random_state=123)
        X_adasyn2, y_adasyn2 = adasyn2.fit_resample(X, y)
        all_datasets['GA只优化ADASYN'] = (X_adasyn2, y_adasyn2)
        print(f"GA只优化ADASYN模拟生成完成: {len(X_adasyn2)} 样本")
    except Exception as e:
        print(f"GA只优化ADASYN生成失败: {e}")
        all_datasets['GA只优化ADASYN'] = (X, y)
    
    # GA只优化WGAN-GP (模拟)
    minority_indices = np.where(y == 1)[0]
    if len(minority_indices) > 0:
        n_generate = np.sum(y == 0) - np.sum(y == 1)
        synthetic_indices = np.random.choice(minority_indices, n_generate, replace=True)
        X_synthetic = X[synthetic_indices] + np.random.normal(0, 0.05, (n_generate, X.shape[1]))
        X_wgan = np.vstack([X, X_synthetic])
        y_wgan = np.concatenate([y, np.ones(n_generate)])
        all_datasets['GA只优化WGAN-GP'] = (X_wgan, y_wgan)
        print(f"WGAN模拟生成完成: {len(X_wgan)} 样本")
    
    # GA优化ADASYN-WGAN-GP (模拟)
    if len(minority_indices) > 0:
        half_generate = n_generate // 2
        synthetic_indices1 = np.random.choice(minority_indices, half_generate, replace=True)
        synthetic_indices2 = np.random.choice(minority_indices, n_generate - half_generate, replace=True)
        
        X_synthetic1 = X[synthetic_indices1] + np.random.normal(0, 0.08, (half_generate, X.shape[1]))
        X_synthetic2 = X[synthetic_indices2] + np.random.normal(0, 0.03, (n_generate - half_generate, X.shape[1]))
        
        X_combined = np.vstack([X, X_synthetic1, X_synthetic2])
        y_combined = np.concatenate([y, np.ones(n_generate)])
        all_datasets['GA优化ADASYN-WGAN-GP'] = (X_combined, y_combined)
        print(f"ADASYN-WGAN模拟生成完成: {len(X_combined)} 样本")
    
    # 不使用GA优化ADASYN-WGAN-GP (模拟为GAN处理不平衡数据)
    if len(minority_indices) > 0:
        synthetic_indices = np.random.choice(minority_indices, n_generate, replace=True)
        X_synthetic = X[synthetic_indices] + np.random.normal(0, 0.1, (n_generate, X.shape[1]))
        X_gan = np.vstack([X, X_synthetic])
        y_gan = np.concatenate([y, np.ones(n_generate)])
        all_datasets['不使用GA优化ADASYN-WGAN-GP'] = (X_gan, y_gan)
        print(f"GAN模拟生成完成: {len(X_gan)} 样本")
    
    # 3. 生成散点图
    print("\n生成散点图...")
    plot_scatter_comparison(all_datasets, X, y, save_path='test_yeast_scatter_comparison.png')
    
    print("\n测试完成！")

if __name__ == "__main__":
    # 设置随机种子确保可复现性
    np.random.seed(42)
    
    # 运行测试
    test_yeast_scatter_plot()
