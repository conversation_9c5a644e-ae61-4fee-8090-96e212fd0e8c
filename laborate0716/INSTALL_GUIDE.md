# 安装和使用指南

## 系统要求

### 硬件要求
- **内存**: 建议16GB以上（最低8GB）
- **存储**: 至少2GB可用空间
- **GPU**: 可选，支持CUDA的GPU可加速WGAN-GP训练
- **CPU**: 多核处理器（建议4核以上）

### 软件要求
- **Python**: 3.7 - 3.10
- **操作系统**: Windows 10/11, macOS 10.14+, Ubuntu 18.04+

## 安装步骤

### 1. 克隆或下载项目

```bash
# 如果使用Git
git clone <repository-url>
cd laborate0716

# 或者直接下载并解压到 laborate0716 文件夹
```

### 2. 创建虚拟环境（推荐）

```bash
# 使用venv
python -m venv venv

# 激活虚拟环境
# Windows
venv\Scripts\activate
# macOS/Linux
source venv/bin/activate
```

### 3. 安装依赖包

```bash
# 升级pip
python -m pip install --upgrade pip

# 安装项目依赖
pip install -r requirements.txt
```

### 4. 验证安装

```bash
# 运行系统测试
python test_system.py
```

如果看到 "🎉 所有测试通过！系统可以正常运行。" 说明安装成功。

## 快速开始

### 1. 运行快速演示

```bash
python quick_demo.py
```

这将使用合成数据集运行一个简化的优化过程，大约需要5-10分钟。

### 2. 运行完整实验

```bash
python main_experiment.py
```

这将在所有配置的数据集上运行完整的遗传算法优化，可能需要几小时。

## 数据集准备

### 支持的数据格式
- CSV文件 (`.csv`)
- 数据文件 (`.data`)

### 数据集放置
将数据集文件放在 `../data/` 目录下：

```
GAAD/
├── laborate0716/          # 项目代码
│   ├── main_experiment.py
│   └── ...
└── data/                  # 数据集目录
    ├── credit.data
    ├── wisconsin.data
    ├── ecoli.data
    └── ...
```

### 数据格式要求
- 最后一列为标签列
- 支持数值型和类别型特征
- 自动处理缺失值
- 自动转换为二分类问题

## 配置说明

### 主要配置文件：`config.py`

#### 遗传算法参数
```python
class GAConfig:
    POPULATION_SIZE = 50      # 种群大小
    MAX_GENERATIONS = 100     # 最大代数
    ELITE_SIZE = 5           # 精英个体数
    CROSSOVER_RATE = 0.8     # 交叉概率
    MUTATION_RATE = 0.1      # 变异概率
```

#### 参数搜索空间
```python
class ParameterRanges:
    # ADASYN参数
    ADASYN_K_MIN = 3         # k邻居最小值
    ADASYN_K_MAX = 20        # k邻居最大值
    ADASYN_ALPHA_MIN = 0.5   # α平衡参数最小值
    ADASYN_ALPHA_MAX = 1.0   # α平衡参数最大值
    
    # WGAN-GP参数
    WGAN_LAMBDA_MIN = 1.0    # 梯度惩罚系数最小值
    WGAN_LAMBDA_MAX = 20.0   # 梯度惩罚系数最大值
    # ... 其他参数
```

#### 实验配置
```python
class ExperimentConfig:
    TRAIN_TEST_SPLIT = 0.7   # 训练测试划分比例
    F1_WEIGHT = 0.6          # F1分数权重
    AUC_WEIGHT = 0.3         # AUC权重
    GMEAN_WEIGHT = 0.1       # G-mean权重
```

## 结果解读

### 输出文件结构
```
results/experiment_YYYYMMDD_HHMMSS/
├── all_experiments_results.json    # 所有实验结果
├── all_experiments_results.pkl     # 完整结果数据
├── experiment_summary.png          # 结果汇总图
├── dataset1/                       # 各数据集详细结果
│   ├── results.json
│   ├── optimization_history.png
│   └── performance_radar.png
└── logs/
    └── ga_optimization.log         # 详细日志
```

### 关键指标说明

#### 适应度分数
综合评估指标，计算公式：
```
适应度 = F1_WEIGHT × F1加权 + AUC_WEIGHT × AUC + GMEAN_WEIGHT × G-mean - 质量惩罚
```

#### 测试集性能
- **F1_weighted**: 加权F1分数
- **F1_minority**: 少数类F1分数
- **AUC_ROC**: ROC曲线下面积
- **Geometric_mean**: 几何平均数

#### 合成质量指标
- **KL_divergence**: KL散度（越小越好，<0.05为优秀）
- **Wasserstein_distance**: Wasserstein距离（越小越好，<0.1为优秀）
- **TSTR_F1**: 仅用合成数据训练的F1分数

## 常见问题

### Q1: 内存不足错误
**解决方案**:
- 减少种群大小：修改 `GAConfig.POPULATION_SIZE`
- 减少WGAN训练轮数：修改 `WGANConfig.EPOCHS`
- 使用较小的批量大小

### Q2: CUDA相关错误
**解决方案**:
- 如果没有GPU，PyTorch会自动使用CPU
- 确保CUDA版本与PyTorch版本兼容
- 可以强制使用CPU：在代码中设置 `device = 'cpu'`

### Q3: 优化过程很慢
**解决方案**:
- 减少最大代数：修改 `GAConfig.MAX_GENERATIONS`
- 启用早停：确保 `EARLY_STOP_PATIENCE` 设置合理
- 使用GPU加速WGAN-GP训练

### Q4: 适应度分数很低
**可能原因**:
- 数据集本身难度较高
- 参数搜索空间需要调整
- 需要更多的优化代数

**解决方案**:
- 检查数据预处理是否正确
- 调整适应度函数权重
- 增加种群大小和代数

### Q5: 数据集加载失败
**解决方案**:
- 检查数据文件路径是否正确
- 确认数据格式符合要求
- 查看日志文件中的详细错误信息

## 性能优化建议

### 1. 计算资源优化
- 使用GPU加速WGAN-GP训练
- 启用多进程并行评估（需要修改代码）
- 使用SSD存储提高I/O性能

### 2. 算法参数调优
- 根据数据集大小调整种群大小
- 使用自适应交叉变异概率
- 实现多目标优化（性能vs效率）

### 3. 内存管理
- 及时释放不需要的变量
- 使用数据生成器减少内存占用
- 监控内存使用情况

## 扩展开发

### 添加新的数据集
1. 将数据文件放入 `../data/` 目录
2. 在 `config.py` 的 `DatasetConfig.DATASETS` 中添加条目
3. 如需特殊处理，修改 `data_utils.py` 中的 `_process_dataset_specific` 方法

### 添加新的评估指标
1. 在 `fitness_function.py` 中添加指标计算函数
2. 修改适应度函数以包含新指标
3. 更新 `config.py` 中的权重配置

### 添加新的优化算法
1. 在 `genetic_algorithm.py` 中实现新的选择/交叉/变异算子
2. 或者创建新的优化器类
3. 修改主实验脚本以使用新算法

## 技术支持

如遇到问题，请：
1. 首先查看日志文件中的详细错误信息
2. 运行 `python test_system.py` 检查系统状态
3. 查看本文档的常见问题部分
4. 提交Issue时请包含：
   - 错误信息和堆栈跟踪
   - 系统环境信息
   - 使用的数据集和配置参数

## 更新日志

### v1.0.0 (2024-07-16)
- 初始版本发布
- 实现遗传算法优化ADASYN+WGAN-GP参数
- 支持多种评估指标和可视化
- 完整的实验框架和文档
