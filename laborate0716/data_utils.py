"""
数据处理工具：数据加载、预处理、划分等功能
"""

import numpy as np
import pandas as pd
from sklearn.model_selection import train_test_split, StratifiedKFold
from sklearn.preprocessing import StandardScaler, LabelEncoder, MinMaxScaler
from sklearn.impute import SimpleImputer
from sklearn.feature_selection import SelectKBest, f_classif
import logging
import os

from config import dataset_config, exp_config

logger = logging.getLogger(__name__)

class DataProcessor:
    """数据处理器"""
    
    def __init__(self):
        self.scaler = None
        self.label_encoder = None
        self.imputer = None
        self.feature_selector = None
        self.feature_names = None
    
    def load_dataset(self, dataset_name: str) -> tuple:
        """
        加载数据集
        
        Args:
            dataset_name: 数据集名称
            
        Returns:
            X, y: 特征和标签
        """
        if dataset_name not in dataset_config.DATASETS:
            raise ValueError(f"不支持的数据集: {dataset_name}")
        
        filepath = dataset_config.DATASETS[dataset_name]
        
        if not os.path.exists(filepath):
            raise FileNotFoundError(f"数据文件不存在: {filepath}")
        
        logger.info(f"加载数据集: {dataset_name} from {filepath}")
        
        # 根据文件扩展名选择加载方法
        if filepath.endswith('.csv'):
            data = pd.read_csv(filepath)
        elif filepath.endswith('.data'):
            # 假设是以空格或逗号分隔的文本文件
            try:
                data = pd.read_csv(filepath, sep=',', header=None)
            except:
                data = pd.read_csv(filepath, sep='\s+', header=None)
        else:
            raise ValueError(f"不支持的文件格式: {filepath}")
        
        # 处理不同数据集的特殊格式
        X, y = self._process_dataset_specific(data, dataset_name)
        
        logger.info(f"数据集加载完成: {X.shape[0]} 样本, {X.shape[1]} 特征")
        logger.info(f"类别分布: {np.bincount(y)}")
        
        return X, y
    
    def _process_dataset_specific(self, data: pd.DataFrame, dataset_name: str) -> tuple:
        """处理特定数据集的格式"""
        
        if dataset_name == 'credit':
            # 信用卡数据集：最后一列是标签
            X = data.iloc[:, :-1].values
            y = data.iloc[:, -1].values
            
        elif dataset_name == 'wisconsin':
            # 威斯康星乳腺癌数据集
            X = data.iloc[:, 1:-1].values  # 去掉ID列
            y = data.iloc[:, -1].values
            
        elif dataset_name == 'ecoli':
            # E.coli数据集
            X = data.iloc[:, 1:-1].values  # 去掉序列名
            y = data.iloc[:, -1].values
            
        elif dataset_name == 'glass':
            # 玻璃识别数据集
            X = data.iloc[:, 1:-1].values  # 去掉ID
            y = data.iloc[:, -1].values
            
        elif dataset_name == 'yeast':
            # 酵母数据集
            X = data.iloc[:, 1:-1].values  # 去掉序列名
            y = data.iloc[:, -1].values
            
        elif dataset_name == 'car':
            # 汽车评估数据集
            X = data.iloc[:, :-1].values
            y = data.iloc[:, -1].values
            
        elif dataset_name == 'statlog':
            # Statlog数据集
            X = data.iloc[:, :-1].values
            y = data.iloc[:, -1].values
            
        else:
            # 默认处理：最后一列是标签
            X = data.iloc[:, :-1].values
            y = data.iloc[:, -1].values
        
        # 转换为二分类问题（如果需要）
        y = self._convert_to_binary(y, dataset_name)
        
        return X, y
    
    def _convert_to_binary(self, y: np.ndarray, dataset_name: str) -> np.ndarray:
        """转换为二分类问题"""
        unique_classes = np.unique(y)

        if len(unique_classes) == 2:
            # 已经是二分类
            if not np.array_equal(unique_classes, [0, 1]):
                # 重新编码为0和1
                le = LabelEncoder()
                y = le.fit_transform(y)
        else:
            # 多分类转二分类：选择最少的类作为正类
            if np.issubdtype(y.dtype, np.number):
                # 数值类型
                try:
                    class_counts = np.bincount(y.astype(int))
                    minority_class = np.argmin(class_counts)
                    y = (y == minority_class).astype(int)
                except:
                    # 如果转换失败，使用pandas
                    class_counts = pd.Series(y).value_counts()
                    minority_class = class_counts.idxmin()
                    y = (y == minority_class).astype(int)
            else:
                # 非数值类型
                class_counts = pd.Series(y).value_counts()
                minority_class = class_counts.idxmin()
                y = (y == minority_class).astype(int)

        return y.astype(int)
    
    def preprocess_data(self, X: np.ndarray, y: np.ndarray, 
                       fit_transform: bool = True) -> tuple:
        """
        数据预处理
        
        Args:
            X: 特征数据
            y: 标签数据
            fit_transform: 是否拟合并转换（训练时为True，测试时为False）
            
        Returns:
            X_processed, y_processed: 预处理后的数据
        """
        logger.info("开始数据预处理...")
        
        # 处理缺失值
        if dataset_config.HANDLE_MISSING_VALUES:
            if fit_transform:
                self.imputer = SimpleImputer(strategy='median')
                X = self.imputer.fit_transform(X)
            else:
                X = self.imputer.transform(X)
        
        # 特征标准化
        if dataset_config.NORMALIZE_FEATURES:
            if fit_transform:
                self.scaler = StandardScaler()
                X = self.scaler.fit_transform(X)
            else:
                X = self.scaler.transform(X)
        
        # 特征选择
        if dataset_config.FEATURE_SELECTION and fit_transform:
            n_features = min(X.shape[1], max(10, X.shape[1] // 2))
            self.feature_selector = SelectKBest(f_classif, k=n_features)
            X = self.feature_selector.fit_transform(X, y)
        elif self.feature_selector is not None:
            X = self.feature_selector.transform(X)
        
        logger.info(f"预处理完成，特征维度: {X.shape[1]}")
        
        return X, y
    
    def split_data(self, X: np.ndarray, y: np.ndarray) -> tuple:
        """
        数据划分：训练集、验证集、测试集
        
        Returns:
            X_train_sub, X_val, X_test, y_train_sub, y_val, y_test
        """
        logger.info("开始数据划分...")
        
        # 首先划分训练集和测试集
        X_train_full, X_test, y_train_full, y_test = train_test_split(
            X, y, 
            test_size=1-exp_config.TRAIN_TEST_SPLIT,
            stratify=y,
            random_state=42
        )
        
        # 在训练集中再划分训练子集和验证集
        X_train_sub, X_val, y_train_sub, y_val = train_test_split(
            X_train_full, y_train_full,
            test_size=1-exp_config.TRAIN_VAL_SPLIT,
            stratify=y_train_full,
            random_state=42
        )
        
        logger.info(f"数据划分完成:")
        logger.info(f"  训练子集: {X_train_sub.shape[0]} 样本")
        logger.info(f"  验证集: {X_val.shape[0]} 样本")
        logger.info(f"  测试集: {X_test.shape[0]} 样本")
        
        # 打印类别分布
        logger.info(f"训练子集类别分布: {np.bincount(y_train_sub.astype(int))}")
        logger.info(f"验证集类别分布: {np.bincount(y_val.astype(int))}")
        logger.info(f"测试集类别分布: {np.bincount(y_test.astype(int))}")
        
        return X_train_sub, X_val, X_test, y_train_sub, y_val, y_test
    
    def get_class_distribution(self, y: np.ndarray) -> dict:
        """获取类别分布信息"""
        unique, counts = np.unique(y, return_counts=True)
        total = len(y)
        
        distribution = {}
        for cls, count in zip(unique, counts):
            distribution[f'class_{cls}'] = {
                'count': count,
                'percentage': count / total * 100
            }
        
        # 计算不平衡比率
        if len(unique) == 2:
            majority_count = max(counts)
            minority_count = min(counts)
            distribution['imbalance_ratio'] = majority_count / minority_count
            distribution['minority_class'] = unique[np.argmin(counts)]
            distribution['majority_class'] = unique[np.argmax(counts)]
        
        return distribution

def load_and_preprocess_data(dataset_name: str) -> tuple:
    """
    便捷函数：加载并预处理数据
    
    Args:
        dataset_name: 数据集名称
        
    Returns:
        完整的数据划分结果和处理器
    """
    processor = DataProcessor()
    
    # 加载数据
    X, y = processor.load_dataset(dataset_name)
    
    # 预处理
    X, y = processor.preprocess_data(X, y, fit_transform=True)
    
    # 数据划分
    X_train_sub, X_val, X_test, y_train_sub, y_val, y_test = processor.split_data(X, y)
    
    # 打印数据集信息
    distribution = processor.get_class_distribution(y)
    logger.info(f"数据集 {dataset_name} 信息:")
    for key, value in distribution.items():
        logger.info(f"  {key}: {value}")
    
    return (X_train_sub, X_val, X_test, y_train_sub, y_val, y_test), processor

def create_synthetic_imbalanced_dataset(n_samples: int = 1000, 
                                      n_features: int = 20,
                                      imbalance_ratio: float = 0.1,
                                      random_state: int = 42) -> tuple:
    """
    创建合成的不平衡数据集用于测试
    
    Args:
        n_samples: 总样本数
        n_features: 特征数
        imbalance_ratio: 少数类比例
        random_state: 随机种子
        
    Returns:
        X, y: 特征和标签
    """
    np.random.seed(random_state)
    
    n_minority = int(n_samples * imbalance_ratio)
    n_majority = n_samples - n_minority
    
    # 生成多数类样本
    X_majority = np.random.normal(0, 1, (n_majority, n_features))
    y_majority = np.zeros(n_majority)
    
    # 生成少数类样本（稍微不同的分布）
    X_minority = np.random.normal(1, 1, (n_minority, n_features))
    y_minority = np.ones(n_minority)
    
    # 合并
    X = np.vstack([X_majority, X_minority])
    y = np.concatenate([y_majority, y_minority])
    
    # 打乱顺序
    indices = np.random.permutation(len(X))
    X = X[indices]
    y = y[indices]
    
    logger.info(f"创建合成数据集: {n_samples} 样本, {n_features} 特征")
    logger.info(f"类别分布: 多数类 {n_majority}, 少数类 {n_minority}")
    logger.info(f"不平衡比率: {1/imbalance_ratio:.1f}:1")
    
    return X, y
