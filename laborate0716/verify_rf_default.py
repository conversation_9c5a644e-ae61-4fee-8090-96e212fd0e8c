"""
验证随机森林分类器使用默认参数进行十折交叉验证
"""

import numpy as np
import pandas as pd
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import StratifiedKFold, cross_val_score
from sklearn.metrics import f1_score, make_scorer
from sklearn.preprocessing import LabelEncoder, StandardScaler
from sklearn.model_selection import train_test_split
import warnings
warnings.filterwarnings('ignore')

def geometric_mean_score(y_true, y_pred):
    """计算G-mean (几何平均数)"""
    from sklearn.metrics import confusion_matrix
    cm = confusion_matrix(y_true, y_pred)
    if cm.shape == (2, 2):
        tn, fp, fn, tp = cm.ravel()
        sensitivity = tp / (tp + fn) if (tp + fn) > 0 else 0
        specificity = tn / (tn + fp) if (tn + fp) > 0 else 0
        return np.sqrt(sensitivity * specificity)
    return 0

def load_car_data():
    """加载car数据集"""
    print("加载Car数据集...")
    
    # 数据路径
    data_path = "C:/Users/<USER>/Desktop/GAAD/data/car.data"
    
    # 列名定义
    columns = ['buying', 'maint', 'doors', 'persons', 'lug_boot', 'safety', 'class']
    
    try:
        data = pd.read_csv(data_path, names=columns)
        print(f"✓ 数据加载成功，形状: {data.shape}")
    except FileNotFoundError:
        print(f"数据文件未找到，创建模拟数据...")
        # 创建模拟数据
        np.random.seed(42)
        n_samples = 1728
        data = pd.DataFrame({
            'buying': np.random.choice(['vhigh', 'high', 'med', 'low'], n_samples),
            'maint': np.random.choice(['vhigh', 'high', 'med', 'low'], n_samples),
            'doors': np.random.choice(['2', '3', '4', '5more'], n_samples),
            'persons': np.random.choice(['2', '4', 'more'], n_samples),
            'lug_boot': np.random.choice(['small', 'med', 'big'], n_samples),
            'safety': np.random.choice(['low', 'med', 'high'], n_samples),
            'class': np.random.choice(['unacc', 'acc', 'good', 'vgood'], n_samples, 
                                    p=[0.7, 0.22, 0.065, 0.015])
        })
    
    print("原始类别分布:")
    print(data['class'].value_counts())
    
    # 处理特征编码
    X_encoded = data.drop('class', axis=1).copy()
    for col in X_encoded.columns:
        le = LabelEncoder()
        X_encoded[col] = le.fit_transform(X_encoded[col])
    
    # 处理标签：vgood作为少数类(1)，其他合并为多数类(0)
    y = (data['class'] == 'vgood').astype(int)
    
    # 标准化特征
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X_encoded)
    
    # 统计信息
    minority_count = np.sum(y == 1)
    majority_count = np.sum(y == 0)
    imbalance_ratio = majority_count / minority_count if minority_count > 0 else float('inf')
    
    print(f"\n数据集统计:")
    print(f"总样本数: {len(y)}")
    print(f"少数类(vgood)数目: {minority_count}")
    print(f"多数类数目: {majority_count}")
    print(f"不平衡比例: {imbalance_ratio:.2f}:1")
    
    return X_scaled, y

def verify_rf_default_params():
    """验证随机森林默认参数"""
    print("\n" + "="*60)
    print("验证随机森林分类器默认参数")
    print("="*60)
    
    # 创建随机森林分类器（只设置random_state）
    rf_default = RandomForestClassifier(random_state=42)
    
    print("随机森林分类器默认参数:")
    print(f"  n_estimators: {rf_default.n_estimators}")
    print(f"  criterion: {rf_default.criterion}")
    print(f"  max_depth: {rf_default.max_depth}")
    print(f"  min_samples_split: {rf_default.min_samples_split}")
    print(f"  min_samples_leaf: {rf_default.min_samples_leaf}")
    print(f"  max_features: {rf_default.max_features}")
    print(f"  bootstrap: {rf_default.bootstrap}")
    print(f"  random_state: {rf_default.random_state}")
    
    return rf_default

def perform_10fold_cv(X, y, method_name="原始数据"):
    """执行十折交叉验证"""
    print(f"\n--- {method_name} 十折交叉验证 ---")
    
    # 创建随机森林分类器（默认参数）
    rf = RandomForestClassifier(random_state=42)
    
    # 十折交叉验证
    cv = StratifiedKFold(n_splits=10, shuffle=True, random_state=42)
    
    print(f"数据集信息:")
    print(f"  样本数: {len(X)}")
    print(f"  特征数: {X.shape[1]}")
    print(f"  类别分布: {np.bincount(y)}")
    
    # 存储每折的结果
    f1_scores = []
    gmean_scores = []
    
    print(f"\n执行十折交叉验证:")
    print(f"{'折数':<4} {'F1-Score':<10} {'G-mean':<10} {'训练样本':<8} {'测试样本':<8}")
    print("-" * 50)
    
    for fold, (train_idx, test_idx) in enumerate(cv.split(X, y)):
        X_train_fold = X[train_idx]
        y_train_fold = y[train_idx]
        X_test_fold = X[test_idx]
        y_test_fold = y[test_idx]
        
        # 训练模型
        rf.fit(X_train_fold, y_train_fold)
        
        # 预测
        y_pred = rf.predict(X_test_fold)
        
        # 计算指标
        f1 = f1_score(y_test_fold, y_pred, average='weighted')
        gmean = geometric_mean_score(y_test_fold, y_pred)
        
        f1_scores.append(f1)
        gmean_scores.append(gmean)
        
        print(f"{fold+1:<4} {f1:<10.4f} {gmean:<10.4f} {len(X_train_fold):<8} {len(X_test_fold):<8}")
    
    # 计算统计结果
    f1_mean = np.mean(f1_scores)
    f1_std = np.std(f1_scores)
    gmean_mean = np.mean(gmean_scores)
    gmean_std = np.std(gmean_scores)
    
    print("-" * 50)
    print(f"平均结果:")
    print(f"  F1-Score: {f1_mean:.4f} ± {f1_std:.4f}")
    print(f"  G-mean: {gmean_mean:.4f} ± {gmean_std:.4f}")
    
    return {
        'method': method_name,
        'f1_mean': f1_mean,
        'f1_std': f1_std,
        'gmean_mean': gmean_mean,
        'gmean_std': gmean_std,
        'f1_scores': f1_scores,
        'gmean_scores': gmean_scores
    }

def test_different_datasets():
    """测试不同的数据集处理方法"""
    print("\n" + "="*80)
    print("测试不同数据集处理方法的十折交叉验证")
    print("="*80)
    
    # 加载原始数据
    X, y = load_car_data()
    
    # 1. 原始数据集
    result_original = perform_10fold_cv(X, y, "原始不平衡数据集")
    
    # 2. 简单的SMOTE处理（作为对比）
    try:
        from imblearn.over_sampling import SMOTE
        smote = SMOTE(random_state=42)
        X_smote, y_smote = smote.fit_resample(X, y)
        result_smote = perform_10fold_cv(X_smote, y_smote, "SMOTE处理后数据集")
    except Exception as e:
        print(f"SMOTE处理失败: {e}")
        result_smote = None
    
    # 3. 简单的ADASYN处理（作为对比）
    try:
        from imblearn.over_sampling import ADASYN
        adasyn = ADASYN(random_state=42)
        X_adasyn, y_adasyn = adasyn.fit_resample(X, y)
        result_adasyn = perform_10fold_cv(X_adasyn, y_adasyn, "ADASYN处理后数据集")
    except Exception as e:
        print(f"ADASYN处理失败: {e}")
        result_adasyn = None
    
    # 汇总结果
    results = [result_original]
    if result_smote:
        results.append(result_smote)
    if result_adasyn:
        results.append(result_adasyn)
    
    # 显示对比结果
    print(f"\n" + "="*80)
    print("十折交叉验证结果对比")
    print("="*80)
    print(f"{'方法':<20} {'F1-Score':<15} {'G-mean':<15}")
    print("-" * 50)
    
    for result in results:
        print(f"{result['method']:<20} "
              f"{result['f1_mean']:.4f}±{result['f1_std']:.3f}  "
              f"{result['gmean_mean']:.4f}±{result['gmean_std']:.3f}")
    
    return results

def main():
    """主函数"""
    print("="*80)
    print("随机森林分类器默认参数十折交叉验证验证脚本")
    print("="*80)
    
    # 1. 验证随机森林默认参数
    rf_default = verify_rf_default_params()
    
    # 2. 测试不同数据集
    results = test_different_datasets()
    
    # 3. 保存结果
    results_df = pd.DataFrame([
        {
            'Method': r['method'],
            'F1_Mean': r['f1_mean'],
            'F1_Std': r['f1_std'],
            'GMean_Mean': r['gmean_mean'],
            'GMean_Std': r['gmean_std']
        }
        for r in results
    ])
    
    results_df.to_csv('rf_default_cv_results.csv', index=False)
    print(f"\n✓ 结果已保存到: rf_default_cv_results.csv")
    
    print(f"\n" + "="*80)
    print("验证完成！")
    print("确认使用随机森林分类器默认参数进行十折交叉验证")
    print("="*80)

if __name__ == "__main__":
    np.random.seed(42)
    main()
