"""
测试Wisconsin数据集处理脚本
"""

import os
import sys
import numpy as np
import pandas as pd
import warnings
warnings.filterwarnings('ignore')

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_wisconsin_data_loading():
    """测试Wisconsin数据加载"""
    print("=" * 60)
    print("测试Wisconsin数据集加载")
    print("=" * 60)
    
    try:
        from wisconsin import load_wisconsin_data
        
        X, y, minority_count, majority_count, imbalance_ratio = load_wisconsin_data()
        
        print(f"✓ 数据加载成功")
        print(f"  数据形状: {X.shape}")
        print(f"  标签形状: {y.shape}")
        print(f"  少数类(恶性)数目: {minority_count}")
        print(f"  多数类(良性)数目: {majority_count}")
        print(f"  不平衡比例: {imbalance_ratio:.2f}:1")
        print(f"  类别分布: {np.bincount(y)}")
        
        return True, (X, y)
        
    except Exception as e:
        print(f"✗ 数据加载失败: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def test_data_characteristics():
    """测试数据特征"""
    print("\n" + "=" * 60)
    print("测试Wisconsin数据集特征")
    print("=" * 60)
    
    try:
        # 尝试读取真实的wisconsin.data文件
        data_path = "C:/Users/<USER>/Desktop/GAAD/data/wisconsin.data"
        
        try:
            # Wisconsin乳腺癌数据集的列名
            columns = [
                'id', 'clump_thickness', 'uniformity_cell_size', 'uniformity_cell_shape',
                'marginal_adhesion', 'single_epithelial_size', 'bare_nuclei', 'bland_chromatin',
                'normal_nucleoli', 'mitoses', 'class'
            ]
            
            data = pd.read_csv(data_path, names=columns)
            print(f"✓ 真实数据文件读取成功")
            print(f"  数据形状: {data.shape}")
            print(f"  列名: {list(data.columns)}")
            
            # 分析目标变量
            class_dist = data['class'].value_counts().sort_index()
            print(f"\n  原始类别分布:")
            for class_val, count in class_dist.items():
                class_name = "良性" if class_val == 2 else "恶性" if class_val == 4 else f"未知({class_val})"
                print(f"    类别 {class_val} ({class_name}): {count} 样本")
            
            # 重新分类后的分布
            malignant_count = (data['class'] == 4).sum()
            benign_count = (data['class'] == 2).sum()
            
            print(f"\n  重新分类后:")
            print(f"    少数类(恶性): {malignant_count} 样本")
            print(f"    多数类(良性): {benign_count} 样本")
            if malignant_count > 0:
                print(f"    不平衡比例: {benign_count/malignant_count:.2f}:1")
            
            # 检查缺失值
            missing_info = []
            for col in data.columns:
                if col != 'id':
                    missing_count = (data[col] == '?').sum() if data[col].dtype == 'object' else data[col].isnull().sum()
                    if missing_count > 0:
                        missing_info.append(f"    {col}: {missing_count} 个缺失值")
            
            if missing_info:
                print(f"\n  缺失值信息:")
                for info in missing_info:
                    print(info)
            else:
                print(f"\n  ✓ 无缺失值")
            
            # 特征范围分析
            print(f"\n  特征范围分析:")
            numeric_cols = ['clump_thickness', 'uniformity_cell_size', 'uniformity_cell_shape',
                          'marginal_adhesion', 'single_epithelial_size', 'bland_chromatin',
                          'normal_nucleoli', 'mitoses']
            
            for col in numeric_cols[:3]:  # 只显示前3个特征
                if col in data.columns:
                    try:
                        # 处理可能的'?'值
                        col_data = data[col][data[col] != '?'] if data[col].dtype == 'object' else data[col]
                        col_data = pd.to_numeric(col_data, errors='coerce').dropna()
                        if len(col_data) > 0:
                            print(f"    {col}: 范围 [{col_data.min()}, {col_data.max()}], 均值 {col_data.mean():.2f}")
                    except:
                        pass
                
        except Exception as e:
            print(f"⚠️  无法读取真实数据文件: {e}")
            print(f"  将使用模拟数据")
        
        return True
        
    except Exception as e:
        print(f"✗ 数据特征测试失败: {e}")
        return False

def test_traditional_adasyn():
    """测试传统ADASYN方法"""
    print("\n" + "=" * 60)
    print("测试传统ADASYN方法")
    print("=" * 60)
    
    try:
        from wisconsin import load_wisconsin_data, method_traditional_adasyn
        from sklearn.model_selection import train_test_split
        
        # 加载数据
        X, y, _, _, _ = load_wisconsin_data()
        
        # 数据划分
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.3, stratify=y, random_state=42
        )
        
        print(f"训练集: {len(X_train)} 样本 (少数类: {np.sum(y_train == 1)})")
        
        # 运行传统ADASYN
        X_balanced, y_balanced, losses, fitness = method_traditional_adasyn(
            X_train, y_train, X_test, y_test
        )
        
        print(f"✓ 传统ADASYN测试成功")
        print(f"  平衡后数据: {len(X_balanced)} 样本")
        print(f"  类别分布: {np.bincount(y_balanced)}")
        
        return True
        
    except Exception as e:
        print(f"✗ 传统ADASYN测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_evaluation():
    """测试评估函数"""
    print("\n" + "=" * 60)
    print("测试十折交叉验证评估")
    print("=" * 60)
    
    try:
        from wisconsin import load_wisconsin_data, evaluate_method
        
        # 加载数据
        X, y, _, _, _ = load_wisconsin_data()
        
        # 测试原始数据集评估
        result = evaluate_method(X, y, X, y, "原始Wisconsin数据集")
        
        print(f"✓ 评估测试成功")
        print(f"  F1-Score: {result['f1_mean']:.4f} ± {result['f1_std']:.4f}")
        print(f"  G-mean: {result['gmean_mean']:.4f} ± {result['gmean_std']:.4f}")
        
        return True
        
    except Exception as e:
        print(f"✗ 评估测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ga_adasyn_only():
    """测试GA只优化ADASYN参数"""
    print("\n" + "=" * 60)
    print("测试GA只优化ADASYN参数")
    print("=" * 60)
    
    try:
        from wisconsin import load_wisconsin_data, method_ga_adasyn_only
        from sklearn.model_selection import train_test_split
        
        # 加载数据
        X, y, _, _, _ = load_wisconsin_data()
        
        # 数据划分
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.3, stratify=y, random_state=42
        )
        
        print(f"开始GA优化ADASYN参数测试...")
        
        # 运行GA优化ADASYN（使用很小的参数进行快速测试）
        X_balanced, y_balanced, losses, fitness = method_ga_adasyn_only(
            X_train, y_train, X_test, y_test
        )
        
        print(f"✓ GA优化ADASYN测试成功")
        print(f"  最佳适应度: {fitness:.4f}")
        print(f"  平衡后数据: {len(X_balanced)} 样本")
        print(f"  类别分布: {np.bincount(y_balanced)}")
        
        return True
        
    except Exception as e:
        print(f"✗ GA优化ADASYN测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_preprocessing():
    """测试数据预处理功能"""
    print("\n" + "=" * 60)
    print("测试数据预处理功能")
    print("=" * 60)
    
    try:
        # 测试缺失值处理
        data_path = "C:/Users/<USER>/Desktop/GAAD/data/wisconsin.data"
        columns = [
            'id', 'clump_thickness', 'uniformity_cell_size', 'uniformity_cell_shape',
            'marginal_adhesion', 'single_epithelial_size', 'bare_nuclei', 'bland_chromatin',
            'normal_nucleoli', 'mitoses', 'class'
        ]
        
        data = pd.read_csv(data_path, names=columns)
        print(f"原始数据形状: {data.shape}")
        
        # 检查bare_nuclei列的缺失值（通常用'?'表示）
        if 'bare_nuclei' in data.columns:
            missing_count = (data['bare_nuclei'] == '?').sum()
            print(f"bare_nuclei列缺失值('?'): {missing_count} 个")
            
            if missing_count > 0:
                print(f"缺失值处理策略: 用最常见值替换")
                mode_value = data['bare_nuclei'][data['bare_nuclei'] != '?'].mode()
                if len(mode_value) > 0:
                    print(f"最常见值: {mode_value[0]}")
        
        # 检查特征范围
        print(f"\n特征值范围检查:")
        feature_cols = [col for col in data.columns if col not in ['id', 'class']]
        
        for col in feature_cols[:3]:  # 只检查前3个特征
            try:
                col_data = data[col][data[col] != '?'] if data[col].dtype == 'object' else data[col]
                col_data = pd.to_numeric(col_data, errors='coerce').dropna()
                if len(col_data) > 0:
                    print(f"  {col}: [{col_data.min()}, {col_data.max()}]")
            except:
                pass
        
        print(f"✓ 数据预处理测试成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 数据预处理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("Wisconsin数据集处理脚本测试")
    print("=" * 80)
    
    tests = [
        ("数据加载", test_wisconsin_data_loading),
        ("数据特征分析", test_data_characteristics),
        ("数据预处理", test_data_preprocessing),
        ("传统ADASYN", test_traditional_adasyn),
        ("十折交叉验证评估", test_evaluation),
        ("GA优化ADASYN", test_ga_adasyn_only)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n运行测试: {test_name}")
        try:
            if test_name == "数据加载":
                result, _ = test_func()
            else:
                result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 测试结果汇总
    print("\n" + "=" * 80)
    print("测试结果汇总")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name:20}: {status}")
        if result:
            passed += 1
    
    print("-" * 80)
    print(f"总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！可以运行完整的wisconsin.py实验")
        print("\n运行完整实验: python wisconsin.py")
        print("\n数据集说明:")
        print("  - 少数类: 恶性肿瘤(原值4 -> 新值1)")
        print("  - 多数类: 良性肿瘤(原值2 -> 新值0)")
        print("  - 这是威斯康星乳腺癌数据集，用于癌症诊断")
        print("  - 特征包括细胞厚度、细胞大小均匀性、细胞形状均匀性等")
    else:
        print("⚠️  部分测试失败，请检查错误信息")
    
    return passed == total

if __name__ == "__main__":
    # 设置随机种子
    np.random.seed(42)
    
    # 运行测试
    success = main()
    sys.exit(0 if success else 1)
