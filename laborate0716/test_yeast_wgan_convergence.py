"""
测试Yeast数据集WGAN-GP收敛训练损失函数绘制功能
专门针对GA优化ADASYN-WGAN方法训练直到G和D损失都逐渐平稳，达到对抗平衡
"""

import os
import sys
import numpy as np
import pandas as pd
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from imblearn.over_sampling import ADASYN
import warnings
warnings.filterwarnings('ignore')

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from wgan_gp import WGAN_GP
from config import wgan_config

def create_simulated_yeast_data():
    """创建模拟的yeast数据集"""
    print("创建模拟yeast数据集...")
    
    np.random.seed(42)
    n_samples = 1484  # 接近真实yeast数据集大小
    n_features = 8    # 8个特征
    
    # 生成多数类数据 (约90%)
    n_majority = int(n_samples * 0.9)
    X_majority = np.random.randn(n_majority, n_features)
    y_majority = np.zeros(n_majority)
    
    # 生成少数类数据 (约10%)
    n_minority = n_samples - n_majority
    X_minority = np.random.randn(n_minority, n_features) + 2  # 稍微偏移
    y_minority = np.ones(n_minority)
    
    # 合并数据
    X = np.vstack([X_majority, X_minority])
    y = np.concatenate([y_majority, y_minority])
    
    # 标准化
    scaler = StandardScaler()
    X = scaler.fit_transform(X)
    
    print(f"模拟数据集创建完成:")
    print(f"  总样本数: {len(y)}")
    print(f"  少数类: {np.sum(y == 1)} 样本")
    print(f"  多数类: {np.sum(y == 0)} 样本")
    print(f"  不平衡比例: {np.sum(y == 0) / np.sum(y == 1):.2f}:1")
    
    return X, y

def plot_wgan_convergence_losses(losses, save_path='yeast_wgan_convergence_losses.png'):
    """
    绘制WGAN-GP训练损失函数图直到收敛
    展示G和D损失都逐渐平稳，达到对抗平衡的过程
    """
    print(f"\n📊 绘制WGAN-GP收敛训练损失函数图...")
    
    if not losses or 'd_losses' not in losses or 'g_losses' not in losses:
        print("❌ 没有找到损失函数数据")
        return
    
    d_losses = losses['d_losses']
    g_losses = losses['g_losses']
    
    if len(d_losses) == 0 or len(g_losses) == 0:
        print("❌ 损失函数数据为空")
        return
    
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 创建图形
    fig, ax = plt.subplots(1, 1, figsize=(14, 8))
    
    # 训练次数
    epochs = range(1, len(d_losses) + 1)
    
    # 绘制损失函数曲线，参照用户图片的颜色
    ax.plot(epochs, d_losses, color='#1f77b4', linewidth=1.5, label='D损失值', alpha=0.8)
    ax.plot(epochs, g_losses, color='#ff7f0e', linewidth=1.5, label='G损失值', alpha=0.8)
    
    # 设置坐标轴
    ax.set_xlabel('训练次数', fontsize=14, fontweight='bold')
    ax.set_ylabel('损失函数值', fontsize=14, fontweight='bold')
    ax.set_title('Yeast数据集上GA优化ADASYN-WGAN生成器和判别器训练损失函数\n(训练直到G和D损失都逐渐平稳，达到对抗平衡)', 
                fontsize=16, fontweight='bold', pad=20)
    
    # 设置网格
    ax.grid(True, alpha=0.3, linestyle='-', linewidth=0.5)
    
    # 设置图例
    ax.legend(fontsize=12, loc='upper right', frameon=True, fancybox=True, shadow=True)
    
    # 设置坐标轴范围
    ax.set_xlim(0, len(d_losses))
    
    # 动态设置y轴范围
    all_losses = d_losses + g_losses
    y_min = min(all_losses)
    y_max = max(all_losses)
    y_range = y_max - y_min
    ax.set_ylim(y_min - y_range * 0.1, y_max + y_range * 0.1)
    
    # 设置坐标轴刻度
    ax.tick_params(axis='both', which='major', labelsize=12)
    
    # 分析收敛性
    convergence_point = analyze_convergence(d_losses, g_losses)
    
    # 添加收敛点标记
    if convergence_point > 0:
        ax.axvline(x=convergence_point, color='red', linestyle='--', alpha=0.7, linewidth=2)
        ax.text(convergence_point + len(d_losses) * 0.02, (y_min + y_max) / 2, 
                f'收敛点\n第{convergence_point}轮', fontsize=10, color='red', fontweight='bold',
                bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.7))
    
    # 添加统计信息文本框
    stats_text = f'训练轮数: {len(d_losses)}\n'
    stats_text += f'D损失值范围: [{min(d_losses):.4f}, {max(d_losses):.4f}]\n'
    stats_text += f'G损失值范围: [{min(g_losses):.4f}, {max(g_losses):.4f}]\n'
    stats_text += f'最终D损失值: {d_losses[-1]:.4f}\n'
    stats_text += f'最终G损失值: {g_losses[-1]:.4f}\n'
    if convergence_point > 0:
        stats_text += f'收敛轮数: {convergence_point}\n'
        stats_text += f'收敛状态: 对抗平衡已达到'
    else:
        stats_text += f'收敛状态: 仍在训练中'
    
    ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, fontsize=10,
            verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
    
    # 保存图片
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
    plt.close()
    
    print(f"✅ WGAN-GP收敛训练损失函数图已保存: {save_path}")
    
    # 打印详细的收敛分析
    print_convergence_analysis(d_losses, g_losses, convergence_point)

def analyze_convergence(d_losses, g_losses, window=50, threshold=0.01):
    """分析损失函数的收敛点"""
    if len(d_losses) < window * 2:
        return -1
    
    for i in range(window, len(d_losses) - window):
        # 计算前后窗口的方差
        d_var_before = np.var(d_losses[i-window:i])
        d_var_after = np.var(d_losses[i:i+window])
        g_var_before = np.var(g_losses[i-window:i])
        g_var_after = np.var(g_losses[i:i+window])
        
        # 如果前后窗口的方差都很小，认为已收敛
        if (d_var_before < threshold and d_var_after < threshold and 
            g_var_before < threshold and g_var_after < threshold):
            return i
    
    return -1

def print_convergence_analysis(d_losses, g_losses, convergence_point):
    """打印收敛分析结果"""
    print(f"\n📈 训练收敛分析:")
    print(f"  训练轮数: {len(d_losses)}")
    
    if convergence_point > 0:
        print(f"  🎯 收敛点: 第{convergence_point}轮")
        print(f"  📊 收敛前D损失方差: {np.var(d_losses[:convergence_point]):.6f}")
        print(f"  📊 收敛后D损失方差: {np.var(d_losses[convergence_point:]):.6f}")
        print(f"  📊 收敛前G损失方差: {np.var(g_losses[:convergence_point]):.6f}")
        print(f"  📊 收敛后G损失方差: {np.var(g_losses[convergence_point:]):.6f}")
        print(f"  ✅ 对抗平衡状态: 已达到")
        print(f"  🎉 生成器能生成让判别器难区分的'逼真'数据")
    else:
        print(f"  ⚠️ 收敛状态: 未完全收敛")
        print(f"  📊 最后100轮D损失方差: {np.var(d_losses[-100:]):.6f}")
        print(f"  📊 最后100轮G损失方差: {np.var(g_losses[-100:]):.6f}")
    
    print(f"  📈 判别器损失值:")
    print(f"    最小值: {min(d_losses):.6f}")
    print(f"    最大值: {max(d_losses):.6f}")
    print(f"    最终值: {d_losses[-1]:.6f}")
    print(f"    平均值: {np.mean(d_losses):.6f}")
    print(f"  📈 生成器损失值:")
    print(f"    最小值: {min(g_losses):.6f}")
    print(f"    最大值: {max(g_losses):.6f}")
    print(f"    最终值: {g_losses[-1]:.6f}")
    print(f"    平均值: {np.mean(g_losses):.6f}")

def test_yeast_wgan_convergence():
    """测试Yeast数据集WGAN-GP收敛训练"""
    print("=" * 80)
    print("Yeast数据集GA优化ADASYN-WGAN收敛训练损失函数测试")
    print("=" * 80)
    
    # 设置随机种子
    np.random.seed(42)
    
    # 1. 加载数据（使用模拟数据）
    X, y = create_simulated_yeast_data()
    
    # 2. 数据划分
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.3, stratify=y, random_state=42
    )
    
    print(f"\n训练集: {len(X_train)} 样本")
    print(f"训练集类别分布: {np.bincount(y_train.astype(int))}")
    
    # 3. 使用GA优化的最优参数（模拟）
    print(f"\n使用GA优化的最优参数:")
    k = 7
    alpha = 0.85
    lambda_gp = 5.0
    n_critic = 3
    lr = 2e-04
    batch_size = 16
    
    print(f"  ADASYN k邻居: {k}")
    print(f"  ADASYN α平衡: {alpha:.4f}")
    print(f"  WGAN λ梯度惩罚: {lambda_gp:.4f}")
    print(f"  WGAN 判别器训练次数: {n_critic}")
    print(f"  WGAN 学习率: {lr:.2e}")
    print(f"  WGAN 批量大小: {batch_size}")
    
    # 4. 生成平衡数据集并训练WGAN-GP直到收敛
    print(f"\n开始训练GA优化ADASYN-WGAN直到收敛...")
    
    try:
        # 提取少数类和多数类
        X_min = X_train[y_train == 1]
        X_maj = X_train[y_train == 0]
        N_min = len(X_min)
        N_maj = len(X_maj)
        G_total = N_maj - N_min
        
        print(f"原始数据: 多数类{N_maj}, 少数类{N_min}")
        
        # ADASYN生成
        G_adasyn = int(alpha * G_total)
        X_synthetic = np.zeros((0, X_train.shape[1]))
        
        if G_adasyn > 0:
            adasyn = ADASYN(
                sampling_strategy={1: N_min + G_adasyn},
                n_neighbors=min(k, N_min-1) if N_min > 1 else 1,
                random_state=42
            )
            X_adasyn_res, y_adasyn_res = adasyn.fit_resample(X_train, y_train)
            X_adasyn_only = X_adasyn_res[len(X_train):]
            X_synthetic = X_adasyn_only
            print(f"ADASYN生成: {len(X_adasyn_only)} 个样本")
        
        # WGAN-GP生成
        G_wgan = G_total - G_adasyn
        
        if G_wgan > 0:
            real_minority = np.vstack([X_min, X_synthetic]) if len(X_synthetic) > 0 else X_min
            
            if len(real_minority) < batch_size:
                batch_size = max(1, len(real_minority) // 2)
            
            wgan_gp = WGAN_GP(
                latent_dim=wgan_config.LATENT_DIM,
                data_dim=X_train.shape[1],
                lambda_gp=lambda_gp,
                lr=lr,
                batch_size=batch_size,
                n_critic=n_critic,
                force_cpu=True
            )
            
            print(f"训练WGAN-GP直到收敛 (最多2500轮)...")
            print(f"  训练数据: {len(real_minority)} 个少数类样本")
            print(f"  网络配置: 潜在维度={wgan_config.LATENT_DIM}, 数据维度={X_train.shape[1]}")
            print(f"  训练参数: λ={lambda_gp:.2f}, n_critic={n_critic}, lr={lr:.2e}, batch_size={batch_size}")
            print(f"  目标: G和D损失都逐渐平稳，达到对抗平衡")
            
            # 训练足够长时间直到收敛
            wgan_gp.train_with_progress(real_minority, epochs=2500)
            
            # 获取训练损失
            losses = wgan_gp.get_training_history()
            
            # 绘制收敛损失函数图
            plot_wgan_convergence_losses(losses, 'yeast_ga_adasyn_wgan_convergence_test.png')
            
        else:
            print("⚠️ 不需要WGAN-GP生成，跳过训练")
            
    except Exception as e:
        print(f"❌ 训练过程出错: {e}")
        import traceback
        traceback.print_exc()
    
    print(f"\n✅ 测试完成！")

if __name__ == "__main__":
    test_yeast_wgan_convergence()
