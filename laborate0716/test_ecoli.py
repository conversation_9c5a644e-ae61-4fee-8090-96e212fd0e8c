"""
测试E.coli数据集处理脚本
"""

import os
import sys
import numpy as np
import warnings
warnings.filterwarnings('ignore')

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_ecoli_data_loading():
    """测试E.coli数据加载"""
    print("=" * 60)
    print("测试E.coli数据集加载")
    print("=" * 60)
    
    try:
        from ecoli import load_ecoli_data
        
        X, y, minority_count, majority_count, imbalance_ratio = load_ecoli_data()
        
        print(f"✓ 数据加载成功")
        print(f"  数据形状: {X.shape}")
        print(f"  标签形状: {y.shape}")
        print(f"  少数类(im)数目: {minority_count}")
        print(f"  多数类数目: {majority_count}")
        print(f"  不平衡比例: {imbalance_ratio:.2f}:1")
        print(f"  类别分布: {np.bincount(y)}")
        
        return True, (X, y)
        
    except Exception as e:
        print(f"✗ 数据加载失败: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def test_traditional_adasyn():
    """测试传统ADASYN方法"""
    print("\n" + "=" * 60)
    print("测试传统ADASYN方法")
    print("=" * 60)
    
    try:
        from ecoli import load_ecoli_data, method_traditional_adasyn
        from sklearn.model_selection import train_test_split
        
        # 加载数据
        X, y, _, _, _ = load_ecoli_data()
        
        # 数据划分
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.3, stratify=y, random_state=42
        )
        
        print(f"训练集: {len(X_train)} 样本 (少数类: {np.sum(y_train == 1)})")
        
        # 运行传统ADASYN
        X_balanced, y_balanced, losses, fitness = method_traditional_adasyn(
            X_train, y_train, X_test, y_test
        )
        
        print(f"✓ 传统ADASYN测试成功")
        print(f"  平衡后数据: {len(X_balanced)} 样本")
        print(f"  类别分布: {np.bincount(y_balanced)}")
        
        return True
        
    except Exception as e:
        print(f"✗ 传统ADASYN测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_evaluation():
    """测试评估函数"""
    print("\n" + "=" * 60)
    print("测试十折交叉验证评估")
    print("=" * 60)
    
    try:
        from ecoli import load_ecoli_data, evaluate_method
        
        # 加载数据
        X, y, _, _, _ = load_ecoli_data()
        
        # 测试原始数据集评估
        result = evaluate_method(X, y, X, y, "原始E.coli数据集")
        
        print(f"✓ 评估测试成功")
        print(f"  F1-Score: {result['f1_mean']:.4f} ± {result['f1_std']:.4f}")
        print(f"  G-mean: {result['gmean_mean']:.4f} ± {result['gmean_std']:.4f}")
        
        return True
        
    except Exception as e:
        print(f"✗ 评估测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ga_adasyn_only():
    """测试GA只优化ADASYN参数"""
    print("\n" + "=" * 60)
    print("测试GA只优化ADASYN参数")
    print("=" * 60)
    
    try:
        from ecoli import load_ecoli_data, method_ga_adasyn_only
        from sklearn.model_selection import train_test_split
        
        # 加载数据
        X, y, _, _, _ = load_ecoli_data()
        
        # 数据划分
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.3, stratify=y, random_state=42
        )
        
        print(f"开始GA优化ADASYN参数测试...")
        
        # 运行GA优化ADASYN（使用很小的参数进行快速测试）
        X_balanced, y_balanced, losses, fitness = method_ga_adasyn_only(
            X_train, y_train, X_test, y_test
        )
        
        print(f"✓ GA优化ADASYN测试成功")
        print(f"  最佳适应度: {fitness:.4f}")
        print(f"  平衡后数据: {len(X_balanced)} 样本")
        print(f"  类别分布: {np.bincount(y_balanced)}")
        
        return True
        
    except Exception as e:
        print(f"✗ GA优化ADASYN测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("E.coli数据集处理脚本测试")
    print("=" * 80)
    
    tests = [
        ("数据加载", test_ecoli_data_loading),
        ("传统ADASYN", test_traditional_adasyn),
        ("十折交叉验证评估", test_evaluation),
        ("GA优化ADASYN", test_ga_adasyn_only)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n运行测试: {test_name}")
        try:
            if test_name == "数据加载":
                result, _ = test_func()
            else:
                result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 测试结果汇总
    print("\n" + "=" * 80)
    print("测试结果汇总")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name:20}: {status}")
        if result:
            passed += 1
    
    print("-" * 80)
    print(f"总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！可以运行完整的ecoli.py实验")
        print("\n运行完整实验: python ecoli.py")
    else:
        print("⚠️  部分测试失败，请检查错误信息")
    
    return passed == total

if __name__ == "__main__":
    # 设置随机种子
    np.random.seed(42)
    
    # 运行测试
    success = main()
    sys.exit(0 if success else 1)
