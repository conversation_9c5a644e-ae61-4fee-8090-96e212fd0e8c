"""
遗传算法实现：优化ADASYN和WGAN-GP参数
"""

import numpy as np
import random
import logging
from typing import List, Tuple, Callable
import copy
from concurrent.futures import ProcessPoolExecutor, as_completed
import multiprocessing as mp

from config import ga_config, param_ranges
from fitness_function import fitness_function

logger = logging.getLogger(__name__)

class Individual:
    """个体类"""
    def __init__(self, genes: List[float], fitness: float = 0.0):
        self.genes = genes
        self.fitness = fitness
        self.age = 0
    
    def __str__(self):
        return f"Individual(fitness={self.fitness:.4f}, genes={self.genes})"
    
    def copy(self):
        return Individual(self.genes.copy(), self.fitness)

class GeneticAlgorithm:
    """遗传算法类"""
    
    def __init__(self, fitness_func: Callable, param_bounds: List[Tuple], 
                 param_types: List[str], population_size: int = 50,
                 max_generations: int = 100, elite_size: int = 5):
        
        self.fitness_func = fitness_func
        self.param_bounds = param_bounds
        self.param_types = param_types
        self.population_size = population_size
        self.max_generations = max_generations
        self.elite_size = elite_size
        
        # 遗传算法参数
        self.crossover_rate = ga_config.CROSSOVER_RATE
        self.mutation_rate = ga_config.MUTATION_RATE
        self.tournament_size = ga_config.TOURNAMENT_SIZE
        self.eta_c = ga_config.ETA_C  # SBX交叉参数
        self.eta_m = ga_config.ETA_M  # 多项式变异参数
        
        # 早停参数
        self.early_stop_patience = ga_config.EARLY_STOP_PATIENCE
        self.early_stop_threshold = ga_config.EARLY_STOP_THRESHOLD
        
        # 历史记录
        self.best_fitness_history = []
        self.avg_fitness_history = []
        self.population_history = []
        
        # 设置随机种子
        np.random.seed(ga_config.RANDOM_SEED)
        random.seed(ga_config.RANDOM_SEED)
    
    def initialize_population(self) -> List[Individual]:
        """初始化种群"""
        population = []
        
        for _ in range(self.population_size):
            genes = []
            for i, (lower, upper) in enumerate(self.param_bounds):
                if self.param_types[i] == 'int':
                    gene = random.randint(int(lower), int(upper))
                else:
                    gene = random.uniform(lower, upper)
                genes.append(gene)
            
            individual = Individual(genes)
            population.append(individual)
        
        logger.info(f"初始化种群完成，种群大小: {len(population)}")
        return population
    
    def evaluate_population(self, population: List[Individual], 
                          X_train, y_train, X_val, y_val) -> List[Individual]:
        """评估种群适应度"""
        logger.info("开始评估种群适应度...")
        
        for i, individual in enumerate(population):
            if individual.fitness == 0.0:  # 只评估未评估的个体
                try:
                    individual.fitness = self.fitness_func(
                        individual.genes, X_train, y_train, X_val, y_val
                    )
                except Exception as e:
                    logger.error(f"个体 {i} 适应度评估失败: {e}")
                    individual.fitness = 0.0
        
        # 按适应度排序
        population.sort(key=lambda x: x.fitness, reverse=True)
        
        logger.info(f"种群评估完成，最佳适应度: {population[0].fitness:.4f}")
        return population
    
    def tournament_selection(self, population: List[Individual]) -> Individual:
        """锦标赛选择"""
        tournament = random.sample(population, self.tournament_size)
        return max(tournament, key=lambda x: x.fitness)
    
    def simulated_binary_crossover(self, parent1: Individual, 
                                 parent2: Individual) -> Tuple[Individual, Individual]:
        """模拟二进制交叉 (SBX)"""
        child1_genes = parent1.genes.copy()
        child2_genes = parent2.genes.copy()
        
        for i in range(len(parent1.genes)):
            if random.random() < 0.5:  # 交叉概率
                if self.param_types[i] == 'float':
                    # 连续变量使用SBX
                    if abs(parent1.genes[i] - parent2.genes[i]) > 1e-14:
                        y1, y2 = min(parent1.genes[i], parent2.genes[i]), max(parent1.genes[i], parent2.genes[i])
                        lb, ub = self.param_bounds[i]
                        
                        # 计算beta
                        rand = random.random()
                        if rand <= 0.5:
                            beta = (2 * rand) ** (1.0 / (self.eta_c + 1))
                        else:
                            beta = (1.0 / (2.0 * (1.0 - rand))) ** (1.0 / (self.eta_c + 1))
                        
                        # 生成子代
                        c1 = 0.5 * ((y1 + y2) - beta * (y2 - y1))
                        c2 = 0.5 * ((y1 + y2) + beta * (y2 - y1))
                        
                        # 边界处理
                        child1_genes[i] = max(lb, min(ub, c1))
                        child2_genes[i] = max(lb, min(ub, c2))
                else:
                    # 整数变量使用单点交叉
                    if random.random() < 0.5:
                        child1_genes[i], child2_genes[i] = child2_genes[i], child1_genes[i]
        
        return Individual(child1_genes), Individual(child2_genes)
    
    def polynomial_mutation(self, individual: Individual) -> Individual:
        """多项式变异"""
        mutated_genes = individual.genes.copy()
        
        for i in range(len(mutated_genes)):
            if random.random() < self.mutation_rate:
                if self.param_types[i] == 'float':
                    # 连续变量多项式变异
                    lb, ub = self.param_bounds[i]
                    y = mutated_genes[i]
                    
                    delta1 = (y - lb) / (ub - lb)
                    delta2 = (ub - y) / (ub - lb)
                    
                    rand = random.random()
                    mut_pow = 1.0 / (self.eta_m + 1.0)
                    
                    if rand <= 0.5:
                        xy = 1.0 - delta1
                        val = 2.0 * rand + (1.0 - 2.0 * rand) * (xy ** (self.eta_m + 1.0))
                        deltaq = val ** mut_pow - 1.0
                    else:
                        xy = 1.0 - delta2
                        val = 2.0 * (1.0 - rand) + 2.0 * (rand - 0.5) * (xy ** (self.eta_m + 1.0))
                        deltaq = 1.0 - val ** mut_pow
                    
                    y = y + deltaq * (ub - lb)
                    mutated_genes[i] = max(lb, min(ub, y))
                    
                else:
                    # 整数变量随机重置
                    lb, ub = self.param_bounds[i]
                    mutated_genes[i] = random.randint(int(lb), int(ub))
        
        return Individual(mutated_genes)
    
    def create_offspring(self, population: List[Individual]) -> List[Individual]:
        """创建子代"""
        offspring = []
        
        # 精英保留
        elite = population[:self.elite_size]
        offspring.extend([ind.copy() for ind in elite])
        
        # 生成剩余子代
        while len(offspring) < self.population_size:
            # 选择父代
            parent1 = self.tournament_selection(population)
            parent2 = self.tournament_selection(population)
            
            # 交叉
            if random.random() < self.crossover_rate:
                child1, child2 = self.simulated_binary_crossover(parent1, parent2)
            else:
                child1, child2 = parent1.copy(), parent2.copy()
            
            # 变异
            child1 = self.polynomial_mutation(child1)
            child2 = self.polynomial_mutation(child2)
            
            offspring.extend([child1, child2])
        
        return offspring[:self.population_size]
    
    def check_early_stopping(self) -> bool:
        """检查早停条件"""
        if len(self.best_fitness_history) < self.early_stop_patience:
            return False
        
        recent_best = self.best_fitness_history[-self.early_stop_patience:]
        improvement = max(recent_best) - min(recent_best)
        
        return improvement < self.early_stop_threshold
    
    def optimize(self, X_train, y_train, X_val, y_val) -> Tuple[Individual, dict]:
        """执行遗传算法优化"""
        logger.info("开始遗传算法优化...")
        
        # 初始化种群
        population = self.initialize_population()
        
        # 评估初始种群
        population = self.evaluate_population(population, X_train, y_train, X_val, y_val)
        
        best_individual = population[0].copy()
        stagnation_count = 0
        
        for generation in range(self.max_generations):
            logger.info(f"第 {generation + 1}/{self.max_generations} 代")
            
            # 记录历史
            best_fitness = population[0].fitness
            avg_fitness = np.mean([ind.fitness for ind in population])
            
            self.best_fitness_history.append(best_fitness)
            self.avg_fitness_history.append(avg_fitness)
            
            logger.info(f"最佳适应度: {best_fitness:.4f}, 平均适应度: {avg_fitness:.4f}")
            
            # 更新全局最优
            if population[0].fitness > best_individual.fitness:
                best_individual = population[0].copy()
                stagnation_count = 0
                logger.info(f"发现更优解: {best_individual.fitness:.4f}")
            else:
                stagnation_count += 1
            
            # 早停检查
            if self.check_early_stopping():
                logger.info(f"满足早停条件，在第 {generation + 1} 代停止")
                break
            
            # 创建下一代
            offspring = self.create_offspring(population)
            
            # 评估子代
            population = self.evaluate_population(offspring, X_train, y_train, X_val, y_val)
        
        # 返回结果
        optimization_result = {
            'best_individual': best_individual,
            'best_fitness_history': self.best_fitness_history,
            'avg_fitness_history': self.avg_fitness_history,
            'final_population': population,
            'generations_run': len(self.best_fitness_history)
        }
        
        logger.info(f"优化完成，最佳适应度: {best_individual.fitness:.4f}")
        
        return best_individual, optimization_result

    def optimize_with_progress(self, X_train, y_train, X_val, y_val) -> Tuple[Individual, dict]:
        """执行遗传算法优化（带详细进度显示）"""
        print("初始化遗传算法优化...")

        # 初始化种群
        population = self.initialize_population()
        print(f"✓ 种群初始化完成: {len(population)} 个个体")

        # 评估初始种群
        print("评估初始种群适应度...")
        population = self.evaluate_population_with_progress(population, X_train, y_train, X_val, y_val)

        best_individual = population[0].copy()
        stagnation_count = 0

        print(f"\n开始进化过程...")
        print(f"{'代数':<6} {'最佳适应度':<12} {'平均适应度':<12} {'改进':<8} {'停滞代数':<8} {'状态'}")
        print("-" * 70)

        for generation in range(self.max_generations):
            # 记录历史
            best_fitness = population[0].fitness
            avg_fitness = np.mean([ind.fitness for ind in population])

            self.best_fitness_history.append(best_fitness)
            self.avg_fitness_history.append(avg_fitness)

            # 计算改进
            improvement = 0.0
            if generation > 0:
                improvement = best_fitness - self.best_fitness_history[generation-1]

            # 更新全局最优
            status = "正常"
            if population[0].fitness > best_individual.fitness:
                best_individual = population[0].copy()
                stagnation_count = 0
                status = "✓新最优"
            else:
                stagnation_count += 1
                if stagnation_count >= self.early_stop_patience // 2:
                    status = f"停滞{stagnation_count}"

            # 显示进度
            print(f"{generation+1:<6} {best_fitness:<12.6f} {avg_fitness:<12.6f} "
                  f"{improvement:<8.6f} {stagnation_count:<8} {status}")

            # 早停检查
            if self.check_early_stopping():
                print(f"\n✓ 满足早停条件，在第 {generation + 1} 代停止")
                print(f"  连续 {self.early_stop_patience} 代改进小于 {self.early_stop_threshold}")
                break

            # 显示当前最优个体参数
            if generation % 5 == 0 or generation == self.max_generations - 1:
                self.display_best_parameters(best_individual, generation + 1)

            # 创建下一代
            offspring = self.create_offspring(population)

            # 评估子代
            population = self.evaluate_population_with_progress(offspring, X_train, y_train, X_val, y_val,
                                                              generation + 1, brief=True)

        # 返回结果
        optimization_result = {
            'best_individual': best_individual,
            'best_fitness_history': self.best_fitness_history,
            'avg_fitness_history': self.avg_fitness_history,
            'final_population': population,
            'generations_run': len(self.best_fitness_history)
        }

        print(f"\n{'='*70}")
        print(f"优化完成！")
        print(f"最佳适应度: {best_individual.fitness:.6f}")
        print(f"运行代数: {len(self.best_fitness_history)}")
        print(f"总评估次数: {len(self.best_fitness_history) * self.population_size}")

        return best_individual, optimization_result

    def evaluate_population_with_progress(self, population: List[Individual],
                                        X_train, y_train, X_val, y_val,
                                        generation: int = 0, brief: bool = False) -> List[Individual]:
        """评估种群适应度（带进度显示）"""
        if not brief:
            print(f"评估第 {generation} 代种群适应度...")

        evaluated_count = 0
        total_count = len(population)

        for i, individual in enumerate(population):
            if individual.fitness == 0.0:  # 只评估未评估的个体
                try:
                    individual.fitness = self.fitness_func(
                        individual.genes, X_train, y_train, X_val, y_val
                    )
                    evaluated_count += 1

                    # 显示进度
                    if not brief and (i + 1) % max(1, total_count // 10) == 0:
                        progress = (i + 1) / total_count * 100
                        print(f"  进度: {progress:.1f}% ({i+1}/{total_count}) "
                              f"当前适应度: {individual.fitness:.4f}")

                except Exception as e:
                    logger.error(f"个体 {i} 适应度评估失败: {e}")
                    individual.fitness = 0.0

        # 按适应度排序
        population.sort(key=lambda x: x.fitness, reverse=True)

        if not brief:
            print(f"✓ 种群评估完成，最佳适应度: {population[0].fitness:.6f}")
            print(f"  评估个体数: {evaluated_count}/{total_count}")
            print(f"  适应度范围: [{population[-1].fitness:.4f}, {population[0].fitness:.4f}]")

        return population

    def display_best_parameters(self, individual: Individual, generation: int):
        """显示当前最优个体的参数"""
        print(f"\n--- 第 {generation} 代最优个体参数 ---")
        try:
            # 解码参数
            k = int(individual.genes[0])
            alpha = individual.genes[1]
            lambda_gp = individual.genes[2]
            n_critic = int(individual.genes[3])
            lr = 10 ** individual.genes[4]
            batch_size_idx = int(individual.genes[5])
            from config import param_ranges
            batch_size = param_ranges.WGAN_BATCH_SIZES[batch_size_idx]

            print(f"  ADASYN k邻居: {k}")
            print(f"  ADASYN α平衡: {alpha:.4f}")
            print(f"  WGAN λ梯度惩罚: {lambda_gp:.4f}")
            print(f"  WGAN 判别器训练次数: {n_critic}")
            print(f"  WGAN 学习率: {lr:.2e}")
            print(f"  WGAN 批量大小: {batch_size}")
            print(f"  适应度: {individual.fitness:.6f}")

        except Exception as e:
            print(f"  参数解码失败: {e}")
            print(f"  原始基因: {individual.genes}")

        print("-" * 40)

def decode_best_individual(individual: Individual) -> dict:
    """解码最优个体参数"""
    k = int(individual.genes[0])
    alpha = individual.genes[1]
    lambda_gp = individual.genes[2]
    n_critic = int(individual.genes[3])
    lr = 10 ** individual.genes[4]
    batch_size_idx = int(individual.genes[5])
    batch_size = param_ranges.WGAN_BATCH_SIZES[batch_size_idx]
    
    return {
        'adasyn_k': k,
        'adasyn_alpha': alpha,
        'wgan_lambda': lambda_gp,
        'wgan_n_critic': n_critic,
        'wgan_lr': lr,
        'wgan_batch_size': batch_size,
        'fitness': individual.fitness
    }
