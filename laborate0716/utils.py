"""
工具函数：日志设置、结果保存、文件管理等辅助功能
"""

import os
import json
import pickle
import logging
import shutil
from datetime import datetime
import numpy as np
import pandas as pd
from typing import Dict, Any
import matplotlib.pyplot as plt

from config import logging_config, exp_config

def setup_logging(results_dir: str = None):
    """设置日志系统"""
    # 创建日志目录
    if results_dir:
        log_file = os.path.join(results_dir, logging_config.LOG_FILE)
    else:
        log_file = logging_config.LOG_FILE
    
    # 配置日志格式
    formatter = logging.Formatter(logging_config.LOG_FORMAT)
    
    # 根日志器配置
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, logging_config.LOG_LEVEL))
    
    # 清除现有处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # 文件处理器
    file_handler = logging.FileHandler(log_file, mode='w', encoding='utf-8')
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(formatter)
    root_logger.addHandler(file_handler)
    
    # 控制台处理器
    if logging_config.CONSOLE_OUTPUT:
        console_handler = logging.StreamHandler()
        console_handler.setLevel(getattr(logging, logging_config.LOG_LEVEL))
        console_handler.setFormatter(formatter)
        root_logger.addHandler(console_handler)
    
    # 设置第三方库日志级别
    logging.getLogger('matplotlib').setLevel(logging.WARNING)
    logging.getLogger('sklearn').setLevel(logging.WARNING)
    logging.getLogger('torch').setLevel(logging.WARNING)
    
    logger = logging.getLogger(__name__)
    logger.info(f"日志系统初始化完成，日志文件: {log_file}")

def create_results_directory() -> str:
    """创建结果目录"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_dir = os.path.join(exp_config.RESULTS_DIR, f"experiment_{timestamp}")
    
    os.makedirs(results_dir, exist_ok=True)
    
    # 创建子目录
    subdirs = ['plots', 'models', 'data', 'logs']
    for subdir in subdirs:
        os.makedirs(os.path.join(results_dir, subdir), exist_ok=True)
    
    return results_dir

def save_results(results: Dict[str, Any], save_dir: str):
    """保存实验结果"""
    try:
        # 保存JSON格式（可读性好）
        json_file = os.path.join(save_dir, 'results.json')
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, default=str, ensure_ascii=False)
        
        # 保存Pickle格式（完整数据）
        pickle_file = os.path.join(save_dir, 'results.pkl')
        with open(pickle_file, 'wb') as f:
            pickle.dump(results, f)
        
        # 保存CSV格式的关键指标
        if 'test_results' in results and isinstance(results['test_results'], dict):
            save_metrics_csv(results, save_dir)
        
        logger = logging.getLogger(__name__)
        logger.info(f"结果已保存到: {save_dir}")
        
    except Exception as e:
        logger = logging.getLogger(__name__)
        logger.error(f"结果保存失败: {str(e)}")

def save_metrics_csv(results: Dict[str, Any], save_dir: str):
    """保存关键指标到CSV文件"""
    try:
        metrics_data = []
        
        # 基本信息
        basic_info = {
            'dataset': results.get('dataset', 'unknown'),
            'timestamp': results.get('timestamp', ''),
            'execution_time': results.get('execution_time', 0)
        }
        
        # 最优参数
        if 'best_params' in results:
            basic_info.update({
                f"param_{k}": v for k, v in results['best_params'].items()
            })
        
        # 测试结果
        if 'test_results' in results:
            test_results = results['test_results']
            if isinstance(test_results, dict):
                basic_info.update({
                    f"test_{k}": v for k, v in test_results.items()
                    if isinstance(v, (int, float, str))
                })
        
        # TSTR结果
        if 'test_results' in results and 'tstr_results' in results['test_results']:
            tstr_results = results['test_results']['tstr_results']
            if isinstance(tstr_results, dict):
                basic_info.update({
                    f"tstr_{k}": v for k, v in tstr_results.items()
                    if isinstance(v, (int, float, str))
                })
        
        metrics_data.append(basic_info)
        
        # 保存到CSV
        df = pd.DataFrame(metrics_data)
        csv_file = os.path.join(save_dir, 'metrics.csv')
        df.to_csv(csv_file, index=False)
        
    except Exception as e:
        logger = logging.getLogger(__name__)
        logger.error(f"CSV指标保存失败: {str(e)}")

def load_results(results_dir: str) -> Dict[str, Any]:
    """加载实验结果"""
    try:
        pickle_file = os.path.join(results_dir, 'results.pkl')
        if os.path.exists(pickle_file):
            with open(pickle_file, 'rb') as f:
                return pickle.load(f)
        
        json_file = os.path.join(results_dir, 'results.json')
        if os.path.exists(json_file):
            with open(json_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        
        raise FileNotFoundError("未找到结果文件")
        
    except Exception as e:
        logger = logging.getLogger(__name__)
        logger.error(f"结果加载失败: {str(e)}")
        return {}

def backup_code(results_dir: str):
    """备份代码到结果目录"""
    try:
        code_backup_dir = os.path.join(results_dir, 'code_backup')
        os.makedirs(code_backup_dir, exist_ok=True)
        
        # 当前目录下的Python文件
        current_dir = os.path.dirname(os.path.abspath(__file__))
        python_files = [f for f in os.listdir(current_dir) if f.endswith('.py')]
        
        for file in python_files:
            src = os.path.join(current_dir, file)
            dst = os.path.join(code_backup_dir, file)
            shutil.copy2(src, dst)
        
        logger = logging.getLogger(__name__)
        logger.info(f"代码已备份到: {code_backup_dir}")
        
    except Exception as e:
        logger = logging.getLogger(__name__)
        logger.error(f"代码备份失败: {str(e)}")

def create_experiment_summary(all_results: Dict[str, Any]) -> str:
    """创建实验总结报告"""
    summary_lines = []
    summary_lines.append("=" * 60)
    summary_lines.append("遗传算法优化ADASYN+WGAN-GP参数实验总结")
    summary_lines.append("=" * 60)
    
    # 基本统计
    summary = all_results.get('summary', {})
    summary_lines.append(f"实验时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    summary_lines.append(f"总数据集数量: {summary.get('total_datasets', 0)}")
    summary_lines.append(f"成功实验: {summary.get('successful_experiments', 0)}")
    summary_lines.append(f"失败实验: {summary.get('failed_experiments', 0)}")
    summary_lines.append(f"总执行时间: {summary.get('total_time', 0):.2f} 秒")
    summary_lines.append("")
    
    # 各数据集结果
    if 'best_performances' in summary:
        summary_lines.append("各数据集最佳性能:")
        summary_lines.append("-" * 40)
        
        performances = summary['best_performances']
        for dataset, perf in performances.items():
            summary_lines.append(f"{dataset:12}: 适应度={perf['fitness']:.4f}, "
                                f"测试F1={perf['test_f1']:.4f}")
        
        # 统计信息
        fitness_scores = [p['fitness'] for p in performances.values()]
        f1_scores = [p['test_f1'] for p in performances.values()]
        
        summary_lines.append("")
        summary_lines.append("统计信息:")
        summary_lines.append(f"平均适应度: {np.mean(fitness_scores):.4f} ± {np.std(fitness_scores):.4f}")
        summary_lines.append(f"平均测试F1: {np.mean(f1_scores):.4f} ± {np.std(f1_scores):.4f}")
        summary_lines.append(f"最佳适应度: {np.max(fitness_scores):.4f}")
        summary_lines.append(f"最佳测试F1: {np.max(f1_scores):.4f}")
    
    # 配置信息
    summary_lines.append("")
    summary_lines.append("实验配置:")
    summary_lines.append("-" * 20)
    
    if 'experiment_config' in all_results:
        config = all_results['experiment_config']
        
        if 'ga_config' in config:
            ga_cfg = config['ga_config']
            summary_lines.append(f"种群大小: {ga_cfg.get('POPULATION_SIZE', 'N/A')}")
            summary_lines.append(f"最大代数: {ga_cfg.get('MAX_GENERATIONS', 'N/A')}")
            summary_lines.append(f"精英大小: {ga_cfg.get('ELITE_SIZE', 'N/A')}")
        
        if 'exp_config' in config:
            exp_cfg = config['exp_config']
            summary_lines.append(f"分类器类型: {exp_cfg.get('CLASSIFIER_TYPE', 'N/A')}")
            summary_lines.append(f"F1权重: {exp_cfg.get('F1_WEIGHT', 'N/A')}")
            summary_lines.append(f"AUC权重: {exp_cfg.get('AUC_WEIGHT', 'N/A')}")
    
    summary_lines.append("=" * 60)
    
    return "\n".join(summary_lines)

def format_time(seconds: float) -> str:
    """格式化时间显示"""
    if seconds < 60:
        return f"{seconds:.1f}秒"
    elif seconds < 3600:
        minutes = seconds // 60
        secs = seconds % 60
        return f"{int(minutes)}分{secs:.1f}秒"
    else:
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        secs = seconds % 60
        return f"{int(hours)}小时{int(minutes)}分{secs:.1f}秒"

def validate_parameters(params: Dict[str, Any]) -> bool:
    """验证参数有效性"""
    try:
        # ADASYN参数验证
        k = params.get('adasyn_k', 0)
        alpha = params.get('adasyn_alpha', 0)
        
        if not (3 <= k <= 20):
            return False
        if not (0.5 <= alpha <= 1.0):
            return False
        
        # WGAN-GP参数验证
        lambda_gp = params.get('wgan_lambda', 0)
        n_critic = params.get('wgan_n_critic', 0)
        lr = params.get('wgan_lr', 0)
        batch_size = params.get('wgan_batch_size', 0)
        
        if not (1.0 <= lambda_gp <= 20.0):
            return False
        if not (1 <= n_critic <= 10):
            return False
        if not (1e-5 <= lr <= 1e-3):
            return False
        if batch_size not in [32, 64, 128, 256]:
            return False
        
        return True
        
    except Exception:
        return False

def calculate_improvement_percentage(baseline: float, optimized: float) -> float:
    """计算改进百分比"""
    if baseline == 0:
        return 0.0
    return ((optimized - baseline) / baseline) * 100

def create_comparison_table(results_dict: Dict[str, Dict]) -> pd.DataFrame:
    """创建结果比较表"""
    try:
        comparison_data = []
        
        for dataset, result in results_dict.items():
            if 'error' in result:
                continue
            
            row = {
                'Dataset': dataset,
                'Best_Fitness': result.get('best_params', {}).get('fitness', 0),
                'Test_F1_Weighted': result.get('test_results', {}).get('f1_weighted', 0),
                'Test_F1_Minority': result.get('test_results', {}).get('f1_minority', 0),
                'Test_AUC': result.get('test_results', {}).get('auc_roc', 0),
                'Test_GMean': result.get('test_results', {}).get('geometric_mean', 0),
                'ADASYN_k': result.get('best_params', {}).get('adasyn_k', 0),
                'ADASYN_alpha': result.get('best_params', {}).get('adasyn_alpha', 0),
                'WGAN_lambda': result.get('best_params', {}).get('wgan_lambda', 0),
                'WGAN_lr': result.get('best_params', {}).get('wgan_lr', 0),
                'Execution_Time': result.get('execution_time', 0)
            }
            comparison_data.append(row)
        
        return pd.DataFrame(comparison_data)
        
    except Exception as e:
        logger = logging.getLogger(__name__)
        logger.error(f"比较表创建失败: {str(e)}")
        return pd.DataFrame()

def export_results_to_excel(all_results: Dict[str, Any], save_path: str):
    """导出结果到Excel文件"""
    try:
        with pd.ExcelWriter(save_path, engine='openpyxl') as writer:
            # 总结表
            summary_data = {
                'Metric': ['Total Datasets', 'Successful Experiments', 'Failed Experiments', 'Total Time (s)'],
                'Value': [
                    all_results['summary'].get('total_datasets', 0),
                    all_results['summary'].get('successful_experiments', 0),
                    all_results['summary'].get('failed_experiments', 0),
                    all_results['summary'].get('total_time', 0)
                ]
            }
            pd.DataFrame(summary_data).to_excel(writer, sheet_name='Summary', index=False)
            
            # 详细结果表
            if 'detailed_results' in all_results:
                comparison_df = create_comparison_table(all_results['detailed_results'])
                if not comparison_df.empty:
                    comparison_df.to_excel(writer, sheet_name='Detailed_Results', index=False)
            
            # 配置信息
            if 'experiment_config' in all_results:
                config_data = []
                for section, params in all_results['experiment_config'].items():
                    for key, value in params.items():
                        config_data.append({'Section': section, 'Parameter': key, 'Value': str(value)})
                
                pd.DataFrame(config_data).to_excel(writer, sheet_name='Configuration', index=False)
        
        logger = logging.getLogger(__name__)
        logger.info(f"结果已导出到Excel: {save_path}")
        
    except Exception as e:
        logger = logging.getLogger(__name__)
        logger.error(f"Excel导出失败: {str(e)}")

def cleanup_temp_files(temp_dir: str):
    """清理临时文件"""
    try:
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)
        logger = logging.getLogger(__name__)
        logger.info(f"临时文件已清理: {temp_dir}")
    except Exception as e:
        logger = logging.getLogger(__name__)
        logger.error(f"临时文件清理失败: {str(e)}")

# 全局异常处理装饰器
def handle_exceptions(func):
    """异常处理装饰器"""
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            logger = logging.getLogger(__name__)
            logger.error(f"函数 {func.__name__} 执行失败: {str(e)}")
            raise
    return wrapper
