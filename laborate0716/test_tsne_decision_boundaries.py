"""
测试Car数据集t-SNE决策边界可视化功能
通过t-SNE降维投影，可视化采用不同过采样技术训练的分类器所得到的决策边界
"""

import os
import sys
import numpy as np
import pandas as pd
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import LabelEncoder, StandardScaler
from sklearn.manifold import TSNE
from imblearn.over_sampling import ADASYN, SMOTE
import warnings
warnings.filterwarnings('ignore')

def load_car_data():
    """加载Car数据集"""
    print("📊 加载Car数据集...")
    
    # 数据路径
    data_path = "C:/Users/<USER>/Desktop/GAAD/data/car.data"
    
    # 列名
    column_names = ['buying', 'maint', 'doors', 'persons', 'lug_boot', 'safety', 'class']
    
    try:
        # 加载数据
        data = pd.read_csv(data_path, header=None, names=column_names)
        print(f"数据加载成功，形状: {data.shape}")
        
        # 编码分类特征
        label_encoders = {}
        for col in column_names[:-1]:  # 除了目标列
            le = LabelEncoder()
            data[col] = le.fit_transform(data[col])
            label_encoders[col] = le
        
        # 处理目标变量：将vgood作为少数类(1)，其他作为多数类(0)
        y = (data['class'] == 'vgood').astype(int)
        X = data.drop('class', axis=1)
        
        # 统计信息
        minority_count = np.sum(y == 1)
        majority_count = np.sum(y == 0)
        imbalance_ratio = majority_count / minority_count if minority_count > 0 else float('inf')
        
        print(f"数据集统计:")
        print(f"总样本数: {len(y)}")
        print(f"少数类(vgood)数目: {minority_count}")
        print(f"多数类数目: {majority_count}")
        print(f"不平衡比例: {imbalance_ratio:.2f}:1")
        
        return X.values, y.values
        
    except Exception as e:
        print(f"数据加载失败: {e}")
        return None, None

def plot_tsne_decision_boundaries(all_datasets, save_path='car_tsne_decision_boundaries_test.png'):
    """
    通过t-SNE降维投影，可视化采用不同过采样技术训练的分类器所得到的决策边界
    展示在二维合成数据集上的可视化结果（该数据集专门用于凸显决策边界效应）
    """
    print(f"\n🎨 生成t-SNE决策边界可视化图...")
    
    if not all_datasets:
        print("❌ 没有数据集可供可视化")
        return
    
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 计算子图布局
    n_methods = len(all_datasets)
    n_cols = 3
    n_rows = (n_methods + n_cols - 1) // n_cols
    
    fig, axes = plt.subplots(n_rows, n_cols, figsize=(18, 6 * n_rows))
    if n_rows == 1:
        axes = axes.reshape(1, -1)
    elif n_cols == 1:
        axes = axes.reshape(-1, 1)
    
    # 颜色映射
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD']
    
    for idx, (method_name, (X_data, y_data)) in enumerate(all_datasets.items()):
        row = idx // n_cols
        col = idx % n_cols
        ax = axes[row, col]
        
        try:
            print(f"  处理方法: {method_name}")
            
            # 如果数据维度大于2，使用t-SNE降维
            if X_data.shape[1] > 2:
                print(f"    使用t-SNE降维: {X_data.shape[1]}D -> 2D")
                tsne = TSNE(n_components=2, random_state=42, perplexity=min(30, len(X_data)//4))
                X_tsne = tsne.fit_transform(X_data)
            else:
                X_tsne = X_data
            
            # 训练分类器
            clf = RandomForestClassifier(n_estimators=100, random_state=42)
            clf.fit(X_tsne, y_data)
            
            # 创建网格点用于绘制决策边界
            h = 0.02  # 网格步长
            x_min, x_max = X_tsne[:, 0].min() - 1, X_tsne[:, 0].max() + 1
            y_min, y_max = X_tsne[:, 1].min() - 1, X_tsne[:, 1].max() + 1
            xx, yy = np.meshgrid(np.arange(x_min, x_max, h),
                               np.arange(y_min, y_max, h))
            
            # 预测网格点
            mesh_points = np.c_[xx.ravel(), yy.ravel()]
            Z = clf.predict_proba(mesh_points)[:, 1]  # 获取正类概率
            Z = Z.reshape(xx.shape)
            
            # 绘制决策边界（等高线）
            contour = ax.contourf(xx, yy, Z, levels=50, alpha=0.6, cmap='RdYlBu')
            
            # 绘制决策边界线
            ax.contour(xx, yy, Z, levels=[0.5], colors='black', linestyles='--', linewidths=2)
            
            # 绘制数据点
            unique_labels = np.unique(y_data)
            for i, label in enumerate(unique_labels):
                mask = y_data == label
                color = colors[i % len(colors)]
                label_name = '少数类' if label == 1 else '多数类'
                ax.scatter(X_tsne[mask, 0], X_tsne[mask, 1], 
                          c=color, alpha=0.7, s=30, 
                          label=f'{label_name} ({np.sum(mask)}个)',
                          edgecolors='black', linewidth=0.5)
            
            # 设置标题和标签
            ax.set_title(f'{method_name}\n决策边界可视化', fontsize=12, fontweight='bold')
            ax.set_xlabel('t-SNE 维度 1', fontsize=10)
            ax.set_ylabel('t-SNE 维度 2', fontsize=10)
            ax.legend(fontsize=9, loc='upper right')
            ax.grid(True, alpha=0.3)
            
            # 添加样本统计信息
            n_minority = np.sum(y_data == 1)
            n_majority = np.sum(y_data == 0)
            ratio = n_majority / n_minority if n_minority > 0 else float('inf')
            
            info_text = f'总样本: {len(y_data)}\n'
            info_text += f'少数类: {n_minority}\n'
            info_text += f'多数类: {n_majority}\n'
            info_text += f'比例: {ratio:.1f}:1'
            
            ax.text(0.02, 0.98, info_text, transform=ax.transAxes, fontsize=8,
                   verticalalignment='top', bbox=dict(boxstyle='round,pad=0.3', 
                   facecolor='white', alpha=0.8))
            
        except Exception as e:
            print(f"    ⚠️ 处理 {method_name} 时出错: {e}")
            ax.text(0.5, 0.5, f'处理 {method_name} 时出错\n{str(e)}', 
                   transform=ax.transAxes, ha='center', va='center',
                   bbox=dict(boxstyle='round', facecolor='lightcoral', alpha=0.7))
            ax.set_title(f'{method_name} (错误)', fontsize=12)
    
    # 隐藏多余的子图
    for idx in range(n_methods, n_rows * n_cols):
        row = idx // n_cols
        col = idx % n_cols
        axes[row, col].set_visible(False)
    
    # 设置总标题
    fig.suptitle('Car数据集：不同过采样技术的t-SNE决策边界可视化\n'
                '通过t-SNE降维投影展示分类器决策边界效应', 
                fontsize=16, fontweight='bold', y=0.98)
    
    # 调整布局
    plt.tight_layout()
    plt.subplots_adjust(top=0.93)
    
    # 保存图片
    plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
    plt.close()
    
    print(f"✅ t-SNE决策边界可视化图已保存: {save_path}")
    print(f"📊 可视化了 {n_methods} 种过采样方法的决策边界")

def test_tsne_decision_boundaries():
    """测试t-SNE决策边界可视化功能"""
    print("=" * 80)
    print("Car数据集t-SNE决策边界可视化测试")
    print("=" * 80)
    
    # 设置随机种子
    np.random.seed(42)
    
    # 1. 加载数据
    X, y = load_car_data()
    if X is None:
        print("❌ 数据加载失败，退出程序")
        return
    
    # 2. 数据划分
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.3, stratify=y, random_state=42
    )
    
    print(f"\n训练集: {len(X_train)} 样本")
    print(f"训练集类别分布: {np.bincount(y_train)}")
    
    # 3. 生成不同过采样方法的数据集
    all_datasets = {}
    
    # 原始数据
    all_datasets["原始数据"] = (X_train, y_train)
    
    # SMOTE
    print(f"\n生成SMOTE数据...")
    smote = SMOTE(random_state=42)
    X_smote, y_smote = smote.fit_resample(X_train, y_train)
    all_datasets["SMOTE"] = (X_smote, y_smote)
    print(f"SMOTE生成: {len(X_smote)} 样本")
    
    # ADASYN
    print(f"\n生成ADASYN数据...")
    adasyn = ADASYN(random_state=42)
    X_adasyn, y_adasyn = adasyn.fit_resample(X_train, y_train)
    all_datasets["ADASYN"] = (X_adasyn, y_adasyn)
    print(f"ADASYN生成: {len(X_adasyn)} 样本")
    
    # 模拟GA优化ADASYN-WGAN
    print(f"\n模拟GA优化ADASYN-WGAN数据...")
    # 先使用ADASYN
    X_ga_wgan, y_ga_wgan = adasyn.fit_resample(X_train, y_train)
    # 添加少量噪声模拟WGAN生成的多样性
    minority_indices = np.where(y_ga_wgan == 1)[0]
    if len(minority_indices) > 0:
        X_ga_wgan = X_ga_wgan.astype(np.float64)
        noise_scale = 0.05
        X_ga_wgan[minority_indices] += np.random.normal(0, noise_scale, X_ga_wgan[minority_indices].shape)
    all_datasets["GA优化ADASYN-WGAN"] = (X_ga_wgan, y_ga_wgan)
    print(f"GA优化ADASYN-WGAN生成: {len(X_ga_wgan)} 样本")
    
    # 4. 生成t-SNE决策边界可视化
    plot_tsne_decision_boundaries(all_datasets)
    
    print(f"\n✅ 测试完成！")
    print(f"📊 t-SNE决策边界可视化图已保存")

if __name__ == "__main__":
    test_tsne_decision_boundaries()
