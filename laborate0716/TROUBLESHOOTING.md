# 故障排除指南

## 常见问题及解决方案

### 1. CUDA相关错误

**问题描述**: 
- `RuntimeError: Expected all tensors to be on the same device`
- `CUDA out of memory`
- `CUDA driver version is insufficient`

**解决方案**:

#### 方法1: 强制使用CPU (推荐)
编辑 `config.py` 文件，修改以下行：
```python
class WGANConfig:
    # ... 其他配置 ...
    FORCE_CPU = True  # 改为 True
```

#### 方法2: 临时测试CPU版本
运行CPU版本测试：
```bash
python test_cpu_demo.py
```

#### 方法3: 检查CUDA环境
```bash
python -c "import torch; print(f'CUDA available: {torch.cuda.is_available()}')"
```

### 2. 内存不足错误

**问题描述**: 
- `MemoryError`
- `Out of memory`

**解决方案**:

#### 减少参数规模
编辑 `config.py`:
```python
class GAConfig:
    POPULATION_SIZE = 20  # 从50减少到20
    MAX_GENERATIONS = 50  # 从100减少到50

class WGANConfig:
    EPOCHS = 50  # 从100减少到50
```

#### 使用更小的批量大小
修改参数范围：
```python
class ParameterRanges:
    WGAN_BATCH_SIZES = [16, 32, 64]  # 移除128, 256
```

### 3. 依赖包版本问题

**问题描述**: 
- `ImportError: cannot import name 'wasserstein_distance'`
- `AttributeError: module has no attribute`

**解决方案**:

#### 更新依赖包
```bash
pip install --upgrade scipy>=1.5.0
pip install --upgrade scikit-learn>=0.24.0
pip install --upgrade imbalanced-learn>=0.8.0
```

#### 检查兼容性
```bash
python check_compatibility.py
```

### 4. ADASYN参数错误

**问题描述**: 
- `TypeError: __init__() got an unexpected keyword argument 'k_neighbors'`

**解决方案**: 
这个问题已经修复，确保使用 `n_neighbors` 而不是 `k_neighbors`。如果仍有问题，更新imbalanced-learn：
```bash
pip install --upgrade imbalanced-learn
```

### 5. 数据加载失败

**问题描述**: 
- `FileNotFoundError: 数据文件不存在`
- `ValueError: 不支持的文件格式`

**解决方案**:

#### 检查数据目录结构
```
GAAD/
├── laborate0716/     # 代码目录
└── data/            # 数据目录
    ├── credit.data
    ├── wisconsin.data
    └── ...
```

#### 使用合成数据测试
如果没有真实数据集，系统会自动创建合成数据集进行演示。

### 6. 性能问题

**问题描述**: 
- 运行速度很慢
- 优化过程卡住

**解决方案**:

#### 使用快速配置
创建 `quick_config.py`:
```python
# 快速测试配置
POPULATION_SIZE = 10
MAX_GENERATIONS = 10
WGAN_EPOCHS = 20
```

#### 启用早停
确保早停参数合理：
```python
class GAConfig:
    EARLY_STOP_PATIENCE = 10  # 减少等待代数
    EARLY_STOP_THRESHOLD = 0.01  # 增加阈值
```

### 7. 可视化问题

**问题描述**: 
- `OSError: cannot load library`
- matplotlib显示问题

**解决方案**:

#### 设置matplotlib后端
在代码开头添加：
```python
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
```

#### 安装GUI支持
```bash
pip install tkinter  # Windows
# 或
conda install tk     # 使用conda
```

### 8. 日志和调试

**启用详细日志**:
编辑 `config.py`:
```python
class LoggingConfig:
    LOG_LEVEL = 'DEBUG'  # 从INFO改为DEBUG
    VERBOSE_GA = True
    VERBOSE_WGAN = True
```

**查看详细错误**:
```bash
python run.py demo 2>&1 | tee debug.log
```

## 快速诊断命令

### 1. 系统检查
```bash
python run.py check
```

### 2. 兼容性检查
```bash
python check_compatibility.py
```

### 3. 简单测试
```bash
python test_cpu_demo.py
```

### 4. 依赖检查
```bash
python -c "
import numpy, pandas, sklearn, scipy, torch, matplotlib
print('所有主要依赖包导入成功')
"
```

## 性能优化建议

### 1. 硬件优化
- **内存**: 建议16GB+
- **CPU**: 多核处理器，启用多线程
- **GPU**: 如果有NVIDIA GPU，安装CUDA版本的PyTorch

### 2. 软件优化
- 使用虚拟环境避免包冲突
- 定期更新依赖包
- 使用SSD提高I/O性能

### 3. 参数调优
- 根据数据集大小调整种群大小
- 使用早停避免过度训练
- 合理设置WGAN训练轮数

## 联系支持

如果以上解决方案都无法解决问题，请：

1. 运行 `python check_compatibility.py` 获取系统信息
2. 保存错误日志和堆栈跟踪
3. 记录使用的数据集和配置参数
4. 提供系统环境信息（Python版本、操作系统等）

## 已知限制

1. **WGAN-GP训练**: 需要较多内存，建议至少8GB
2. **大数据集**: 超过10万样本可能需要调整参数
3. **Windows路径**: 确保路径中没有中文字符
4. **Python版本**: 建议使用Python 3.7-3.10

## 更新日志

- **v1.0.1**: 修复CUDA设备兼容性问题
- **v1.0.1**: 修复ADASYN参数名称问题
- **v1.0.1**: 添加CPU强制模式
- **v1.0.1**: 改进错误处理和日志记录
