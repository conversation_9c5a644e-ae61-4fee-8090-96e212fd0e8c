"""
DDAG-WGAN Implementation for Yeast Dataset
基于论文架构的DDAG-WGAN方法实现，结合GA优化ADASYN超参数和WGAN训练
包含生成器和判别器的完整架构，支持十折交叉验证和损失函数可视化
"""

import os
import sys
import numpy as np
import pandas as pd
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
import seaborn as sns
import torch
import torch.nn as nn
import torch.optim as optim
from sklearn.model_selection import StratifiedKFold, train_test_split
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import f1_score, roc_auc_score, classification_report, confusion_matrix
from imblearn.over_sampling import ADASYN
import warnings
warnings.filterwarnings('ignore')

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from genetic_algorithm import GeneticAlgorithm
from config import ga_config, param_ranges
import time

def geometric_mean_score(y_true, y_pred):
    """计算G-mean (几何平均数)"""
    from sklearn.metrics import confusion_matrix
    cm = confusion_matrix(y_true, y_pred)
    if cm.shape == (2, 2):
        tn, fp, fn, tp = cm.ravel()
        sensitivity = tp / (tp + fn) if (tp + fn) > 0 else 0
        specificity = tn / (tn + fp) if (tn + fp) > 0 else 0
        return np.sqrt(sensitivity * specificity)
    return 0

class DDAGGenerator(nn.Module):
    """
    DDAG-WGAN生成器架构
    基于论文图2的生成器结构：全连接层(128) -> 全连接层(256) -> 全连接层(512) -> 全连接层(1024) -> 输出层
    使用ReLU激活函数和Batch Normalization，最后一层使用Tanh激活
    """
    def __init__(self, latent_dim, data_dim):
        super(DDAGGenerator, self).__init__()
        
        self.latent_dim = latent_dim
        self.data_dim = data_dim
        
        # 基于论文架构的生成器网络
        self.model = nn.Sequential(
            # 第一层：全连接层(128) + ReLU
            nn.Linear(latent_dim, 128),
            nn.ReLU(inplace=True),
            nn.BatchNorm1d(128),
            
            # 第二层：全连接层(256) + ReLU  
            nn.Linear(128, 256),
            nn.ReLU(inplace=True),
            nn.BatchNorm1d(256),
            
            # 第三层：全连接层(512) + ReLU
            nn.Linear(256, 512),
            nn.ReLU(inplace=True),
            nn.BatchNorm1d(512),
            
            # 第四层：全连接层(1024) + ReLU
            nn.Linear(512, 1024),
            nn.ReLU(inplace=True),
            nn.BatchNorm1d(1024),
            
            # 输出层：Tanh激活
            nn.Linear(1024, data_dim),
            nn.Tanh()
        )
        
        # 权重初始化
        self.apply(self._init_weights)
    
    def _init_weights(self, m):
        """权重初始化"""
        if isinstance(m, nn.Linear):
            nn.init.normal_(m.weight, 0.0, 0.02)
            if m.bias is not None:
                nn.init.constant_(m.bias, 0)
        elif isinstance(m, nn.BatchNorm1d):
            nn.init.normal_(m.weight, 1.0, 0.02)
            nn.init.constant_(m.bias, 0)
    
    def forward(self, z):
        return self.model(z)

class DDAGDiscriminator(nn.Module):
    """
    DDAG-WGAN判别器架构
    基于论文图3的判别器结构：全连接层(512) -> 全连接层(256) -> 全连接层(128) -> 输出层
    使用Leaky ReLU激活函数，无Batch Normalization
    """
    def __init__(self, data_dim):
        super(DDAGDiscriminator, self).__init__()
        
        self.data_dim = data_dim
        
        # 基于论文架构的判别器网络
        self.model = nn.Sequential(
            # 第一层：全连接层(512) + Leaky ReLU
            nn.Linear(data_dim, 512),
            nn.LeakyReLU(0.2, inplace=True),
            
            # 第二层：全连接层(256) + Leaky ReLU
            nn.Linear(512, 256),
            nn.LeakyReLU(0.2, inplace=True),
            
            # 第三层：全连接层(128) + Leaky ReLU
            nn.Linear(256, 128),
            nn.LeakyReLU(0.2, inplace=True),
            
            # 输出层：真假判别
            nn.Linear(128, 1)
        )
        
        # 权重初始化
        self.apply(self._init_weights)
    
    def _init_weights(self, m):
        """权重初始化"""
        if isinstance(m, nn.Linear):
            nn.init.normal_(m.weight, 0.0, 0.02)
            if m.bias is not None:
                nn.init.constant_(m.bias, 0)
    
    def forward(self, x):
        return self.model(x)

class DDAG_WGAN:
    """
    DDAG-WGAN完整实现
    结合ADASYN过采样和WGAN-GP生成对抗网络的混合方法
    """
    def __init__(self, latent_dim, data_dim, lambda_gp=10, lr=1e-4, 
                 batch_size=64, n_critic=5, force_cpu=False):
        
        self.latent_dim = latent_dim
        self.data_dim = data_dim
        self.lambda_gp = lambda_gp
        self.lr = lr
        self.batch_size = batch_size
        self.n_critic = n_critic
        
        # 设备选择
        if force_cpu:
            self.device = torch.device('cpu')
        else:
            self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"使用设备: {self.device}")
        
        # 初始化网络
        self.generator = DDAGGenerator(latent_dim, data_dim).to(self.device)
        self.discriminator = DDAGDiscriminator(data_dim).to(self.device)
        
        # 优化器
        self.g_optimizer = optim.Adam(self.generator.parameters(), 
                                     lr=lr, betas=(0.0, 0.9))
        self.d_optimizer = optim.Adam(self.discriminator.parameters(), 
                                     lr=lr, betas=(0.0, 0.9))
        
        # 损失记录
        self.d_losses = []
        self.g_losses = []
        self.w_distances = []
    
    def gradient_penalty(self, real_data, fake_data):
        """计算梯度惩罚"""
        batch_size = real_data.size(0)
        
        # 随机插值
        alpha = torch.rand(batch_size, 1).to(self.device)
        alpha = alpha.expand_as(real_data)
        
        interpolated = alpha * real_data + (1 - alpha) * fake_data
        interpolated = interpolated.requires_grad_(True)
        
        # 计算判别器输出
        d_interpolated = self.discriminator(interpolated)
        
        # 计算梯度
        gradients = torch.autograd.grad(
            outputs=d_interpolated,
            inputs=interpolated,
            grad_outputs=torch.ones_like(d_interpolated),
            create_graph=True,
            retain_graph=True,
            only_inputs=True
        )[0]
        
        # 梯度惩罚
        gradients = gradients.view(batch_size, -1)
        gradient_penalty = ((gradients.norm(2, dim=1) - 1) ** 2).mean()
        
        return gradient_penalty
    
    def train_discriminator(self, real_data):
        """训练判别器"""
        self.d_optimizer.zero_grad()
        
        batch_size = real_data.size(0)
        
        # 真实数据的判别器输出
        real_output = self.discriminator(real_data)
        
        # 生成假数据
        z = torch.randn(batch_size, self.latent_dim).to(self.device)
        fake_data = self.generator(z).detach()
        fake_output = self.discriminator(fake_data)
        
        # Wasserstein损失
        w_distance = real_output.mean() - fake_output.mean()
        
        # 梯度惩罚
        gp = self.gradient_penalty(real_data, fake_data)
        
        # 判别器损失
        d_loss = -w_distance + self.lambda_gp * gp
        
        d_loss.backward()
        self.d_optimizer.step()
        
        return d_loss.item(), w_distance.item()
    
    def train_generator(self):
        """训练生成器"""
        self.g_optimizer.zero_grad()
        
        # 生成假数据
        z = torch.randn(self.batch_size, self.latent_dim).to(self.device)
        fake_data = self.generator(z)
        fake_output = self.discriminator(fake_data)
        
        # 生成器损失
        g_loss = -fake_output.mean()
        
        g_loss.backward()
        self.g_optimizer.step()
        
        return g_loss.item()
    
    def generate_samples(self, n_samples):
        """生成样本"""
        self.generator.eval()
        with torch.no_grad():
            z = torch.randn(n_samples, self.latent_dim).to(self.device)
            samples = self.generator(z)
        self.generator.train()
        return samples.cpu().numpy()

def load_yeast_data():
    """加载Yeast数据集"""
    print("加载Yeast数据集...")
    
    # 数据文件路径
    data_path = "yeast.data"
    
    if not os.path.exists(data_path):
        print(f"数据文件 {data_path} 不存在，尝试从UCI下载...")
        try:
            import urllib.request
            url = "https://archive.ics.uci.edu/ml/machine-learning-databases/yeast/yeast.data"
            urllib.request.urlretrieve(url, data_path)
            print("数据下载成功")
        except Exception as e:
            print(f"数据下载失败: {e}")
            return None, None, 0, 0, 0
    
    # 读取数据
    column_names = ['sequence_name', 'mcg', 'gvh', 'alm', 'mit', 'erl', 'pox', 'vac', 'nuc', 'class']
    df = pd.read_csv(data_path, sep='\s+', names=column_names)
    
    # 移除序列名称列
    df = df.drop('sequence_name', axis=1)
    
    # 特征和标签分离
    X = df.drop('class', axis=1).values
    y_original = df['class'].values
    
    # 创建二分类标签：GOL+POX+VAC作为少数类(1)，其他作为多数类(0)
    minority_classes = ['GOL', 'POX', 'VAC']
    y = np.where(np.isin(y_original, minority_classes), 1, 0)
    
    # 数据标准化
    scaler = StandardScaler()
    X = scaler.fit_transform(X)
    
    # 统计信息
    minority_count = np.sum(y == 1)
    majority_count = np.sum(y == 0)
    imbalance_ratio = majority_count / minority_count
    
    print(f"数据集加载完成:")
    print(f"  总样本数: {len(X)}")
    print(f"  特征维度: {X.shape[1]}")
    print(f"  少数类样本: {minority_count}")
    print(f"  多数类样本: {majority_count}")
    print(f"  不平衡比例: {imbalance_ratio:.2f}:1")
    
    return X, y, minority_count, majority_count, imbalance_ratio

def generate_balanced_data_ddag_wgan(X_train, y_train, k, alpha, lambda_gp, n_critic, lr, batch_size):
    """
    使用DDAG-WGAN方法生成平衡数据
    结合ADASYN过采样和WGAN-GP生成对抗网络
    """
    try:
        # 提取少数类和多数类
        X_min = X_train[y_train == 1]
        X_maj = X_train[y_train == 0]
        N_min = len(X_min)
        N_maj = len(X_maj)
        G_total = N_maj - N_min

        print(f"原始数据: 多数类{N_maj}, 少数类{N_min}")

        # 第一阶段：ADASYN生成
        G_adasyn = int(alpha * G_total)
        X_synthetic = np.zeros((0, X_train.shape[1]))

        if G_adasyn > 0:
            try:
                adasyn = ADASYN(
                    sampling_strategy={1: N_min + G_adasyn},
                    n_neighbors=min(k, N_min-1) if N_min > 1 else 1,
                    random_state=42
                )
                X_adasyn_res, y_adasyn_res = adasyn.fit_resample(X_train, y_train)
                X_adasyn_only = X_adasyn_res[len(X_train):]
                X_synthetic = X_adasyn_only
                print(f"ADASYN生成: {len(X_adasyn_only)} 个样本")
            except Exception as e:
                print(f"ADASYN生成失败: {e}")

        # 第二阶段：WGAN-GP生成
        G_wgan = G_total - G_adasyn
        losses = {'d_losses': [], 'g_losses': [], 'w_distances': []}

        if G_wgan > 0:
            print(f"开始WGAN-GP训练，目标生成 {G_wgan} 个样本...")

            # 准备训练数据（少数类 + ADASYN生成的样本）
            if len(X_synthetic) > 0:
                X_train_data = np.vstack([X_min, X_synthetic])
            else:
                X_train_data = X_min

            # 转换为tensor
            X_tensor = torch.FloatTensor(X_train_data)

            # 初始化DDAG-WGAN
            ddag_wgan = DDAG_WGAN(
                latent_dim=min(100, X_train.shape[1] * 2),
                data_dim=X_train.shape[1],
                lambda_gp=lambda_gp,
                lr=lr,
                batch_size=min(batch_size, len(X_train_data)),
                n_critic=n_critic,
                force_cpu=True  # 强制使用CPU以确保稳定性
            )

            # 训练WGAN-GP
            n_epochs = 200  # 训练轮数
            dataset = torch.utils.data.TensorDataset(X_tensor)
            dataloader = torch.utils.data.DataLoader(
                dataset,
                batch_size=ddag_wgan.batch_size,
                shuffle=True,
                drop_last=True
            )

            print("开始DDAG-WGAN训练...")
            for epoch in range(n_epochs):
                epoch_d_losses = []
                epoch_g_losses = []
                epoch_w_distances = []

                for batch_idx, (real_data,) in enumerate(dataloader):
                    real_data = real_data.to(ddag_wgan.device)

                    # 训练判别器
                    for _ in range(ddag_wgan.n_critic):
                        d_loss, w_distance = ddag_wgan.train_discriminator(real_data)
                        epoch_d_losses.append(d_loss)
                        epoch_w_distances.append(w_distance)

                    # 训练生成器
                    g_loss = ddag_wgan.train_generator()
                    epoch_g_losses.append(g_loss)

                # 记录平均损失
                if epoch_d_losses and epoch_g_losses:
                    avg_d_loss = np.mean(epoch_d_losses)
                    avg_g_loss = np.mean(epoch_g_losses)
                    avg_w_distance = np.mean(epoch_w_distances)

                    losses['d_losses'].append(avg_d_loss)
                    losses['g_losses'].append(avg_g_loss)
                    losses['w_distances'].append(avg_w_distance)

                # 每20轮显示进度
                if (epoch + 1) % 20 == 0:
                    print(f"Epoch {epoch+1}/{n_epochs}: D_loss={avg_d_loss:.4f}, G_loss={avg_g_loss:.4f}, W_dist={avg_w_distance:.4f}")

            # 生成样本
            print(f"生成 {G_wgan} 个WGAN样本...")
            X_wgan_samples = ddag_wgan.generate_samples(G_wgan)

            # 合并所有合成样本
            if len(X_synthetic) > 0:
                X_synthetic = np.vstack([X_synthetic, X_wgan_samples])
            else:
                X_synthetic = X_wgan_samples

            print(f"WGAN-GP生成: {len(X_wgan_samples)} 个样本")

        # 创建平衡数据集
        if len(X_synthetic) > 0:
            X_balanced = np.vstack([X_train, X_synthetic])
            y_balanced = np.hstack([y_train, np.ones(len(X_synthetic), dtype=y_train.dtype)])
        else:
            X_balanced = X_train
            y_balanced = y_train

        print(f"最终平衡数据集: {len(X_balanced)} 个样本")
        print(f"类别分布: {np.bincount(y_balanced)}")

        return X_balanced, y_balanced, losses

    except Exception as e:
        print(f"DDAG-WGAN生成失败: {e}")
        return X_train, y_train, {'d_losses': [], 'g_losses': [], 'w_distances': []}

def plot_training_losses(losses, save_path='ddag_wgan_training_losses.png'):
    """绘制DDAG-WGAN训练损失函数图"""
    print(f"\n📊 绘制DDAG-WGAN训练损失函数图...")

    if not losses or 'd_losses' not in losses or 'g_losses' not in losses:
        print("❌ 没有找到损失函数数据")
        return

    d_losses = losses['d_losses']
    g_losses = losses['g_losses']

    if len(d_losses) == 0 or len(g_losses) == 0:
        print("❌ 损失函数数据为空")
        return

    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False

    # 创建图形
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(14, 10))

    # 训练次数
    epochs = range(1, len(d_losses) + 1)

    # 绘制判别器损失
    ax1.plot(epochs, d_losses, color='#1f77b4', linewidth=2, label='判别器损失', alpha=0.8)
    ax1.set_xlabel('训练轮数', fontsize=12)
    ax1.set_ylabel('判别器损失值', fontsize=12)
    ax1.set_title('DDAG-WGAN判别器训练损失函数', fontsize=14, fontweight='bold')
    ax1.grid(True, alpha=0.3)
    ax1.legend(fontsize=11)

    # 绘制生成器损失
    ax2.plot(epochs, g_losses, color='#ff7f0e', linewidth=2, label='生成器损失', alpha=0.8)
    ax2.set_xlabel('训练轮数', fontsize=12)
    ax2.set_ylabel('生成器损失值', fontsize=12)
    ax2.set_title('DDAG-WGAN生成器训练损失函数', fontsize=14, fontweight='bold')
    ax2.grid(True, alpha=0.3)
    ax2.legend(fontsize=11)

    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()

    print(f"✅ 损失函数图已保存至: {save_path}")

def evaluate_with_cross_validation(X, y, method_name="DDAG-WGAN", n_folds=10):
    """使用十折交叉验证评估方法性能"""
    print(f"\n🔄 开始{method_name}方法的{n_folds}折交叉验证...")

    # 初始化结果存储
    results = {
        'f1_scores': [],
        'auc_scores': [],
        'gmean_scores': [],
        'precision_scores': [],
        'recall_scores': []
    }

    # 十折交叉验证
    skf = StratifiedKFold(n_splits=n_folds, shuffle=True, random_state=42)

    for fold, (train_idx, test_idx) in enumerate(skf.split(X, y)):
        print(f"  处理第 {fold+1}/{n_folds} 折...")

        # 划分训练集和测试集
        X_train, X_test = X[train_idx], X[test_idx]
        y_train, y_test = y[train_idx], y[test_idx]

        try:
            # 使用DDAG-WGAN生成平衡数据
            # 使用优化的参数（这里使用默认值，实际应该通过GA优化）
            X_balanced, y_balanced, losses = generate_balanced_data_ddag_wgan(
                X_train, y_train,
                k=5,           # ADASYN邻居数
                alpha=0.7,     # ADASYN生成比例
                lambda_gp=10,  # 梯度惩罚系数
                n_critic=5,    # 判别器训练次数
                lr=1e-4,       # 学习率
                batch_size=32  # 批量大小
            )

            # 训练随机森林分类器
            rf = RandomForestClassifier(n_estimators=100, random_state=42)
            rf.fit(X_balanced, y_balanced)

            # 在测试集上预测
            y_pred = rf.predict(X_test)
            y_pred_proba = rf.predict_proba(X_test)[:, 1]

            # 计算评估指标
            f1 = f1_score(y_test, y_pred)
            auc = roc_auc_score(y_test, y_pred_proba)
            gmean = geometric_mean_score(y_test, y_pred)

            from sklearn.metrics import precision_score, recall_score
            precision = precision_score(y_test, y_pred)
            recall = recall_score(y_test, y_pred)

            # 存储结果
            results['f1_scores'].append(f1)
            results['auc_scores'].append(auc)
            results['gmean_scores'].append(gmean)
            results['precision_scores'].append(precision)
            results['recall_scores'].append(recall)

            print(f"    F1: {f1:.4f}, AUC: {auc:.4f}, G-mean: {gmean:.4f}")

        except Exception as e:
            print(f"    第 {fold+1} 折处理失败: {e}")
            # 添加默认值以保持一致性
            results['f1_scores'].append(0.0)
            results['auc_scores'].append(0.5)
            results['gmean_scores'].append(0.0)
            results['precision_scores'].append(0.0)
            results['recall_scores'].append(0.0)

    # 计算平均结果和标准差
    print(f"\n📊 {method_name}方法{n_folds}折交叉验证结果:")
    print("=" * 60)

    for metric_name, scores in results.items():
        mean_score = np.mean(scores)
        std_score = np.std(scores)
        print(f"{metric_name.replace('_', ' ').title():<15}: {mean_score:.4f} ± {std_score:.4f}")

    return results

def ddag_wgan_fitness_function(params, X_train, y_train, X_val, y_val):
    """
    DDAG-WGAN方法的适应度函数
    用于遗传算法优化ADASYN和WGAN-GP参数
    """
    try:
        # 解析参数
        k = int(params[0])           # ADASYN邻居数
        alpha = params[1]            # ADASYN生成比例
        lambda_gp = params[2]        # 梯度惩罚系数
        n_critic = int(params[3])    # 判别器训练次数
        lr = params[4]               # 学习率
        batch_size = int(params[5])  # 批量大小

        # 生成平衡数据
        X_balanced, y_balanced, _ = generate_balanced_data_ddag_wgan(
            X_train, y_train, k, alpha, lambda_gp, n_critic, lr, batch_size
        )

        # 训练分类器
        rf = RandomForestClassifier(n_estimators=50, random_state=42)  # 减少树的数量以加快训练
        rf.fit(X_balanced, y_balanced)

        # 在验证集上预测
        y_pred = rf.predict(X_val)
        y_pred_proba = rf.predict_proba(X_val)[:, 1]

        # 计算评估指标
        f1 = f1_score(y_val, y_pred)
        auc = roc_auc_score(y_val, y_pred_proba)
        gmean = geometric_mean_score(y_val, y_pred)

        # 综合适应度（加权组合）
        fitness = 0.4 * f1 + 0.3 * auc + 0.3 * gmean

        return max(0.0, fitness)

    except Exception as e:
        print(f"适应度函数执行失败: {e}")
        return 0.0

def optimize_ddag_wgan_parameters(X_train, y_train, X_val, y_val):
    """使用遗传算法优化DDAG-WGAN参数"""
    print("\n🧬 开始遗传算法优化DDAG-WGAN参数...")

    # 定义参数范围
    param_ranges = {
        'k': (3, 15),           # ADASYN邻居数
        'alpha': (0.3, 0.9),    # ADASYN生成比例
        'lambda_gp': (5, 20),   # 梯度惩罚系数
        'n_critic': (3, 8),     # 判别器训练次数
        'lr': (1e-5, 1e-3),     # 学习率
        'batch_size': (16, 64)  # 批量大小
    }

    # 初始化遗传算法
    ga = GeneticAlgorithm(
        param_ranges=param_ranges,
        fitness_func=ddag_wgan_fitness_function,
        population_size=20,      # 减小种群大小以加快优化
        max_generations=30,      # 减少代数
        crossover_rate=0.8,
        mutation_rate=0.1,
        elite_size=2,
        tournament_size=3
    )

    # 执行优化
    best_individual, optimization_result = ga.optimize_with_progress(
        X_train, y_train, X_val, y_val
    )

    # 解析最优参数
    best_params = {
        'k': int(best_individual.genes[0]),
        'alpha': best_individual.genes[1],
        'lambda_gp': best_individual.genes[2],
        'n_critic': int(best_individual.genes[3]),
        'lr': best_individual.genes[4],
        'batch_size': int(best_individual.genes[5])
    }

    print(f"\n✅ 遗传算法优化完成!")
    print(f"最佳适应度: {best_individual.fitness:.4f}")
    print("最优参数:")
    for param, value in best_params.items():
        print(f"  {param}: {value}")

    return best_params, optimization_result

def main():
    """主函数：运行DDAG-WGAN实验"""
    print("=" * 80)
    print("DDAG-WGAN方法用于Yeast数据集不平衡分类实验")
    print("=" * 80)

    # 1. 加载数据
    X, y, minority_count, majority_count, imbalance_ratio = load_yeast_data()
    if X is None:
        print("数据加载失败，退出程序")
        return

    # 2. 数据划分
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.3, stratify=y, random_state=42
    )

    # 进一步划分训练集和验证集用于GA优化
    X_train_ga, X_val_ga, y_train_ga, y_val_ga = train_test_split(
        X_train, y_train, test_size=0.2, stratify=y_train, random_state=42
    )

    print(f"\n数据划分:")
    print(f"  GA训练集: {len(X_train_ga)} 样本")
    print(f"  GA验证集: {len(X_val_ga)} 样本")
    print(f"  最终测试集: {len(X_test)} 样本")

    # 3. 遗传算法优化参数
    print("\n" + "="*60)
    print("第一阶段：遗传算法优化DDAG-WGAN参数")
    print("="*60)

    best_params, optimization_result = optimize_ddag_wgan_parameters(
        X_train_ga, y_train_ga, X_val_ga, y_val_ga
    )

    # 4. 使用最优参数进行十折交叉验证
    print("\n" + "="*60)
    print("第二阶段：使用最优参数进行十折交叉验证")
    print("="*60)

    # 使用最优参数生成平衡数据并进行交叉验证
    print(f"使用最优参数: {best_params}")

    # 在完整训练集上使用最优参数
    X_balanced, y_balanced, losses = generate_balanced_data_ddag_wgan(
        X_train, y_train,
        k=best_params['k'],
        alpha=best_params['alpha'],
        lambda_gp=best_params['lambda_gp'],
        n_critic=best_params['n_critic'],
        lr=best_params['lr'],
        batch_size=best_params['batch_size']
    )

    # 5. 绘制训练损失函数图
    if losses['d_losses'] and losses['g_losses']:
        plot_training_losses(losses, 'yeast_ddag_wgan_training_losses.png')

    # 6. 十折交叉验证评估
    cv_results = evaluate_with_cross_validation(X, y, "GA优化DDAG-WGAN", n_folds=10)

    # 7. 最终测试集评估
    print("\n" + "="*60)
    print("第三阶段：最终测试集评估")
    print("="*60)

    # 训练最终模型
    rf_final = RandomForestClassifier(n_estimators=100, random_state=42)
    rf_final.fit(X_balanced, y_balanced)

    # 在测试集上预测
    y_test_pred = rf_final.predict(X_test)
    y_test_pred_proba = rf_final.predict_proba(X_test)[:, 1]

    # 计算最终指标
    final_f1 = f1_score(y_test, y_test_pred)
    final_auc = roc_auc_score(y_test, y_test_pred_proba)
    final_gmean = geometric_mean_score(y_test, y_test_pred)

    print(f"最终测试集结果:")
    print(f"  F1-Score: {final_f1:.4f}")
    print(f"  AUC-ROC:  {final_auc:.4f}")
    print(f"  G-mean:   {final_gmean:.4f}")

    # 8. 保存结果
    results_summary = {
        'method': 'GA优化DDAG-WGAN',
        'best_params': best_params,
        'cv_results': cv_results,
        'final_test_results': {
            'f1_score': final_f1,
            'auc_score': final_auc,
            'gmean_score': final_gmean
        }
    }

    # 保存到CSV文件
    cv_df = pd.DataFrame({
        'F1_Score': cv_results['f1_scores'],
        'AUC_Score': cv_results['auc_scores'],
        'G_mean': cv_results['gmean_scores'],
        'Precision': cv_results['precision_scores'],
        'Recall': cv_results['recall_scores']
    })
    cv_df.to_csv('yeast_ddag_wgan_cv_results.csv', index=False)

    print(f"\n✅ 实验完成！")
    print(f"交叉验证结果已保存至: yeast_ddag_wgan_cv_results.csv")
    print(f"训练损失图已保存至: yeast_ddag_wgan_training_losses.png")

    return results_summary

if __name__ == "__main__":
    main()
