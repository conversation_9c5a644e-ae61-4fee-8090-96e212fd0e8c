"""
配置文件：定义遗传算法优化ADASYN和WGAN-GP参数的所有配置参数
"""

import numpy as np

class GAConfig:
    """遗传算法配置"""
    # 种群参数
    POPULATION_SIZE = 50
    MAX_GENERATIONS = 100
    ELITE_SIZE = 5
    TOURNAMENT_SIZE = 3
    
    # 交叉和变异参数
    CROSSOVER_RATE = 0.8
    MUTATION_RATE = 0.1
    ETA_C = 20  # SBX交叉分布指数
    ETA_M = 15  # 多项式变异分布指数
    
    # 早停参数
    EARLY_STOP_PATIENCE = 20
    EARLY_STOP_THRESHOLD = 0.001
    
    # 随机种子
    RANDOM_SEED = 42

class ParameterRanges:
    """参数搜索空间定义"""
    # ADASYN参数
    ADASYN_K_MIN = 3
    ADASYN_K_MAX = 20
    ADASYN_ALPHA_MIN = 0.5
    ADASYN_ALPHA_MAX = 1.0
    
    # WGAN-GP参数
    WGAN_LAMBDA_MIN = 1.0
    WGAN_LAMBDA_MAX = 20.0
    WGAN_N_CRITIC_MIN = 1
    WGAN_N_CRITIC_MAX = 10
    WGAN_LR_MIN = 1e-5
    WGAN_LR_MAX = 1e-3
    WGAN_BATCH_SIZES = [32, 64, 128, 256]
    
    @classmethod
    def get_param_bounds(cls):
        """返回参数边界列表"""
        return [
            (cls.ADASYN_K_MIN, cls.ADASYN_K_MAX),      # k (整数)
            (cls.ADASYN_ALPHA_MIN, cls.ADASYN_ALPHA_MAX),  # α (连续)
            (cls.WGAN_LAMBDA_MIN, cls.WGAN_LAMBDA_MAX),    # λ (连续)
            (cls.WGAN_N_CRITIC_MIN, cls.WGAN_N_CRITIC_MAX), # n_critic (整数)
            (np.log10(cls.WGAN_LR_MIN), np.log10(cls.WGAN_LR_MAX)), # log(lr) (连续)
            (0, len(cls.WGAN_BATCH_SIZES)-1)  # batch_size索引 (整数)
        ]
    
    @classmethod
    def get_param_types(cls):
        """返回参数类型列表 ('int' 或 'float')"""
        return ['int', 'float', 'float', 'int', 'float', 'int']

class WGANConfig:
    """WGAN-GP训练配置"""
    LATENT_DIM = 100
    EPOCHS = 100
    GENERATOR_HIDDEN_DIMS = [128, 256, 128]
    DISCRIMINATOR_HIDDEN_DIMS = [128, 256, 128]
    BETA1 = 0.0
    BETA2 = 0.9
    FORCE_CPU = False  # 设置为True强制使用CPU，避免CUDA问题
    
class ExperimentConfig:
    """实验配置"""
    # 数据划分
    TRAIN_TEST_SPLIT = 0.7
    TRAIN_VAL_SPLIT = 0.7  # 在训练集中再划分
    
    # 评估指标权重
    F1_WEIGHT = 0.6
    AUC_WEIGHT = 0.3
    GMEAN_WEIGHT = 0.1
    STABILITY_PENALTY = 0.3  # 稳定性惩罚系数
    
    # 交叉验证
    CV_FOLDS = 5
    CV_REPEATS = 3
    
    # 合成质量评估阈值
    KL_DIVERGENCE_THRESHOLD = 0.05
    WASSERSTEIN_DISTANCE_THRESHOLD = 0.1
    
    # 分类器配置
    CLASSIFIER_TYPE = 'rf'  # 'rf', 'svm', 'lr', 'nn'
    RF_N_ESTIMATORS = 100
    RF_MAX_DEPTH = 10
    
    # 结果保存
    SAVE_RESULTS = True
    RESULTS_DIR = 'results'
    SAVE_PLOTS = True
    
class DatasetConfig:
    """数据集配置"""
    DATASETS = {
        'credit': '../data/credit.data',
        'wisconsin': '../data/wisconsin.data',
        'ecoli': '../data/ecoli.data',
        'glass': '../data/glass.data',
        'yeast': '../data/yeast.data',
        'car': '../data/car.data',
        'statlog': '../data/statlog.data'
    }
    
    # 数据预处理
    NORMALIZE_FEATURES = True
    HANDLE_MISSING_VALUES = True
    FEATURE_SELECTION = False
    
class LoggingConfig:
    """日志配置"""
    LOG_LEVEL = 'INFO'
    LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    LOG_FILE = 'ga_optimization.log'
    CONSOLE_OUTPUT = True
    
    # 详细输出控制
    VERBOSE_GA = True
    VERBOSE_WGAN = False
    VERBOSE_EVALUATION = True
    
    # 进度报告频率
    REPORT_FREQUENCY = 10  # 每10代报告一次
    SAVE_FREQUENCY = 20    # 每20代保存一次中间结果

# 全局配置实例
ga_config = GAConfig()
param_ranges = ParameterRanges()
wgan_config = WGANConfig()
exp_config = ExperimentConfig()
dataset_config = DatasetConfig()
logging_config = LoggingConfig()
