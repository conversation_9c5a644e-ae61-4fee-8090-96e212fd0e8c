"""
WGAN-GP (Wasserstein GAN with Gradient Penalty) 实现
用于生成高质量的少数类样本
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from torch.autograd import grad
import logging

logger = logging.getLogger(__name__)

class Generator(nn.Module):
    """生成器网络"""
    def __init__(self, latent_dim, output_dim, hidden_dims=[128, 256, 128]):
        super(Generator, self).__init__()
        
        layers = []
        input_dim = latent_dim
        
        # 隐藏层
        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(input_dim, hidden_dim),
                nn.BatchNorm1d(hidden_dim),
                nn.ReLU(inplace=True)
            ])
            input_dim = hidden_dim
        
        # 输出层
        layers.append(nn.Linear(input_dim, output_dim))
        layers.append(nn.Tanh())  # 输出范围[-1, 1]
        
        self.model = nn.Sequential(*layers)
    
    def forward(self, z):
        return self.model(z)

class Discriminator(nn.Module):
    """判别器网络"""
    def __init__(self, input_dim, hidden_dims=[128, 256, 128]):
        super(Discriminator, self).__init__()
        
        layers = []
        current_dim = input_dim
        
        # 隐藏层
        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(current_dim, hidden_dim),
                nn.LeakyReLU(0.2, inplace=True),
                nn.Dropout(0.3)
            ])
            current_dim = hidden_dim
        
        # 输出层 (不使用sigmoid，因为WGAN使用Wasserstein距离)
        layers.append(nn.Linear(current_dim, 1))
        
        self.model = nn.Sequential(*layers)
    
    def forward(self, x):
        return self.model(x)

class WGAN_GP:
    """WGAN-GP模型"""
    def __init__(self, latent_dim, data_dim, lambda_gp=10, lr=1e-4,
                 batch_size=64, n_critic=5, beta1=0.0, beta2=0.9,
                 generator_hidden_dims=[128, 256, 128],
                 discriminator_hidden_dims=[128, 256, 128],
                 force_cpu=False):

        self.latent_dim = latent_dim
        self.data_dim = data_dim
        self.lambda_gp = lambda_gp
        self.lr = lr
        self.batch_size = batch_size
        self.n_critic = n_critic

        # 检查CUDA可用性
        if force_cpu:
            self.device = torch.device('cpu')
        else:
            self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        logger.info(f"Using device: {self.device}")
        
        # 初始化网络
        self.generator = Generator(latent_dim, data_dim, generator_hidden_dims).to(self.device)
        self.discriminator = Discriminator(data_dim, discriminator_hidden_dims).to(self.device)
        
        # 优化器
        self.g_optimizer = optim.Adam(self.generator.parameters(), lr=lr, betas=(beta1, beta2))
        self.d_optimizer = optim.Adam(self.discriminator.parameters(), lr=lr, betas=(beta1, beta2))
        
        # 训练历史
        self.d_losses = []
        self.g_losses = []
        self.w_distances = []
        
    def gradient_penalty(self, real_data, fake_data):
        """计算梯度惩罚"""
        batch_size = real_data.size(0)
        
        # 随机插值
        alpha = torch.rand(batch_size, 1).to(self.device)
        alpha = alpha.expand_as(real_data)
        
        interpolated = alpha * real_data + (1 - alpha) * fake_data
        interpolated = interpolated.to(self.device)
        interpolated.requires_grad_(True)
        
        # 计算判别器输出
        d_interpolated = self.discriminator(interpolated)
        
        # 计算梯度
        gradients = grad(
            outputs=d_interpolated,
            inputs=interpolated,
            grad_outputs=torch.ones_like(d_interpolated).to(self.device),
            create_graph=True,
            retain_graph=True,
            only_inputs=True
        )[0]
        
        # 梯度惩罚
        gradients = gradients.view(batch_size, -1)
        gradient_penalty = ((gradients.norm(2, dim=1) - 1) ** 2).mean()
        
        return gradient_penalty
    
    def train_discriminator(self, real_data):
        """训练判别器"""
        self.d_optimizer.zero_grad()
        
        batch_size = real_data.size(0)
        
        # 真实数据的判别器输出
        real_output = self.discriminator(real_data)
        
        # 生成假数据
        z = torch.randn(batch_size, self.latent_dim).to(self.device)
        fake_data = self.generator(z).detach()
        fake_output = self.discriminator(fake_data)
        
        # Wasserstein损失
        w_distance = real_output.mean() - fake_output.mean()
        
        # 梯度惩罚
        gp = self.gradient_penalty(real_data, fake_data)
        
        # 判别器损失
        d_loss = -w_distance + self.lambda_gp * gp
        
        d_loss.backward()
        self.d_optimizer.step()
        
        return d_loss.item(), w_distance.item()
    
    def train_generator(self):
        """训练生成器"""
        self.g_optimizer.zero_grad()
        
        # 生成假数据
        z = torch.randn(self.batch_size, self.latent_dim).to(self.device)
        fake_data = self.generator(z)
        fake_output = self.discriminator(fake_data)
        
        # 生成器损失
        g_loss = -fake_output.mean()
        
        g_loss.backward()
        self.g_optimizer.step()
        
        return g_loss.item()
    
    def train(self, real_data, epochs=100):
        """训练WGAN-GP"""
        # 数据预处理
        if isinstance(real_data, np.ndarray):
            real_data = torch.FloatTensor(real_data)

        # 确保数据在正确的设备上
        real_data = real_data.to(self.device)

        # 数据标准化到[-1, 1]
        self.data_mean = real_data.mean(dim=0)
        self.data_std = real_data.std(dim=0) + 1e-8
        real_data = (real_data - self.data_mean) / self.data_std
        
        dataset = torch.utils.data.TensorDataset(real_data)
        dataloader = torch.utils.data.DataLoader(
            dataset, batch_size=self.batch_size, shuffle=True, drop_last=True
        )
        
        self.generator.train()
        self.discriminator.train()
        
        logger.info(f"开始训练WGAN-GP，epochs: {epochs}, batch_size: {self.batch_size}")
        
        for epoch in range(epochs):
            epoch_d_loss = 0
            epoch_g_loss = 0
            epoch_w_distance = 0
            num_batches = 0
            
            for batch_data, in dataloader:
                batch_data = batch_data.to(self.device)
                
                # 训练判别器
                d_loss, w_distance = self.train_discriminator(batch_data)
                epoch_d_loss += d_loss
                epoch_w_distance += w_distance
                
                # 每n_critic次训练一次生成器
                if num_batches % self.n_critic == 0:
                    g_loss = self.train_generator()
                    epoch_g_loss += g_loss
                
                num_batches += 1
            
            # 记录损失
            avg_d_loss = epoch_d_loss / num_batches
            avg_g_loss = epoch_g_loss / (num_batches // self.n_critic + 1)
            avg_w_distance = epoch_w_distance / num_batches
            
            self.d_losses.append(avg_d_loss)
            self.g_losses.append(avg_g_loss)
            self.w_distances.append(avg_w_distance)
            
            if epoch % 20 == 0:
                logger.debug(f"Epoch {epoch}: D_loss={avg_d_loss:.4f}, "
                           f"G_loss={avg_g_loss:.4f}, W_distance={avg_w_distance:.4f}")

    def train_with_progress(self, real_data, epochs=100):
        """训练WGAN-GP（带详细进度显示）"""
        # 数据预处理
        if isinstance(real_data, np.ndarray):
            real_data = torch.FloatTensor(real_data)

        # 确保数据在正确的设备上
        real_data = real_data.to(self.device)

        # 数据标准化到[-1, 1]
        self.data_mean = real_data.mean(dim=0)
        self.data_std = real_data.std(dim=0) + 1e-8
        real_data = (real_data - self.data_mean) / self.data_std

        dataset = torch.utils.data.TensorDataset(real_data)
        dataloader = torch.utils.data.DataLoader(
            dataset, batch_size=self.batch_size, shuffle=True, drop_last=True
        )

        self.generator.train()
        self.discriminator.train()

        print(f"开始WGAN-GP训练...")
        print(f"{'轮次':<6} {'D损失':<10} {'G损失':<10} {'W距离':<10} {'状态'}")
        print("-" * 50)

        for epoch in range(epochs):
            epoch_d_loss = 0
            epoch_g_loss = 0
            epoch_w_distance = 0
            num_batches = 0

            for batch_data, in dataloader:
                batch_data = batch_data.to(self.device)

                # 训练判别器
                d_loss, w_distance = self.train_discriminator(batch_data)
                epoch_d_loss += d_loss
                epoch_w_distance += w_distance

                # 每n_critic次训练一次生成器
                if num_batches % self.n_critic == 0:
                    g_loss = self.train_generator()
                    epoch_g_loss += g_loss

                num_batches += 1

            # 记录损失
            avg_d_loss = epoch_d_loss / num_batches
            avg_g_loss = epoch_g_loss / (num_batches // self.n_critic + 1)
            avg_w_distance = epoch_w_distance / num_batches

            self.d_losses.append(avg_d_loss)
            self.g_losses.append(avg_g_loss)
            self.w_distances.append(avg_w_distance)

            # 显示进度
            status = "训练中"
            if epoch % 10 == 0 or epoch == epochs - 1:
                # 检查收敛状态
                if epoch > 10:
                    recent_w_distances = self.w_distances[-10:]
                    w_distance_std = np.std(recent_w_distances)
                    if w_distance_std < 0.01:
                        status = "收敛中"
                    elif avg_w_distance < 0.1:
                        status = "良好"

                print(f"{epoch+1:<6} {avg_d_loss:<10.4f} {avg_g_loss:<10.4f} "
                      f"{avg_w_distance:<10.4f} {status}")

            # 每20轮显示详细信息
            if epoch % 20 == 0 and epoch > 0:
                print(f"  第{epoch+1}轮详细信息:")
                print(f"    判别器损失趋势: {np.mean(self.d_losses[-10:]):.4f}")
                print(f"    生成器损失趋势: {np.mean(self.g_losses[-10:]):.4f}")
                print(f"    Wasserstein距离趋势: {np.mean(self.w_distances[-10:]):.4f}")

        print(f"✓ WGAN-GP训练完成")
        print(f"  最终判别器损失: {self.d_losses[-1]:.4f}")
        print(f"  最终生成器损失: {self.g_losses[-1]:.4f}")
        print(f"  最终Wasserstein距离: {self.w_distances[-1]:.4f}")
    
    def generate_samples(self, n_samples):
        """生成样本"""
        self.generator.eval()
        
        generated_samples = []
        n_batches = (n_samples + self.batch_size - 1) // self.batch_size
        
        with torch.no_grad():
            for i in range(n_batches):
                current_batch_size = min(self.batch_size, n_samples - i * self.batch_size)
                z = torch.randn(current_batch_size, self.latent_dim).to(self.device)
                fake_data = self.generator(z)
                
                # 反标准化
                fake_data = fake_data * self.data_std + self.data_mean
                generated_samples.append(fake_data.cpu().numpy())
        
        return np.vstack(generated_samples)[:n_samples]
    
    def get_training_history(self):
        """获取训练历史"""
        return {
            'd_losses': self.d_losses,
            'g_losses': self.g_losses,
            'w_distances': self.w_distances
        }
    
    def save_model(self, filepath):
        """保存模型"""
        torch.save({
            'generator_state_dict': self.generator.state_dict(),
            'discriminator_state_dict': self.discriminator.state_dict(),
            'g_optimizer_state_dict': self.g_optimizer.state_dict(),
            'd_optimizer_state_dict': self.d_optimizer.state_dict(),
            'data_mean': self.data_mean,
            'data_std': self.data_std,
            'config': {
                'latent_dim': self.latent_dim,
                'data_dim': self.data_dim,
                'lambda_gp': self.lambda_gp,
                'lr': self.lr,
                'batch_size': self.batch_size,
                'n_critic': self.n_critic
            }
        }, filepath)
    
    def load_model(self, filepath):
        """加载模型"""
        checkpoint = torch.load(filepath, map_location=self.device)
        self.generator.load_state_dict(checkpoint['generator_state_dict'])
        self.discriminator.load_state_dict(checkpoint['discriminator_state_dict'])
        self.g_optimizer.load_state_dict(checkpoint['g_optimizer_state_dict'])
        self.d_optimizer.load_state_dict(checkpoint['d_optimizer_state_dict'])
        self.data_mean = checkpoint['data_mean']
        self.data_std = checkpoint['data_std']
