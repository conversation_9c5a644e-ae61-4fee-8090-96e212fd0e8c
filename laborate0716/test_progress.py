"""
测试GA优化ADASYN-WGAN-GP参数的进度显示功能
"""

import os
import sys
import numpy as np
import pandas as pd
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import LabelEncoder, StandardScaler
import warnings
warnings.filterwarnings('ignore')

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def load_car_data_simple():
    """简化的car数据加载"""
    print("加载Car数据集...")
    
    # 数据路径
    data_path = "C:/Users/<USER>/Desktop/GAAD/data/car.data"
    
    # 列名定义
    columns = ['buying', 'maint', 'doors', 'persons', 'lug_boot', 'safety', 'class']
    
    try:
        data = pd.read_csv(data_path, names=columns)
        print(f"✓ 数据加载成功，形状: {data.shape}")
    except FileNotFoundError:
        print(f"数据文件未找到，创建模拟数据...")
        # 创建模拟数据
        np.random.seed(42)
        n_samples = 500  # 减少样本数用于测试
        data = pd.DataFrame({
            'buying': np.random.choice(['vhigh', 'high', 'med', 'low'], n_samples),
            'maint': np.random.choice(['vhigh', 'high', 'med', 'low'], n_samples),
            'doors': np.random.choice(['2', '3', '4', '5more'], n_samples),
            'persons': np.random.choice(['2', '4', 'more'], n_samples),
            'lug_boot': np.random.choice(['small', 'med', 'big'], n_samples),
            'safety': np.random.choice(['low', 'med', 'high'], n_samples),
            'class': np.random.choice(['unacc', 'acc', 'good', 'vgood'], n_samples, 
                                    p=[0.7, 0.22, 0.065, 0.015])
        })
    
    print("原始类别分布:")
    print(data['class'].value_counts())
    
    # 处理特征编码
    X_encoded = data.drop('class', axis=1).copy()
    for col in X_encoded.columns:
        le = LabelEncoder()
        X_encoded[col] = le.fit_transform(X_encoded[col])
    
    # 处理标签：vgood作为少数类(1)，其他合并为多数类(0)
    y = (data['class'] == 'vgood').astype(int)
    
    # 标准化特征
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X_encoded)
    
    # 统计信息
    minority_count = np.sum(y == 1)
    majority_count = np.sum(y == 0)
    imbalance_ratio = majority_count / minority_count if minority_count > 0 else float('inf')
    
    print(f"\n数据集统计:")
    print(f"总样本数: {len(y)}")
    print(f"少数类(vgood)数目: {minority_count}")
    print(f"多数类数目: {majority_count}")
    print(f"不平衡比例: {imbalance_ratio:.2f}:1")
    
    return X_scaled, y

def test_ga_progress():
    """测试GA优化的进度显示"""
    print("=" * 80)
    print("测试GA优化ADASYN-WGAN-GP参数的进度显示")
    print("=" * 80)
    
    # 1. 加载数据
    X, y = load_car_data_simple()
    
    # 2. 数据划分
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.3, stratify=y, random_state=42
    )
    
    print(f"\n数据划分:")
    print(f"训练集: {len(X_train)} 样本 (少数类: {np.sum(y_train == 1)})")
    print(f"测试集: {len(X_test)} 样本 (少数类: {np.sum(y_test == 1)})")
    
    # 3. 运行GA优化ADASYN-WGAN-GP方法
    from car import method_ga_adasyn_wgan
    
    print(f"\n开始测试GA优化ADASYN-WGAN-GP参数...")
    
    try:
        X_balanced, y_balanced, losses, fitness = method_ga_adasyn_wgan(
            X_train, y_train, X_test, y_test
        )
        
        print(f"\n✓ 测试完成！")
        print(f"平衡后数据集: {len(X_balanced)} 样本")
        print(f"类别分布: {np.bincount(y_balanced.astype(int))}")
        print(f"最终适应度: {fitness:.4f}")
        
        # 显示训练损失信息
        if losses and len(losses.get('d_losses', [])) > 0:
            print(f"\nWGAN-GP训练损失:")
            print(f"  判别器损失: {losses['d_losses'][-1]:.4f}")
            print(f"  生成器损失: {losses['g_losses'][-1]:.4f}")
            print(f"  Wasserstein距离: {losses['w_distances'][-1]:.4f}")
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_individual_components():
    """测试各个组件的进度显示"""
    print("\n" + "=" * 60)
    print("测试各个组件的进度显示")
    print("=" * 60)
    
    # 测试WGAN-GP训练进度
    print("\n1. 测试WGAN-GP训练进度显示:")
    try:
        from wgan_gp import WGAN_GP
        from config import wgan_config
        
        # 创建简单测试数据
        np.random.seed(42)
        test_data = np.random.normal(0, 1, (50, 6))  # 50个样本，6个特征
        
        wgan_gp = WGAN_GP(
            latent_dim=wgan_config.LATENT_DIM,
            data_dim=6,
            lambda_gp=10.0,
            lr=1e-4,
            batch_size=16,
            n_critic=2,
            force_cpu=True
        )
        
        print("开始WGAN-GP训练测试...")
        wgan_gp.train_with_progress(test_data, epochs=10)  # 只训练10轮用于测试
        
        print("✓ WGAN-GP训练进度显示测试通过")
        
    except Exception as e:
        print(f"✗ WGAN-GP测试失败: {e}")
    
    # 测试遗传算法进度
    print("\n2. 测试遗传算法进度显示:")
    try:
        from genetic_algorithm import GeneticAlgorithm
        from fitness_function import fitness_function
        from config import param_ranges
        
        # 创建简单测试数据
        X_simple = np.random.normal(0, 1, (100, 6))
        y_simple = np.random.choice([0, 1], 100, p=[0.8, 0.2])
        
        X_train_sub, X_val, y_train_sub, y_val = train_test_split(
            X_simple, y_simple, test_size=0.3, stratify=y_simple, random_state=42
        )
        
        ga = GeneticAlgorithm(
            fitness_func=fitness_function,
            param_bounds=param_ranges.get_param_bounds(),
            param_types=param_ranges.get_param_types(),
            population_size=8,   # 很小的种群用于测试
            max_generations=5,   # 很少的代数
            elite_size=2
        )
        
        print("开始遗传算法测试...")
        best_individual, _ = ga.optimize_with_progress(X_train_sub, y_train_sub, X_val, y_val)
        
        print("✓ 遗传算法进度显示测试通过")
        print(f"  最佳适应度: {best_individual.fitness:.4f}")
        
    except Exception as e:
        print(f"✗ 遗传算法测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主测试函数"""
    print("开始进度显示功能测试...")
    
    # 测试各个组件
    test_individual_components()
    
    # 测试完整流程
    test_ga_progress()
    
    print("\n" + "=" * 80)
    print("进度显示功能测试完成！")
    print("=" * 80)

if __name__ == "__main__":
    # 设置随机种子
    np.random.seed(42)
    
    # 运行测试
    main()
