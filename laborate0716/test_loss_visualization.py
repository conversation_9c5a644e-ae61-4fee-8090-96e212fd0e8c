"""
测试DAG-WGAN损失可视化功能
验证蓝色G损失和橙色D损失的显示效果
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Agg')  # 非交互式后端

def create_sample_loss_data():
    """创建示例损失数据用于测试"""
    epochs = 100
    
    # 模拟生成器损失 (通常较小且波动)
    generator_losses = []
    discriminator_losses = []
    wasserstein_distances = []
    
    np.random.seed(42)
    
    for epoch in range(epochs):
        # 生成器损失模式
        if epoch < 20:
            g_loss = -0.5 + epoch * 0.01 + np.random.normal(0, 0.05)
        elif epoch < 50:
            g_loss = -0.3 - (epoch-20) * 0.005 + np.random.normal(0, 0.03)
        else:
            g_loss = -0.45 + np.random.normal(0, 0.02)
        
        # 判别器损失模式
        if epoch < 20:
            d_loss = 0.8 - epoch * 0.02 + np.random.normal(0, 0.1)
        elif epoch < 50:
            d_loss = 0.4 - (epoch-20) * 0.01 + np.random.normal(0, 0.08)
        else:
            d_loss = 0.1 + np.random.normal(0, 0.05)
        
        # Wasserstein距离
        w_dist = abs(g_loss - d_loss) + np.random.normal(0, 0.02)
        
        generator_losses.append(g_loss)
        discriminator_losses.append(d_loss)
        wasserstein_distances.append(w_dist)
    
    return {
        'g_losses': generator_losses,
        'd_losses': discriminator_losses,
        'w_distances': wasserstein_distances
    }

def test_plot_training_losses():
    """测试训练损失可视化函数"""
    print("🧪 测试训练损失可视化...")
    
    # 创建测试数据
    sample_losses = {
        'DAG-WGAN': create_sample_loss_data(),
        'ADASYN-WGAN-GP': create_sample_loss_data()
    }
    
    # 修改第二个方法的数据使其略有不同
    for i in range(len(sample_losses['ADASYN-WGAN-GP']['g_losses'])):
        sample_losses['ADASYN-WGAN-GP']['g_losses'][i] += np.random.normal(0, 0.01)
        sample_losses['ADASYN-WGAN-GP']['d_losses'][i] += np.random.normal(0, 0.02)
    
    # 测试原始的plot_training_losses函数
    plt.figure(figsize=(15, 10))
    
    methods_with_losses = list(sample_losses.items())
    n_methods = len(methods_with_losses)
    
    for i, (method_name, losses) in enumerate(methods_with_losses):
        # 判别器损失 (橙色)
        plt.subplot(n_methods, 3, i*3 + 1)
        if losses['d_losses']:
            plt.plot(losses['d_losses'], 'orange', linewidth=2.5, label='🟠 判别器损失 (D)', alpha=0.9)
            plt.title(f'{method_name} - 判别器损失 (橙色)', fontsize=12, fontweight='bold', color='orange')
            plt.xlabel('训练轮次 (Epochs)', fontsize=10)
            plt.ylabel('损失值', fontsize=10)
            plt.grid(True, alpha=0.3, linestyle='--')
            plt.legend(fontsize=9)
            plt.tick_params(axis='y', labelcolor='orange')

        # 生成器损失 (蓝色)
        plt.subplot(n_methods, 3, i*3 + 2)
        if losses['g_losses']:
            plt.plot(losses['g_losses'], 'blue', linewidth=2.5, label='🔵 生成器损失 (G)', alpha=0.9)
            plt.title(f'{method_name} - 生成器损失 (蓝色)', fontsize=12, fontweight='bold', color='blue')
            plt.xlabel('训练轮次 (Epochs)', fontsize=10)
            plt.ylabel('损失值', fontsize=10)
            plt.grid(True, alpha=0.3, linestyle='--')
            plt.legend(fontsize=9)
            plt.tick_params(axis='y', labelcolor='blue')

        # Wasserstein距离 (绿色保持不变)
        plt.subplot(n_methods, 3, i*3 + 3)
        if losses['w_distances']:
            plt.plot(losses['w_distances'], 'green', linewidth=2.5, label='🟢 Wasserstein距离', alpha=0.9)
            plt.title(f'{method_name} - Wasserstein距离', fontsize=12, fontweight='bold', color='green')
            plt.xlabel('训练轮次 (Epochs)', fontsize=10)
            plt.ylabel('距离值', fontsize=10)
            plt.grid(True, alpha=0.3, linestyle='--')
            plt.legend(fontsize=9)
            plt.tick_params(axis='y', labelcolor='green')

    plt.suptitle('DAG-WGAN训练损失变化过程\n🔵 蓝色G损失 & 🟠 橙色D损失', 
                fontsize=16, fontweight='bold', y=0.98)
    plt.tight_layout()
    plt.subplots_adjust(top=0.92)
    plt.savefig('test_training_losses.png', dpi=300, bbox_inches='tight', facecolor='white')
    plt.close()
    
    print("✅ 训练损失图测试完成: test_training_losses.png")

def test_detailed_losses():
    """测试详细损失分析图"""
    print("🧪 测试详细损失分析...")
    
    # 创建测试数据
    sample_losses = {
        'DAG-WGAN': create_sample_loss_data()
    }
    
    plt.figure(figsize=(16, 12))
    
    method_name, losses = list(sample_losses.items())[0]
    
    # 生成器和判别器损失对比图
    plt.subplot(1, 2, 1)
    epochs = range(1, len(losses['g_losses']) + 1)
    
    # 双Y轴设计
    ax1 = plt.gca()
    ax2 = ax1.twinx()
    
    # 生成器损失 (蓝色, 左Y轴)
    line1 = ax1.plot(epochs, losses['g_losses'], 'blue', linewidth=3, 
                    label='🔵 生成器损失 (G)', alpha=0.9, marker='o', markersize=2)
    ax1.set_xlabel('训练轮次 (Epochs)', fontsize=11)
    ax1.set_ylabel('生成器损失', color='blue', fontsize=11, fontweight='bold')
    ax1.tick_params(axis='y', labelcolor='blue')
    ax1.grid(True, alpha=0.3, linestyle='--')
    
    # 判别器损失 (橙色, 右Y轴)
    line2 = ax2.plot(epochs, losses['d_losses'], 'orange', linewidth=3, 
                    label='🟠 判别器损失 (D)', alpha=0.9, marker='s', markersize=2)
    ax2.set_ylabel('判别器损失', color='orange', fontsize=11, fontweight='bold')
    ax2.tick_params(axis='y', labelcolor='orange')
    
    # 添加图例
    lines = line1 + line2
    labels = [l.get_label() for l in lines]
    ax1.legend(lines, labels, loc='upper right', fontsize=10)
    
    plt.title(f'{method_name}\n🔵蓝色G损失 vs 🟠橙色D损失', 
             fontsize=13, fontweight='bold', pad=15)
    
    # 损失统计信息图
    plt.subplot(1, 2, 2)
    
    # 计算统计信息
    g_mean = np.mean(losses['g_losses'])
    g_std = np.std(losses['g_losses'])
    g_min = np.min(losses['g_losses'])
    g_max = np.max(losses['g_losses'])
    
    d_mean = np.mean(losses['d_losses'])
    d_std = np.std(losses['d_losses'])
    d_min = np.min(losses['d_losses'])
    d_max = np.max(losses['d_losses'])
    
    # 创建统计条形图
    categories = ['均值', '标准差', '最小值', '最大值']
    g_stats = [g_mean, g_std, g_min, g_max]
    d_stats = [d_mean, d_std, d_min, d_max]
    
    x = np.arange(len(categories))
    width = 0.35
    
    plt.bar(x - width/2, g_stats, width, label='🔵 生成器 (G)', 
           color='blue', alpha=0.7)
    plt.bar(x + width/2, d_stats, width, label='🟠 判别器 (D)', 
           color='orange', alpha=0.7)
    
    plt.xlabel('统计指标', fontsize=11)
    plt.ylabel('损失值', fontsize=11)
    plt.title(f'{method_name} - 损失统计', fontsize=13, fontweight='bold')
    plt.xticks(x, categories, fontsize=10)
    plt.legend(fontsize=10)
    plt.grid(True, alpha=0.3, axis='y')
    
    # 添加数值标签
    for j, (g_val, d_val) in enumerate(zip(g_stats, d_stats)):
        plt.text(j - width/2, g_val + abs(g_val)*0.05, f'{g_val:.4f}', 
                ha='center', va='bottom', fontsize=8, color='blue')
        plt.text(j + width/2, d_val + abs(d_val)*0.05, f'{d_val:.4f}', 
                ha='center', va='bottom', fontsize=8, color='orange')
    
    plt.suptitle('DAG-WGAN详细训练损失分析\n🔵 蓝色生成器损失 & 🟠 橙色判别器损失', 
                fontsize=18, fontweight='bold', y=0.98)
    plt.tight_layout()
    plt.subplots_adjust(top=0.90)
    plt.savefig('test_detailed_losses.png', dpi=300, bbox_inches='tight', facecolor='white')
    plt.close()
    
    print("✅ 详细损失分析图测试完成: test_detailed_losses.png")

def main():
    """主测试函数"""
    print("=" * 80)
    print("🎯 DAG-WGAN损失可视化测试")
    print("🔵 蓝色G损失 & 🟠 橙色D损失")
    print("=" * 80)
    
    # 测试训练损失图
    test_plot_training_losses()
    
    # 测试详细损失分析图
    test_detailed_losses()
    
    print("\n🎉 所有测试完成!")
    print("📊 生成的测试图片:")
    print("  - test_training_losses.png (训练损失图)")
    print("  - test_detailed_losses.png (详细损失分析图)")
    print("\n✅ 颜色验证:")
    print("  🔵 生成器损失: 蓝色")
    print("  🟠 判别器损失: 橙色")
    print("  🟢 Wasserstein距离: 绿色")

if __name__ == '__main__':
    main()
