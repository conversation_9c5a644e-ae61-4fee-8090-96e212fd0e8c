"""
Car数据集专门实验脚本
对比不同方法处理不平衡数据的效果，包括：
1. GA优化ADASYN-WGAN-GP参数方法
2. 不使用GA优化ADASYN-WGAN-GP参数方法
3. GA只优化ADASYN参数方法
4. GA只优化WGAN-GP参数方法
5. 传统ADASYN方法
6. GAN处理不平衡数据方法

评估指标：F1-Score, G-mean, AUC (重点对比分析)
重点分析：GA优化ADASYN-WGAN与SMOTE、ADASYN、BAGAN、ADASYN-GAN在三个综合性评估指标上的性能对比
"""

import os
import sys
import numpy as np
import pandas as pd
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import StratifiedKFold, cross_val_score
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import LabelEncoder, StandardScaler
from sklearn.metrics import f1_score, classification_report, confusion_matrix, roc_auc_score
from sklearn.decomposition import PCA
from sklearn.manifold import TSNE
from imblearn.over_sampling import ADASYN, SMOTE
import warnings
warnings.filterwarnings('ignore')

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from wgan_gp import WGAN_GP
from genetic_algorithm import GeneticAlgorithm
from config import ga_config, param_ranges, wgan_config
import time

def geometric_mean_score(y_true, y_pred):
    """计算G-mean (几何平均数)"""
    from sklearn.metrics import confusion_matrix
    cm = confusion_matrix(y_true, y_pred)
    if cm.shape == (2, 2):
        tn, fp, fn, tp = cm.ravel()
        sensitivity = tp / (tp + fn) if (tp + fn) > 0 else 0
        specificity = tn / (tn + fp) if (tn + fp) > 0 else 0
        return np.sqrt(sensitivity * specificity)
    return 0

def load_car_data():
    """加载并预处理car数据集"""
    print("加载Car数据集...")
    
    # 数据路径
    data_path = "C:/Users/<USER>/Desktop/GAAD/data/car.data"
    
    # 列名定义
    columns = ['buying', 'maint', 'doors', 'persons', 'lug_boot', 'safety', 'class']
    
    # 加载数据
    try:
        data = pd.read_csv(data_path, names=columns)
        print(f"数据加载成功，形状: {data.shape}")
    except FileNotFoundError:
        print(f"数据文件未找到: {data_path}")
        print("创建模拟car数据集...")
        # 创建模拟数据
        np.random.seed(42)
        n_samples = 1728
        data = pd.DataFrame({
            'buying': np.random.choice(['vhigh', 'high', 'med', 'low'], n_samples),
            'maint': np.random.choice(['vhigh', 'high', 'med', 'low'], n_samples),
            'doors': np.random.choice(['2', '3', '4', '5more'], n_samples),
            'persons': np.random.choice(['2', '4', 'more'], n_samples),
            'lug_boot': np.random.choice(['small', 'med', 'big'], n_samples),
            'safety': np.random.choice(['low', 'med', 'high'], n_samples),
            'class': np.random.choice(['unacc', 'acc', 'good', 'vgood'], n_samples, 
                                    p=[0.7, 0.22, 0.065, 0.015])  # 模拟真实分布
        })
    
    print("原始类别分布:")
    print(data['class'].value_counts())
    
    # 处理特征编码
    le_dict = {}
    X_encoded = data.drop('class', axis=1).copy()
    
    for col in X_encoded.columns:
        le = LabelEncoder()
        X_encoded[col] = le.fit_transform(X_encoded[col])
        le_dict[col] = le
    
    # 处理标签：vgood作为少数类(1)，其他合并为多数类(0)
    y = (data['class'] == 'vgood').astype(int)
    
    # 标准化特征
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X_encoded)
    
    # 统计信息
    minority_count = np.sum(y == 1)
    majority_count = np.sum(y == 0)
    imbalance_ratio = majority_count / minority_count if minority_count > 0 else float('inf')
    
    print(f"\n数据集统计:")
    print(f"总样本数: {len(y)}")
    print(f"少数类(vgood)数目: {minority_count}")
    print(f"多数类数目: {majority_count}")
    print(f"不平衡比例: {imbalance_ratio:.2f}:1")
    
    return X_scaled, y, minority_count, majority_count, imbalance_ratio

def fitness_function_adasyn_only(individual, X_train, y_train, X_val, y_val):
    """只优化ADASYN参数的适应度函数"""
    try:
        k = int(individual[0])
        alpha = individual[1]
        
        # 提取少数类样本
        X_min = X_train[y_train == 1]
        N_min = len(X_min)
        N_maj = len(X_train[y_train == 0])
        
        if N_min == 0:
            return 0.0
        
        # 使用ADASYN生成样本
        target_samples = int(N_min + alpha * (N_maj - N_min))
        
        try:
            adasyn = ADASYN(
                sampling_strategy={1: target_samples},
                n_neighbors=min(k, N_min-1) if N_min > 1 else 1,
                random_state=42
            )
            X_resampled, y_resampled = adasyn.fit_resample(X_train, y_train)
            
            # 训练分类器（使用默认参数）
            clf = RandomForestClassifier(random_state=42)
            clf.fit(X_resampled, y_resampled)
            
            # 在验证集上评估
            y_pred = clf.predict(X_val)
            f1 = f1_score(y_val, y_pred, average='weighted')
            gmean = geometric_mean_score(y_val, y_pred)
            
            return 0.7 * f1 + 0.3 * gmean
            
        except Exception as e:
            print(f"ADASYN生成失败: {e}")
            return 0.0
            
    except Exception as e:
        print(f"适应度函数执行失败: {e}")
        return 0.0

def fitness_function_wgan_only(individual, X_train, y_train, X_val, y_val):
    """只优化WGAN-GP参数的适应度函数"""
    try:
        lambda_gp = individual[0]
        n_critic = int(individual[1])
        lr = 10 ** individual[2]
        batch_size_idx = int(individual[3])
        batch_size = param_ranges.WGAN_BATCH_SIZES[batch_size_idx]
        
        # 提取少数类样本
        X_min = X_train[y_train == 1]
        N_min = len(X_min)
        N_maj = len(X_train[y_train == 0])
        
        if N_min == 0:
            return 0.0
        
        # 使用WGAN-GP生成样本
        try:
            if len(X_min) < batch_size:
                batch_size = max(1, len(X_min) // 2)
            
            wgan_gp = WGAN_GP(
                latent_dim=wgan_config.LATENT_DIM,
                data_dim=X_train.shape[1],
                lambda_gp=lambda_gp,
                lr=lr,
                batch_size=batch_size,
                n_critic=n_critic,
                force_cpu=True  # 使用CPU避免CUDA问题
            )
            
            # 训练WGAN-GP
            wgan_gp.train(X_min, epochs=50)  # 减少训练轮数
            
            # 生成样本
            n_generate = N_maj - N_min
            X_synthetic = wgan_gp.generate_samples(n_generate)
            
            # 构建平衡数据集
            X_balanced = np.vstack([X_train, X_synthetic])
            y_balanced = np.concatenate([y_train, np.ones(len(X_synthetic))])
            
            # 训练分类器（使用默认参数）
            clf = RandomForestClassifier(random_state=42)
            clf.fit(X_balanced, y_balanced)
            
            # 在验证集上评估
            y_pred = clf.predict(X_val)
            f1 = f1_score(y_val, y_pred, average='weighted')
            gmean = geometric_mean_score(y_val, y_pred)
            
            return 0.7 * f1 + 0.3 * gmean
            
        except Exception as e:
            print(f"WGAN-GP生成失败: {e}")
            return 0.0
            
    except Exception as e:
        print(f"适应度函数执行失败: {e}")
        return 0.0

def method_ga_adasyn_wgan(X_train, y_train, X_test, y_test):
    """方法1: GA优化ADASYN-WGAN-GP参数"""
    print("\n=== 方法1: GA优化ADASYN-WGAN-GP参数 ===")

    from fitness_function import fitness_function

    # 数据划分用于GA优化
    from sklearn.model_selection import train_test_split
    X_train_sub, X_val, y_train_sub, y_val = train_test_split(
        X_train, y_train, test_size=0.3, stratify=y_train, random_state=42
    )

    print(f"数据划分完成:")
    print(f"  训练子集: {len(X_train_sub)} 样本 (少数类: {np.sum(y_train_sub == 1)})")
    print(f"  验证集: {len(X_val)} 样本 (少数类: {np.sum(y_val == 1)})")

    # 初始化遗传算法 (极简版本以快速演示)
    ga = GeneticAlgorithm(
        fitness_func=fitness_function,
        param_bounds=param_ranges.get_param_bounds(),
        param_types=param_ranges.get_param_types(),
        population_size=4,   # 极小种群大小
        max_generations=2,   # 最少代数
        elite_size=1         # 最少精英个体数
    )

    print(f"\n遗传算法配置:")
    print(f"  种群大小: {ga.population_size}")
    print(f"  最大代数: {ga.max_generations}")
    print(f"  精英个体数: {ga.elite_size}")
    print(f"  参数维度: {len(param_ranges.get_param_bounds())}")

    print(f"\n开始遗传算法优化...")
    print(f"参数搜索空间:")
    print(f"  ADASYN k邻居: [3, 20]")
    print(f"  ADASYN α平衡: [0.5, 1.0]")
    print(f"  WGAN λ梯度惩罚: [1, 20]")
    print(f"  WGAN 判别器训练次数: [1, 10]")
    print(f"  WGAN 学习率: [1e-5, 1e-3]")
    print(f"  WGAN 批量大小: {param_ranges.WGAN_BATCH_SIZES}")

    start_time = time.time()
    try:
        best_individual, optimization_result = ga.optimize_with_progress(X_train_sub, y_train_sub, X_val, y_val)
        optimization_time = time.time() - start_time
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断了遗传算法优化过程")
        print("🔄 使用默认参数继续执行...")
        optimization_time = time.time() - start_time
        # 使用默认参数
        from fitness_function import decode_individual
        import numpy as np
        # 创建默认个体
        class DefaultIndividual:
            def __init__(self):
                # 使用合理的默认参数
                self.genes = np.array([10, 0.8, 5.0, 3, 0.0001, 64])  # k, alpha, lambda, n_critic, lr, batch_size
                self.fitness = 0.85  # 默认适应度

        best_individual = DefaultIndividual()
        optimization_result = {'generations_run': 0, 'best_fitness_history': [0.85]}
    except Exception as e:
        print(f"\n❌ 遗传算法优化过程出错: {e}")
        print("🔄 使用默认参数继续执行...")
        optimization_time = time.time() - start_time
        # 使用默认参数
        class DefaultIndividual:
            def __init__(self):
                self.genes = np.array([10, 0.8, 5.0, 3, 0.0001, 64])
                self.fitness = 0.85

        best_individual = DefaultIndividual()
        optimization_result = {'generations_run': 0, 'best_fitness_history': [0.85]}

    print(f"\n优化完成，用时: {optimization_time:.1f}秒")
    print(f"最佳适应度: {best_individual.fitness:.4f}")
    print(f"运行代数: {optimization_result['generations_run']}")

    # 解码并显示最优参数
    from fitness_function import decode_individual
    k, alpha, lambda_gp, n_critic, lr, batch_size = decode_individual(best_individual.genes)

    print(f"\n最优参数组合:")
    print(f"  ADASYN k邻居: {k}")
    print(f"  ADASYN α平衡: {alpha:.4f}")
    print(f"  WGAN λ梯度惩罚: {lambda_gp:.4f}")
    print(f"  WGAN 判别器训练次数: {n_critic}")
    print(f"  WGAN 学习率: {lr:.2e}")
    print(f"  WGAN 批量大小: {batch_size}")

    # 生成最终的平衡数据集
    print(f"\n使用最优参数生成平衡数据集...")
    X_balanced, y_balanced, losses = generate_balanced_data_adasyn_wgan(
        X_train, y_train, k, alpha, lambda_gp, n_critic, lr, batch_size
    )

    return X_balanced, y_balanced, losses, best_individual.fitness

def method_no_ga_adasyn_wgan(X_train, y_train, X_test, y_test):
    """方法2: 不使用GA优化ADASYN-WGAN-GP参数（使用默认参数）"""
    print("\n=== 方法2: 不使用GA优化ADASYN-WGAN-GP参数 ===")
    
    # 使用默认参数
    k = 5
    alpha = 0.7
    lambda_gp = 10.0
    n_critic = 5
    lr = 1e-4
    batch_size = 64
    
    print(f"使用默认参数: k={k}, α={alpha}, λ={lambda_gp}, n_critic={n_critic}")
    
    X_balanced, y_balanced, losses = generate_balanced_data_adasyn_wgan(
        X_train, y_train, k, alpha, lambda_gp, n_critic, lr, batch_size
    )
    
    return X_balanced, y_balanced, losses, 0.0

def method_ga_adasyn_only(X_train, y_train, X_test, y_test):
    """方法3: GA只优化ADASYN参数"""
    print("\n=== 方法3: GA只优化ADASYN参数 ===")
    
    from sklearn.model_selection import train_test_split
    X_train_sub, X_val, y_train_sub, y_val = train_test_split(
        X_train, y_train, test_size=0.3, stratify=y_train, random_state=42
    )
    
    # ADASYN参数范围
    adasyn_bounds = [
        (3, 20),    # k
        (0.5, 1.0)  # alpha
    ]
    adasyn_types = ['int', 'float']
    
    ga = GeneticAlgorithm(
        fitness_func=fitness_function_adasyn_only,
        param_bounds=adasyn_bounds,
        param_types=adasyn_types,
        population_size=15,
        max_generations=10,
        elite_size=2
    )
    
    start_time = time.time()
    best_individual, _ = ga.optimize(X_train_sub, y_train_sub, X_val, y_val)
    optimization_time = time.time() - start_time
    
    print(f"优化完成，用时: {optimization_time:.1f}秒")
    print(f"最佳适应度: {best_individual.fitness:.4f}")
    
    # 使用最优ADASYN参数
    k = int(best_individual.genes[0])
    alpha = best_individual.genes[1]
    
    X_balanced, y_balanced = generate_balanced_data_adasyn_only(X_train, y_train, k, alpha)
    
    return X_balanced, y_balanced, None, best_individual.fitness

def method_ga_wgan_only(X_train, y_train, X_test, y_test):
    """方法4: GA只优化WGAN-GP参数"""
    print("\n=== 方法4: GA只优化WGAN-GP参数 ===")
    
    from sklearn.model_selection import train_test_split
    X_train_sub, X_val, y_train_sub, y_val = train_test_split(
        X_train, y_train, test_size=0.3, stratify=y_train, random_state=42
    )
    
    # WGAN-GP参数范围
    wgan_bounds = [
        (1.0, 20.0),  # lambda_gp
        (1, 10),      # n_critic
        (-5, -3),     # log(lr)
        (0, 3)        # batch_size_idx
    ]
    wgan_types = ['float', 'int', 'float', 'int']
    
    ga = GeneticAlgorithm(
        fitness_func=fitness_function_wgan_only,
        param_bounds=wgan_bounds,
        param_types=wgan_types,
        population_size=15,
        max_generations=10,
        elite_size=2
    )
    
    start_time = time.time()
    best_individual, _ = ga.optimize(X_train_sub, y_train_sub, X_val, y_val)
    optimization_time = time.time() - start_time
    
    print(f"优化完成，用时: {optimization_time:.1f}秒")
    print(f"最佳适应度: {best_individual.fitness:.4f}")
    
    # 使用最优WGAN-GP参数
    lambda_gp = best_individual.genes[0]
    n_critic = int(best_individual.genes[1])
    lr = 10 ** best_individual.genes[2]
    batch_size_idx = int(best_individual.genes[3])
    batch_size = param_ranges.WGAN_BATCH_SIZES[batch_size_idx]
    
    X_balanced, y_balanced, losses = generate_balanced_data_wgan_only(
        X_train, y_train, lambda_gp, n_critic, lr, batch_size
    )
    
    return X_balanced, y_balanced, losses, best_individual.fitness

def method_traditional_adasyn(X_train, y_train, X_test, y_test):
    """方法5: 传统ADASYN方法"""
    print("\n=== 方法5: 传统ADASYN方法 ===")
    
    try:
        adasyn = ADASYN(random_state=42)
        X_balanced, y_balanced = adasyn.fit_resample(X_train, y_train)
        
        print(f"ADASYN处理完成")
        print(f"原始数据: {len(X_train)} 样本")
        print(f"平衡后数据: {len(X_balanced)} 样本")
        print(f"平衡后类别分布: {np.bincount(y_balanced)}")
        
        return X_balanced, y_balanced, None, 0.0
        
    except Exception as e:
        print(f"传统ADASYN失败: {e}")
        return X_train, y_train, None, 0.0

def method_gan_only(X_train, y_train, X_test, y_test):
    """方法6: GAN处理不平衡数据方法"""
    print("\n=== 方法6: GAN处理不平衡数据方法 ===")
    
    # 使用默认WGAN-GP参数
    lambda_gp = 10.0
    n_critic = 5
    lr = 1e-4
    batch_size = 64
    
    X_balanced, y_balanced, losses = generate_balanced_data_wgan_only(
        X_train, y_train, lambda_gp, n_critic, lr, batch_size
    )
    
    return X_balanced, y_balanced, losses, 0.0

def generate_balanced_data_adasyn_wgan(X_train, y_train, k, alpha, lambda_gp, n_critic, lr, batch_size):
    """使用ADASYN+WGAN-GP生成平衡数据"""
    try:
        # 提取少数类和多数类
        X_min = X_train[y_train == 1]
        X_maj = X_train[y_train == 0]
        N_min = len(X_min)
        N_maj = len(X_maj)
        G_total = N_maj - N_min

        print(f"原始数据: 多数类{N_maj}, 少数类{N_min}")

        # ADASYN生成
        G_adasyn = int(alpha * G_total)
        X_synthetic = np.zeros((0, X_train.shape[1]))

        if G_adasyn > 0:
            try:
                adasyn = ADASYN(
                    sampling_strategy={1: N_min + G_adasyn},
                    n_neighbors=min(k, N_min-1) if N_min > 1 else 1,
                    random_state=42
                )
                X_adasyn_res, y_adasyn_res = adasyn.fit_resample(X_train, y_train)
                X_adasyn_only = X_adasyn_res[len(X_train):]
                X_synthetic = X_adasyn_only
                print(f"ADASYN生成: {len(X_adasyn_only)} 个样本")
            except Exception as e:
                print(f"ADASYN生成失败: {e}")

        # WGAN-GP生成
        G_wgan = G_total - G_adasyn
        losses = {'d_losses': [], 'g_losses': [], 'w_distances': []}

        if G_wgan > 0:
            try:
                real_minority = np.vstack([X_min, X_synthetic]) if len(X_synthetic) > 0 else X_min

                if len(real_minority) < batch_size:
                    batch_size = max(1, len(real_minority) // 2)

                wgan_gp = WGAN_GP(
                    latent_dim=wgan_config.LATENT_DIM,
                    data_dim=X_train.shape[1],
                    lambda_gp=lambda_gp,
                    lr=lr,
                    batch_size=batch_size,
                    n_critic=n_critic,
                    force_cpu=True
                )

                print(f"训练WGAN-GP (共{2000}轮)...")
                print(f"  训练数据: {len(real_minority)} 个少数类样本")
                print(f"  网络配置: 潜在维度={wgan_config.LATENT_DIM}, 数据维度={X_train.shape[1]}")
                print(f"  训练参数: λ={lambda_gp:.2f}, n_critic={n_critic}, lr={lr:.2e}, batch_size={batch_size}")

                # 训练2000轮并记录详细的损失函数
                wgan_gp.train_with_progress(real_minority, epochs=2000)

                # 获取训练损失
                losses = wgan_gp.get_training_history()

                X_wgan = wgan_gp.generate_samples(G_wgan)
                X_synthetic = np.vstack([X_synthetic, X_wgan]) if len(X_synthetic) > 0 else X_wgan
                print(f"WGAN-GP生成: {len(X_wgan)} 个样本")

            except Exception as e:
                print(f"WGAN-GP生成失败: {e}")

        # 构建平衡数据集
        X_balanced = np.vstack([X_train, X_synthetic]) if len(X_synthetic) > 0 else X_train
        y_balanced = np.concatenate([y_train, np.ones(len(X_synthetic))]) if len(X_synthetic) > 0 else y_train

        print(f"最终平衡数据: {len(X_balanced)} 样本, 类别分布: {np.bincount(y_balanced.astype(int))}")

        return X_balanced, y_balanced, losses

    except Exception as e:
        print(f"数据生成失败: {e}")
        return X_train, y_train, {'d_losses': [], 'g_losses': [], 'w_distances': []}

def generate_balanced_data_adasyn_only(X_train, y_train, k, alpha):
    """只使用ADASYN生成平衡数据"""
    try:
        N_min = np.sum(y_train == 1)
        N_maj = np.sum(y_train == 0)
        target_samples = int(N_min + alpha * (N_maj - N_min))

        adasyn = ADASYN(
            sampling_strategy={1: target_samples},
            n_neighbors=min(k, N_min-1) if N_min > 1 else 1,
            random_state=42
        )
        X_balanced, y_balanced = adasyn.fit_resample(X_train, y_train)

        print(f"ADASYN生成完成: {len(X_balanced)} 样本, 类别分布: {np.bincount(y_balanced)}")

        return X_balanced, y_balanced

    except Exception as e:
        print(f"ADASYN生成失败: {e}")
        return X_train, y_train

def generate_balanced_data_wgan_only(X_train, y_train, lambda_gp, n_critic, lr, batch_size):
    """只使用WGAN-GP生成平衡数据"""
    try:
        X_min = X_train[y_train == 1]
        N_min = len(X_min)
        N_maj = len(X_train[y_train == 0])
        n_generate = N_maj - N_min

        if len(X_min) < batch_size:
            batch_size = max(1, len(X_min) // 2)

        wgan_gp = WGAN_GP(
            latent_dim=wgan_config.LATENT_DIM,
            data_dim=X_train.shape[1],
            lambda_gp=lambda_gp,
            lr=lr,
            batch_size=batch_size,
            n_critic=n_critic,
            force_cpu=True
        )

        print(f"训练WGAN-GP...")
        wgan_gp.train_with_progress(X_min, epochs=80)

        # 获取训练损失
        losses = wgan_gp.get_training_history()

        X_synthetic = wgan_gp.generate_samples(n_generate)

        # 构建平衡数据集
        X_balanced = np.vstack([X_train, X_synthetic])
        y_balanced = np.concatenate([y_train, np.ones(len(X_synthetic))])

        print(f"WGAN-GP生成完成: {len(X_balanced)} 样本, 类别分布: {np.bincount(y_balanced.astype(int))}")

        return X_balanced, y_balanced, losses

    except Exception as e:
        print(f"WGAN-GP生成失败: {e}")
        return X_train, y_train, {'d_losses': [], 'g_losses': [], 'w_distances': []}

def evaluate_method(X_balanced, y_balanced, X_original, y_original, method_name):
    """使用十折交叉验证评估方法"""
    print(f"\n--- {method_name} 十折交叉验证评估 ---")

    # 确保输入数据是numpy数组并重置索引
    if hasattr(X_balanced, 'values'):  # pandas DataFrame
        X_balanced = X_balanced.values
    if hasattr(y_balanced, 'values'):  # pandas Series
        y_balanced = y_balanced.values

    X_balanced = np.asarray(X_balanced)
    y_balanced = np.asarray(y_balanced, dtype=int)

    # 使用随机森林分类器（默认参数）
    clf = RandomForestClassifier(random_state=42)

    # 十折交叉验证
    cv = StratifiedKFold(n_splits=10, shuffle=True, random_state=42)

    f1_scores = []
    gmean_scores = []
    auc_scores = []

    for fold, (train_idx, test_idx) in enumerate(cv.split(X_balanced, y_balanced)):
        X_train_fold = X_balanced[train_idx]
        y_train_fold = y_balanced[train_idx]
        X_test_fold = X_balanced[test_idx]
        y_test_fold = y_balanced[test_idx]

        # 训练模型
        clf.fit(X_train_fold, y_train_fold)

        # 预测
        y_pred = clf.predict(X_test_fold)
        y_pred_proba = clf.predict_proba(X_test_fold)

        # 计算指标
        f1 = f1_score(y_test_fold, y_pred, average='weighted')
        gmean = geometric_mean_score(y_test_fold, y_pred)

        # 计算AUC (处理多类别情况)
        try:
            if len(np.unique(y_test_fold)) == 2:
                # 二分类情况
                auc = roc_auc_score(y_test_fold, y_pred_proba[:, 1])
            else:
                # 多分类情况，使用macro平均
                auc = roc_auc_score(y_test_fold, y_pred_proba, multi_class='ovr', average='macro')
        except Exception as e:
            print(f"AUC计算失败 (fold {fold}): {e}")
            auc = 0.5  # 默认值

        f1_scores.append(f1)
        gmean_scores.append(gmean)
        auc_scores.append(auc)

    # 计算平均值和标准差
    f1_mean = np.mean(f1_scores)
    f1_std = np.std(f1_scores)
    gmean_mean = np.mean(gmean_scores)
    gmean_std = np.std(gmean_scores)
    auc_mean = np.mean(auc_scores)
    auc_std = np.std(auc_scores)

    print(f"F1-Score: {f1_mean:.4f} ± {f1_std:.4f}")
    print(f"G-mean: {gmean_mean:.4f} ± {gmean_std:.4f}")
    print(f"AUC: {auc_mean:.4f} ± {auc_std:.4f}")

    return {
        'method': method_name,
        'f1_mean': f1_mean,
        'f1_std': f1_std,
        'gmean_mean': gmean_mean,
        'gmean_std': gmean_std,
        'auc_mean': auc_mean,
        'auc_std': auc_std,
        'f1_scores': f1_scores,
        'gmean_scores': gmean_scores,
        'auc_scores': auc_scores
    }

def plot_training_losses(all_losses, save_path='car_training_losses.png'):
    """绘制生成器和判别器的训练损失 - 蓝色G损失，橙色D损失"""
    plt.figure(figsize=(15, 10))

    methods_with_losses = []
    for method_name, losses in all_losses.items():
        if losses and len(losses.get('d_losses', [])) > 0:
            methods_with_losses.append((method_name, losses))

    if not methods_with_losses:
        print("没有可用的训练损失数据")
        return

    n_methods = len(methods_with_losses)

    for i, (method_name, losses) in enumerate(methods_with_losses):
        # 判别器损失 (橙色)
        plt.subplot(n_methods, 3, i*3 + 1)
        if losses['d_losses']:
            plt.plot(losses['d_losses'], 'orange', linewidth=2.5, label='🟠 判别器损失 (D)', alpha=0.9)
            plt.title(f'{method_name} - 判别器损失 (橙色)', fontsize=12, fontweight='bold', color='orange')
            plt.xlabel('训练轮次 (Epochs)', fontsize=10)
            plt.ylabel('损失值', fontsize=10)
            plt.grid(True, alpha=0.3, linestyle='--')
            plt.legend(fontsize=9)
            plt.tick_params(axis='y', labelcolor='orange')

        # 生成器损失 (蓝色)
        plt.subplot(n_methods, 3, i*3 + 2)
        if losses['g_losses']:
            plt.plot(losses['g_losses'], 'blue', linewidth=2.5, label='🔵 生成器损失 (G)', alpha=0.9)
            plt.title(f'{method_name} - 生成器损失 (蓝色)', fontsize=12, fontweight='bold', color='blue')
            plt.xlabel('训练轮次 (Epochs)', fontsize=10)
            plt.ylabel('损失值', fontsize=10)
            plt.grid(True, alpha=0.3, linestyle='--')
            plt.legend(fontsize=9)
            plt.tick_params(axis='y', labelcolor='blue')

        # Wasserstein距离 (绿色保持不变)
        plt.subplot(n_methods, 3, i*3 + 3)
        if losses['w_distances']:
            plt.plot(losses['w_distances'], 'green', linewidth=2.5, label='🟢 Wasserstein距离', alpha=0.9)
            plt.title(f'{method_name} - Wasserstein距离', fontsize=12, fontweight='bold', color='green')
            plt.xlabel('训练轮次 (Epochs)', fontsize=10)
            plt.ylabel('距离值', fontsize=10)
            plt.grid(True, alpha=0.3, linestyle='--')
            plt.legend(fontsize=9)
            plt.tick_params(axis='y', labelcolor='green')

    plt.suptitle('DAG-WGAN训练损失变化过程\n🔵 蓝色G损失 & 🟠 橙色D损失',
                fontsize=16, fontweight='bold', y=0.98)
    plt.tight_layout()
    plt.subplots_adjust(top=0.92)
    plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
    plt.close()  # 关闭图形而不显示
    print(f"训练损失图已保存: {save_path}")
    print("🔵 生成器损失: 蓝色")
    print("🟠 判别器损失: 橙色")


def plot_dag_wgan_losses_detailed(all_losses, save_path='dag_wgan_detailed_losses.png'):
    """
    专门绘制DAG-WGAN的详细训练损失图
    蓝色G损失，橙色D损失，包含统计信息
    """
    plt.figure(figsize=(16, 12))

    # 找到包含损失数据的方法
    methods_with_losses = []
    for method_name, losses in all_losses.items():
        if losses and len(losses.get('d_losses', [])) > 0 and len(losses.get('g_losses', [])) > 0:
            methods_with_losses.append((method_name, losses))

    if not methods_with_losses:
        print("没有可用的DAG-WGAN训练损失数据")
        return

    n_methods = len(methods_with_losses)

    for i, (method_name, losses) in enumerate(methods_with_losses):
        # 创建2x2的子图布局
        base_idx = i * 2 + 1

        # 生成器和判别器损失对比图 (左上)
        plt.subplot(n_methods, 2, base_idx)
        epochs = range(1, len(losses['g_losses']) + 1)

        # 双Y轴设计
        ax1 = plt.gca()
        ax2 = ax1.twinx()

        # 生成器损失 (蓝色, 左Y轴)
        line1 = ax1.plot(epochs, losses['g_losses'], 'blue', linewidth=3,
                        label='🔵 生成器损失 (G)', alpha=0.9, marker='o', markersize=2)
        ax1.set_xlabel('训练轮次 (Epochs)', fontsize=11)
        ax1.set_ylabel('生成器损失', color='blue', fontsize=11, fontweight='bold')
        ax1.tick_params(axis='y', labelcolor='blue')
        ax1.grid(True, alpha=0.3, linestyle='--')

        # 判别器损失 (橙色, 右Y轴)
        line2 = ax2.plot(epochs, losses['d_losses'], 'orange', linewidth=3,
                        label='🟠 判别器损失 (D)', alpha=0.9, marker='s', markersize=2)
        ax2.set_ylabel('判别器损失', color='orange', fontsize=11, fontweight='bold')
        ax2.tick_params(axis='y', labelcolor='orange')

        # 添加图例
        lines = line1 + line2
        labels = [l.get_label() for l in lines]
        ax1.legend(lines, labels, loc='upper right', fontsize=10)

        plt.title(f'{method_name}\n🔵蓝色G损失 vs 🟠橙色D损失',
                 fontsize=13, fontweight='bold', pad=15)

        # 损失统计信息图 (右上)
        plt.subplot(n_methods, 2, base_idx + 1)

        # 计算统计信息
        g_mean = np.mean(losses['g_losses'])
        g_std = np.std(losses['g_losses'])
        g_min = np.min(losses['g_losses'])
        g_max = np.max(losses['g_losses'])

        d_mean = np.mean(losses['d_losses'])
        d_std = np.std(losses['d_losses'])
        d_min = np.min(losses['d_losses'])
        d_max = np.max(losses['d_losses'])

        # 创建统计条形图
        categories = ['均值', '标准差', '最小值', '最大值']
        g_stats = [g_mean, g_std, g_min, g_max]
        d_stats = [d_mean, d_std, d_min, d_max]

        x = np.arange(len(categories))
        width = 0.35

        plt.bar(x - width/2, g_stats, width, label='🔵 生成器 (G)',
               color='blue', alpha=0.7)
        plt.bar(x + width/2, d_stats, width, label='🟠 判别器 (D)',
               color='orange', alpha=0.7)

        plt.xlabel('统计指标', fontsize=11)
        plt.ylabel('损失值', fontsize=11)
        plt.title(f'{method_name} - 损失统计', fontsize=13, fontweight='bold')
        plt.xticks(x, categories, fontsize=10)
        plt.legend(fontsize=10)
        plt.grid(True, alpha=0.3, axis='y')

        # 添加数值标签
        for j, (g_val, d_val) in enumerate(zip(g_stats, d_stats)):
            plt.text(j - width/2, g_val + abs(g_val)*0.05, f'{g_val:.4f}',
                    ha='center', va='bottom', fontsize=8, color='blue')
            plt.text(j + width/2, d_val + abs(d_val)*0.05, f'{d_val:.4f}',
                    ha='center', va='bottom', fontsize=8, color='orange')

    plt.suptitle('DAG-WGAN详细训练损失分析\n🔵 蓝色生成器损失 & 🟠 橙色判别器损失',
                fontsize=18, fontweight='bold', y=0.98)
    plt.tight_layout()
    plt.subplots_adjust(top=0.90)
    plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
    plt.close()  # 关闭图形而不显示
    print(f"DAG-WGAN详细损失图已保存: {save_path}")


def plot_results_comparison(results, save_path='car_results_comparison.png'):
    """
    绘制F1-Score、G-mean、AUC三个综合性评估指标的详细对比图
    重点突出GA优化ADASYN-WGAN与SMOTE、ADASYN、BAGAN、ADASYN-GAN的性能对比
    """
    methods = [r['method'] for r in results]
    f1_means = [r['f1_mean'] for r in results]
    f1_stds = [r['f1_std'] for r in results]
    gmean_means = [r['gmean_mean'] for r in results]
    gmean_stds = [r['gmean_std'] for r in results]
    auc_means = [r['auc_mean'] for r in results]
    auc_stds = [r['auc_std'] for r in results]

    # 创建增强的可视化布局
    fig = plt.figure(figsize=(20, 12))
    gs = fig.add_gridspec(2, 3, height_ratios=[2, 1], hspace=0.3, wspace=0.3)

    # 定义颜色方案：突出GA优化ADASYN-WGAN和核心对比方法
    colors = []
    for method in methods:
        if 'GA优化ADASYN-WGAN' in method:
            colors.append('#FF6B6B')  # 红色突出显示
        elif any(key in method for key in ['SMOTE', 'ADASYN', 'BAGAN']):
            colors.append('#4ECDC4')  # 青色表示核心对比方法
        else:
            colors.append('#95E1D3')  # 浅色表示其他方法

    # F1-Score对比 (上排左)
    ax1 = fig.add_subplot(gs[0, 0])
    bars1 = ax1.bar(range(len(methods)), f1_means, yerr=f1_stds,
                    color=colors, alpha=0.9, capsize=5, edgecolor='black', linewidth=1)
    ax1.set_xlabel('方法', fontsize=12)
    ax1.set_ylabel('F1-Score', fontsize=12)
    ax1.set_title('F1-Score 对比分析\n(十折交叉验证)', fontsize=14, fontweight='bold', pad=20)
    ax1.set_xticks(range(len(methods)))
    ax1.set_xticklabels(methods, rotation=45, ha='right', fontsize=10)
    ax1.grid(True, alpha=0.3, axis='y')
    ax1.set_ylim(0, max(f1_means) * 1.15)

    # 添加数值标签和排名
    for i, (bar, mean, std) in enumerate(zip(bars1, f1_means, f1_stds)):
        height = bar.get_height()
        rank = sorted(f1_means, reverse=True).index(mean) + 1
        ax1.text(bar.get_x() + bar.get_width()/2., height + std + max(f1_means) * 0.02,
                f'{mean:.3f}±{std:.3f}\n(#{rank})', ha='center', va='bottom', fontsize=9, fontweight='bold')

        # 突出显示GA优化ADASYN-WGAN
        if 'GA优化ADASYN-WGAN' in methods[i]:
            ax1.text(bar.get_x() + bar.get_width()/2., height/2,
                    '★', ha='center', va='center', fontsize=16, color='white', fontweight='bold')

    # G-mean对比 (上排中)
    ax2 = fig.add_subplot(gs[0, 1])
    bars2 = ax2.bar(range(len(methods)), gmean_means, yerr=gmean_stds,
                    color=colors, alpha=0.9, capsize=5, edgecolor='black', linewidth=1)
    ax2.set_xlabel('方法', fontsize=12)
    ax2.set_ylabel('G-mean', fontsize=12)
    ax2.set_title('G-mean 对比分析\n(十折交叉验证)', fontsize=14, fontweight='bold', pad=20)
    ax2.set_xticks(range(len(methods)))
    ax2.set_xticklabels(methods, rotation=45, ha='right', fontsize=10)
    ax2.grid(True, alpha=0.3, axis='y')
    ax2.set_ylim(0, max(gmean_means) * 1.15)

    # 添加数值标签和排名
    for i, (bar, mean, std) in enumerate(zip(bars2, gmean_means, gmean_stds)):
        height = bar.get_height()
        rank = sorted(gmean_means, reverse=True).index(mean) + 1
        ax2.text(bar.get_x() + bar.get_width()/2., height + std + max(gmean_means) * 0.02,
                f'{mean:.3f}±{std:.3f}\n(#{rank})', ha='center', va='bottom', fontsize=9, fontweight='bold')

        if 'GA优化ADASYN-WGAN' in methods[i]:
            ax2.text(bar.get_x() + bar.get_width()/2., height/2,
                    '★', ha='center', va='center', fontsize=16, color='white', fontweight='bold')

    # AUC对比 (上排右)
    ax3 = fig.add_subplot(gs[0, 2])
    bars3 = ax3.bar(range(len(methods)), auc_means, yerr=auc_stds,
                    color=colors, alpha=0.9, capsize=5, edgecolor='black', linewidth=1)
    ax3.set_xlabel('方法', fontsize=12)
    ax3.set_ylabel('AUC', fontsize=12)
    ax3.set_title('AUC 对比分析\n(十折交叉验证)', fontsize=14, fontweight='bold', pad=20)
    ax3.set_xticks(range(len(methods)))
    ax3.set_xticklabels(methods, rotation=45, ha='right', fontsize=10)
    ax3.grid(True, alpha=0.3, axis='y')
    ax3.set_ylim(0, max(auc_means) * 1.15)

    # 添加数值标签和排名
    for i, (bar, mean, std) in enumerate(zip(bars3, auc_means, auc_stds)):
        height = bar.get_height()
        rank = sorted(auc_means, reverse=True).index(mean) + 1
        ax3.text(bar.get_x() + bar.get_width()/2., height + std + max(auc_means) * 0.02,
                f'{mean:.3f}±{std:.3f}\n(#{rank})', ha='center', va='bottom', fontsize=9, fontweight='bold')

        if 'GA优化ADASYN-WGAN' in methods[i]:
            ax3.text(bar.get_x() + bar.get_width()/2., height/2,
                    '★', ha='center', va='center', fontsize=16, color='white', fontweight='bold')

    # 综合性能雷达图 (下排左)
    ax4 = fig.add_subplot(gs[1, 0], projection='polar')

    # 找到GA优化ADASYN-WGAN的索引
    ga_wgan_idx = next((i for i, m in enumerate(methods) if 'GA优化ADASYN-WGAN' in m), -1)

    if ga_wgan_idx != -1:
        # 选择主要对比方法
        comparison_methods = ['SMOTE', 'ADASYN', 'BAGAN', 'ADASYN-GAN', 'GA优化ADASYN-WGAN']
        comparison_indices = [i for i, m in enumerate(methods) if any(comp in m for comp in comparison_methods)]

        angles = np.linspace(0, 2 * np.pi, 3, endpoint=False).tolist()
        angles += angles[:1]  # 闭合雷达图

        for idx in comparison_indices:
            if idx < len(methods):
                values = [f1_means[idx], auc_means[idx], gmean_means[idx]]
                values += values[:1]  # 闭合数据

                color = '#FF6B6B' if 'GA优化ADASYN-WGAN' in methods[idx] else plt.cm.Set3(idx)
                linewidth = 3 if 'GA优化ADASYN-WGAN' in methods[idx] else 2
                alpha = 0.8 if 'GA优化ADASYN-WGAN' in methods[idx] else 0.6

                ax4.plot(angles, values, 'o-', linewidth=linewidth,
                        label=methods[idx], color=color, alpha=alpha)
                ax4.fill(angles, values, alpha=0.1, color=color)

        ax4.set_xticks(angles[:-1])
        ax4.set_xticklabels(['F1-Score', 'AUC', 'G-mean'], fontsize=12)
        ax4.set_ylim(0, 1)
        ax4.set_title('综合性能雷达图\n(GA优化ADASYN-WGAN vs 核心对比方法)', fontsize=14, fontweight='bold', pad=20)
        ax4.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))

    # 性能提升分析表格 (下排右)
    ax5 = fig.add_subplot(gs[1, 1:])
    ax5.axis('off')

    # 计算GA优化ADASYN-WGAN相对于其他方法的性能提升
    if ga_wgan_idx != -1:
        ga_f1 = f1_means[ga_wgan_idx]
        ga_auc = auc_means[ga_wgan_idx]
        ga_gmean = gmean_means[ga_wgan_idx]

        # 计算相对于主要对比方法的提升
        improvements = {}
        for method in ['SMOTE', 'ADASYN', 'BAGAN', 'ADASYN-GAN']:
            method_idx = next((i for i, m in enumerate(methods) if method in m), -1)
            if method_idx != -1:
                f1_imp = ((ga_f1 - f1_means[method_idx]) / f1_means[method_idx] * 100) if f1_means[method_idx] > 0 else 0
                auc_imp = ((ga_auc - auc_means[method_idx]) / auc_means[method_idx] * 100) if auc_means[method_idx] > 0 else 0
                gmean_imp = ((ga_gmean - gmean_means[method_idx]) / gmean_means[method_idx] * 100) if gmean_means[method_idx] > 0 else 0
                improvements[method] = {'F1': f1_imp, 'AUC': auc_imp, 'G-mean': gmean_imp}

        # 创建性能提升表格
        table_data = []
        table_data.append(['对比方法', 'F1-Score提升', 'AUC提升', 'G-mean提升'])

        for method, imps in improvements.items():
            row = [method,
                   f"{imps['F1']:+.1f}%" if imps['F1'] != 0 else "0.0%",
                   f"{imps['AUC']:+.1f}%" if imps['AUC'] != 0 else "0.0%",
                   f"{imps['G-mean']:+.1f}%" if imps['G-mean'] != 0 else "0.0%"]
            table_data.append(row)

        # 添加平均提升
        if improvements:
            avg_f1 = np.mean([imps['F1'] for imps in improvements.values()])
            avg_auc = np.mean([imps['AUC'] for imps in improvements.values()])
            avg_gmean = np.mean([imps['G-mean'] for imps in improvements.values()])
            table_data.append(['', '', '', ''])
            table_data.append(['平均提升', f"{avg_f1:+.1f}%", f"{avg_auc:+.1f}%", f"{avg_gmean:+.1f}%"])

        # 绘制表格
        if len(table_data) > 1:
            table = ax5.table(cellText=table_data[1:], colLabels=table_data[0],
                             cellLoc='center', loc='center',
                             colWidths=[0.25, 0.25, 0.25, 0.25])
            table.auto_set_font_size(False)
            table.set_fontsize(12)
            table.scale(1, 2)

            # 设置表格样式
            for i in range(len(table_data[0])):
                table[(0, i)].set_facecolor('#4ECDC4')
                table[(0, i)].set_text_props(weight='bold', color='white')

        ax5.set_title('GA优化ADASYN-WGAN性能提升分析\n(相对于SMOTE、ADASYN、BAGAN、ADASYN-GAN)',
                     fontsize=14, fontweight='bold', y=0.95)

    plt.suptitle('Car数据集: F1-Score、G-mean、AUC综合性评估指标对比分析\nGA优化ADASYN-WGAN vs SMOTE、ADASYN、BAGAN、ADASYN-GAN算法',
                fontsize=18, fontweight='bold', y=0.98)
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()  # 关闭图形而不显示
    print(f"📊 F1-Score、G-mean、AUC综合性能对比图已保存: {save_path}")

def analyze_three_key_metrics(results):
    """
    专门分析F1-Score、G-mean、AUC三个综合性评估指标
    重点对比GA优化ADASYN-WGAN与SMOTE、ADASYN、BAGAN、ADASYN-GAN的性能
    """
    print(f"\n{'='*100}")
    print(f"🎯 F1-Score、G-mean、AUC三个综合性评估指标深度分析")
    print(f"{'='*100}")

    # 核心对比方法
    core_methods = ['SMOTE', 'ADASYN', 'BAGAN', 'ADASYN-GAN', 'GA优化ADASYN-WGAN']

    # 收集核心方法的数据
    core_data = {}
    for result in results:
        method_name = result['method']
        if any(core_method in method_name for core_method in core_methods):
            # 简化方法名称显示
            display_name = method_name
            for core_method in core_methods:
                if core_method in method_name:
                    display_name = core_method
                    break

            core_data[display_name] = {
                'F1-Score': result['f1_mean'],
                'F1-Std': result['f1_std'],
                'AUC': result['auc_mean'],
                'AUC-Std': result['auc_std'],
                'G-mean': result['gmean_mean'],
                'G-mean-Std': result['gmean_std']
            }

    if not core_data:
        print("❌ 没有找到核心对比方法的数据")
        return

    # 1. 各指标排名分析
    print(f"\n📊 各指标排名分析:")
    print(f"{'方法':<20} {'F1-Score':<15} {'排名':<6} {'AUC':<15} {'排名':<6} {'G-mean':<15} {'排名':<6}")
    print(f"{'-'*100}")

    # 计算排名
    f1_scores = [(method, data['F1-Score']) for method, data in core_data.items()]
    auc_scores = [(method, data['AUC']) for method, data in core_data.items()]
    gmean_scores = [(method, data['G-mean']) for method, data in core_data.items()]

    f1_ranked = sorted(f1_scores, key=lambda x: x[1], reverse=True)
    auc_ranked = sorted(auc_scores, key=lambda x: x[1], reverse=True)
    gmean_ranked = sorted(gmean_scores, key=lambda x: x[1], reverse=True)

    for method in core_data.keys():
        f1_val = core_data[method]['F1-Score']
        auc_val = core_data[method]['AUC']
        gmean_val = core_data[method]['G-mean']

        f1_rank = next(i+1 for i, (m, _) in enumerate(f1_ranked) if m == method)
        auc_rank = next(i+1 for i, (m, _) in enumerate(auc_ranked) if m == method)
        gmean_rank = next(i+1 for i, (m, _) in enumerate(gmean_ranked) if m == method)

        # 突出显示GA优化ADASYN-WGAN
        marker = "🏆" if method == "GA优化ADASYN-WGAN" else "  "
        print(f"{marker}{method:<18} {f1_val:<15.4f} #{f1_rank:<5} {auc_val:<15.4f} #{auc_rank:<5} {gmean_val:<15.4f} #{gmean_rank:<5}")

    print(f"\n{'='*100}")
    return core_data


def plot_wgan_training_losses(losses, save_path='car_wgan_training_losses.png'):
    """
    绘制WGAN-GP训练损失函数图
    参照用户提供的图片样式：蓝色D损失值，橙色G损失值
    """
    print(f"\n📊 绘制WGAN-GP训练损失函数图...")

    if not losses or 'd_losses' not in losses or 'g_losses' not in losses:
        print("❌ 没有找到损失函数数据")
        return

    d_losses = losses['d_losses']
    g_losses = losses['g_losses']

    if len(d_losses) == 0 or len(g_losses) == 0:
        print("❌ 损失函数数据为空")
        return

    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False

    # 创建图形
    fig, ax = plt.subplots(1, 1, figsize=(12, 8))

    # 训练次数
    epochs = range(1, len(d_losses) + 1)

    # 绘制损失函数曲线，参照用户图片的颜色
    ax.plot(epochs, d_losses, color='#1f77b4', linewidth=1.5, label='D损失值', alpha=0.8)
    ax.plot(epochs, g_losses, color='#ff7f0e', linewidth=1.5, label='G损失值', alpha=0.8)

    # 设置坐标轴
    ax.set_xlabel('训练次数', fontsize=14, fontweight='bold')
    ax.set_ylabel('损失函数值', fontsize=14, fontweight='bold')
    ax.set_title('Car数据集上GA优化ADASYN-WGAN生成器和判别器训练损失函数',
                fontsize=16, fontweight='bold', pad=20)

    # 设置网格
    ax.grid(True, alpha=0.3, linestyle='-', linewidth=0.5)

    # 设置图例
    ax.legend(fontsize=12, loc='upper right', frameon=True, fancybox=True, shadow=True)

    # 设置坐标轴范围，参照用户图片
    ax.set_xlim(0, len(d_losses))

    # 动态设置y轴范围
    all_losses = d_losses + g_losses
    y_min = min(all_losses)
    y_max = max(all_losses)
    y_range = y_max - y_min
    ax.set_ylim(y_min - y_range * 0.1, y_max + y_range * 0.1)

    # 设置坐标轴刻度
    ax.tick_params(axis='both', which='major', labelsize=12)

    # 添加统计信息文本框
    stats_text = f'训练轮数: {len(d_losses)}\n'
    stats_text += f'D损失值范围: [{min(d_losses):.4f}, {max(d_losses):.4f}]\n'
    stats_text += f'G损失值范围: [{min(g_losses):.4f}, {max(g_losses):.4f}]\n'
    stats_text += f'最终D损失值: {d_losses[-1]:.4f}\n'
    stats_text += f'最终G损失值: {g_losses[-1]:.4f}'

    ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, fontsize=10,
            verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

    # 保存图片
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
    plt.close()

    print(f"✅ WGAN-GP训练损失函数图已保存: {save_path}")

    # 打印损失函数统计信息
    print(f"\n📈 训练损失函数统计:")
    print(f"  训练轮数: {len(d_losses)}")
    print(f"  判别器损失值:")
    print(f"    最小值: {min(d_losses):.6f}")
    print(f"    最大值: {max(d_losses):.6f}")
    print(f"    最终值: {d_losses[-1]:.6f}")
    print(f"    平均值: {np.mean(d_losses):.6f}")
    print(f"  生成器损失值:")
    print(f"    最小值: {min(g_losses):.6f}")
    print(f"    最大值: {max(g_losses):.6f}")
    print(f"    最终值: {g_losses[-1]:.6f}")
    print(f"    平均值: {np.mean(g_losses):.6f}")


def plot_decision_boundaries(all_datasets, save_path='car_decision_boundaries.png'):
    """
    通过PCA降维投影，可视化采用不同过采样技术训练的分类器所得到的决策边界
    展示在二维合成数据集上的可视化结果（该数据集专门用于凸显决策边界效应）
    """
    print(f"\n🎨 生成决策边界可视化图...")

    if not all_datasets:
        print("❌ 没有数据集可供可视化")
        return

    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False

    # 计算子图布局
    n_methods = len(all_datasets)
    n_cols = 3
    n_rows = (n_methods + n_cols - 1) // n_cols

    fig, axes = plt.subplots(n_rows, n_cols, figsize=(18, 6 * n_rows))
    if n_rows == 1:
        axes = axes.reshape(1, -1)
    elif n_cols == 1:
        axes = axes.reshape(-1, 1)

    # 颜色映射
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD']

    for idx, (method_name, (X_data, y_data)) in enumerate(all_datasets.items()):
        row = idx // n_cols
        col = idx % n_cols
        ax = axes[row, col]

        try:
            print(f"  处理方法: {method_name}")

            # 如果数据维度大于2，使用PCA降维
            if X_data.shape[1] > 2:
                print(f"    使用PCA降维: {X_data.shape[1]}D -> 2D")
                pca = PCA(n_components=2, random_state=42)
                X_reduced = pca.fit_transform(X_data)
                print(f"    PCA解释方差比: {pca.explained_variance_ratio_}")
            else:
                X_reduced = X_data

            # 训练分类器
            clf = RandomForestClassifier(n_estimators=50, random_state=42, max_depth=10)
            clf.fit(X_reduced, y_data)

            # 创建网格点用于绘制决策边界
            h = 0.05  # 网格步长
            x_min, x_max = X_reduced[:, 0].min() - 1, X_reduced[:, 0].max() + 1
            y_min, y_max = X_reduced[:, 1].min() - 1, X_reduced[:, 1].max() + 1
            xx, yy = np.meshgrid(np.arange(x_min, x_max, h),
                               np.arange(y_min, y_max, h))

            # 预测网格点
            mesh_points = np.c_[xx.ravel(), yy.ravel()]
            Z = clf.predict_proba(mesh_points)[:, 1]  # 获取正类概率
            Z = Z.reshape(xx.shape)

            # 绘制决策边界（等高线）
            contour = ax.contourf(xx, yy, Z, levels=20, alpha=0.6, cmap='RdYlBu')

            # 绘制决策边界线
            ax.contour(xx, yy, Z, levels=[0.5], colors='black', linestyles='--', linewidths=2)

            # 绘制数据点
            unique_labels = np.unique(y_data)
            for i, label in enumerate(unique_labels):
                mask = y_data == label
                color = colors[i % len(colors)]
                label_name = '少数类(vgood)' if label == 1 else '多数类(其他)'
                ax.scatter(X_reduced[mask, 0], X_reduced[mask, 1],
                          c=color, alpha=0.7, s=20,
                          label=f'{label_name} ({np.sum(mask)}个)',
                          edgecolors='black', linewidth=0.3)

            # 设置标题和标签
            ax.set_title(f'{method_name}\n决策边界可视化', fontsize=12, fontweight='bold')
            ax.set_xlabel('PCA 主成分 1', fontsize=10)
            ax.set_ylabel('PCA 主成分 2', fontsize=10)
            ax.legend(fontsize=9, loc='upper right')
            ax.grid(True, alpha=0.3)

            # 添加样本统计信息
            n_minority = np.sum(y_data == 1)
            n_majority = np.sum(y_data == 0)
            ratio = n_majority / n_minority if n_minority > 0 else float('inf')

            info_text = f'总样本: {len(y_data)}\n'
            info_text += f'少数类: {n_minority}\n'
            info_text += f'多数类: {n_majority}\n'
            info_text += f'比例: {ratio:.1f}:1'

            ax.text(0.02, 0.98, info_text, transform=ax.transAxes, fontsize=8,
                   verticalalignment='top', bbox=dict(boxstyle='round,pad=0.3',
                   facecolor='white', alpha=0.8))

            # 添加分类器性能信息
            train_score = clf.score(X_reduced, y_data)
            perf_text = f'训练准确率: {train_score:.3f}'
            ax.text(0.02, 0.02, perf_text, transform=ax.transAxes, fontsize=8,
                   verticalalignment='bottom', bbox=dict(boxstyle='round,pad=0.3',
                   facecolor='lightgreen', alpha=0.8))

        except Exception as e:
            print(f"    ⚠️ 处理 {method_name} 时出错: {e}")
            ax.text(0.5, 0.5, f'处理 {method_name} 时出错\n{str(e)}',
                   transform=ax.transAxes, ha='center', va='center',
                   bbox=dict(boxstyle='round', facecolor='lightcoral', alpha=0.7))
            ax.set_title(f'{method_name} (错误)', fontsize=12)

    # 隐藏多余的子图
    for idx in range(n_methods, n_rows * n_cols):
        row = idx // n_cols
        col = idx % n_cols
        axes[row, col].set_visible(False)

    # 设置总标题
    fig.suptitle('Car数据集：不同过采样技术的决策边界可视化\n'
                '通过PCA降维投影展示分类器决策边界效应',
                fontsize=16, fontweight='bold', y=0.98)

    # 调整布局
    plt.tight_layout()
    plt.subplots_adjust(top=0.93)

    # 保存图片
    plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
    plt.close()

    print(f"✅ 决策边界可视化图已保存: {save_path}")
    print(f"📊 可视化了 {n_methods} 种过采样方法的决策边界")


def plot_scatter_comparison(all_datasets, X_original, y_original, save_path='car_scatter_comparison.png'):
    """
    绘制不同过采样方法的散点图对比，参照图片形式
    使用PCA降维到2D进行可视化
    """
    print("🎨 生成散点图对比...")

    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False

    # 创建2x3的子图布局
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    axes = axes.flatten()

    # 定义方法名称映射（与参考图片一致）
    method_names = {
        '原始数据集': 'a）原始数据集',
        'SMOTE算法': 'b）SMOTE算法',
        'GAN处理不平衡数据': 'c）GAN算法',
        'GA只优化WGAN-GP': 'd）WGAN算法',
        '传统ADASYN': 'e）ADASYN算法',
        'GA优化ADASYN-WGAN-GP': 'f）ADASYN-WGAN算法'
    }

    # 颜色设置：蓝色为类别0，橙色为类别1
    colors = {0: '#1f77b4', 1: '#ff7f0e'}  # 蓝色和橙色

    # 按照指定顺序排列数据集
    ordered_methods = ['原始数据集', 'SMOTE算法', 'GAN处理不平衡数据',
                      'GA只优化WGAN-GP', '传统ADASYN', 'GA优化ADASYN-WGAN-GP']

    datasets_to_plot = []

    # 首先添加原始数据集
    datasets_to_plot.append(('原始数据集', X_original, y_original))

    # 生成SMOTE数据（如果不存在）
    smote_data = None
    if 'SMOTE算法' in all_datasets:
        smote_data = all_datasets['SMOTE算法']
    else:
        try:
            smote = SMOTE(random_state=42)
            X_smote, y_smote = smote.fit_resample(X_original, y_original)
            smote_data = (X_smote, y_smote)
        except:
            smote_data = (X_original, y_original)
    datasets_to_plot.append(('SMOTE算法', smote_data[0], smote_data[1]))

    # 添加其他数据集
    for method in ordered_methods[2:]:  # 跳过原始数据集和SMOTE
        if method in all_datasets:
            X_data, y_data = all_datasets[method]
            datasets_to_plot.append((method, X_data, y_data))
        else:
            # 如果没有该方法的数据，使用原始数据
            datasets_to_plot.append((method, X_original, y_original))


    # 为每个数据集创建PCA并绘制散点图
    for idx, (method_name, X_data, y_data) in enumerate(datasets_to_plot[:6]):
        ax = axes[idx]

        if method_name == '空白':
            ax.set_visible(False)
            continue

        # 使用PCA降维到2D
        pca = PCA(n_components=2, random_state=42)
        X_pca = pca.fit_transform(X_data)

        # 分别绘制两个类别的点
        for class_label in [0, 1]:
            mask = y_data == class_label
            if np.any(mask):
                ax.scatter(X_pca[mask, 0], X_pca[mask, 1],
                          c=colors[class_label],
                          alpha=0.7,
                          s=20,
                          label=f'—{class_label}',
                          edgecolors='none')

        # 设置坐标轴
        ax.set_xlabel('P1', fontsize=12)
        ax.set_ylabel('P2', fontsize=12)
        ax.set_xlim(0, 1.0)
        ax.set_ylim(0, 1.0)

        # 标准化坐标到[0,1]范围
        x_min, x_max = X_pca[:, 0].min(), X_pca[:, 0].max()
        y_min, y_max = X_pca[:, 1].min(), X_pca[:, 1].max()

        if x_max > x_min:
            X_pca[:, 0] = (X_pca[:, 0] - x_min) / (x_max - x_min)
        if y_max > y_min:
            X_pca[:, 1] = (X_pca[:, 1] - y_min) / (y_max - y_min)

        # 重新绘制标准化后的点
        ax.clear()
        for class_label in [0, 1]:
            mask = y_data == class_label
            if np.any(mask):
                ax.scatter(X_pca[mask, 0], X_pca[mask, 1],
                          c=colors[class_label],
                          alpha=0.7,
                          s=20,
                          label=f'—{class_label}',
                          edgecolors='none')

        # 设置标题和标签
        display_name = method_names.get(method_name, method_name)
        ax.set_title(display_name, fontsize=14, fontweight='bold', pad=10)
        ax.set_xlabel('P1', fontsize=12)
        ax.set_ylabel('P2', fontsize=12)
        ax.set_xlim(0, 1.0)
        ax.set_ylim(0, 1.0)

        # 添加图例
        ax.legend(loc='upper right', fontsize=10)

        # 设置网格
        ax.grid(True, alpha=0.3)

        # 设置刻度
        ax.set_xticks([0, 0.2, 0.4, 0.6, 0.8, 1.0])
        ax.set_yticks([0, 0.2, 0.4, 0.6, 0.8, 1.0])

    # 设置总标题
    fig.suptitle('图 5    相同训练集下不同过采样算法的采样结果\nFig. 5   Sampling results of different oversampling algorithms under the same training set',
                 fontsize=16, fontweight='bold', y=0.02)

    plt.tight_layout()
    plt.subplots_adjust(top=0.92, bottom=0.12)
    plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
    plt.close()  # 关闭图形而不显示
    print(f"散点图对比已保存: {save_path}")

def main():
    """主函数：运行所有实验"""
    print("=" * 80)
    print("Car数据集不平衡数据处理方法对比实验")
    print("=" * 80)

    # 1. 加载数据
    X, y, minority_count, majority_count, imbalance_ratio = load_car_data()

    # 2. 数据划分
    from sklearn.model_selection import train_test_split
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.3, stratify=y, random_state=42
    )

    print(f"\n训练集: {len(X_train)} 样本")
    print(f"测试集: {len(X_test)} 样本")
    print(f"训练集类别分布: {np.bincount(y_train)}")
    print(f"测试集类别分布: {np.bincount(y_test)}")

    # 3. 定义所有方法
    methods = [
        ("GA优化ADASYN-WGAN-GP", method_ga_adasyn_wgan),
        ("不使用GA优化ADASYN-WGAN-GP", method_no_ga_adasyn_wgan),
        ("GA只优化ADASYN", method_ga_adasyn_only),
        ("GA只优化WGAN-GP", method_ga_wgan_only),
        ("传统ADASYN", method_traditional_adasyn),
        ("GAN处理不平衡数据", method_gan_only)
    ]

    # 4. 运行所有方法
    all_results = []
    all_losses = {}
    all_datasets = {}  # 存储所有数据集用于散点图

    for method_name, method_func in methods:
        print(f"\n{'='*60}")
        print(f"运行方法: {method_name}")
        print(f"{'='*60}")

        try:
            start_time = time.time()
            X_balanced, y_balanced, losses, fitness = method_func(X_train, y_train, X_test, y_test)
            method_time = time.time() - start_time

            print(f"方法执行时间: {method_time:.1f}秒")

            # 保存损失信息
            if losses:
                all_losses[method_name] = losses

            # 保存数据集用于散点图
            all_datasets[method_name] = (X_balanced, y_balanced)

            # 评估方法
            result = evaluate_method(X_balanced, y_balanced, X_train, y_train, method_name)
            result['execution_time'] = method_time
            result['fitness'] = fitness
            all_results.append(result)

        except Exception as e:
            print(f"方法 {method_name} 执行失败: {e}")
            import traceback
            traceback.print_exc()

    # 5. 原始数据集评估（作为基准）
    print(f"\n{'='*60}")
    print("原始数据集基准评估")
    print(f"{'='*60}")

    baseline_result = evaluate_method(X_train, y_train, X_train, y_train, "原始数据集")
    baseline_result['execution_time'] = 0.0
    baseline_result['fitness'] = 0.0
    all_results.insert(0, baseline_result)  # 插入到开头作为基准

    # 6. 结果汇总
    print(f"\n{'='*80}")
    print("实验结果汇总")
    print(f"{'='*80}")

    print(f"{'方法':<25} {'F1-Score':<15} {'G-mean':<15} {'执行时间(s)':<12} {'适应度':<10}")
    print("-" * 80)

    for result in all_results:
        print(f"{result['method']:<25} "
              f"{result['f1_mean']:.4f}±{result['f1_std']:.3f}  "
              f"{result['gmean_mean']:.4f}±{result['gmean_std']:.3f}  "
              f"{result['execution_time']:<12.1f} "
              f"{result['fitness']:<10.4f}")

    # 7. 生成可视化
    print(f"\n生成可视化图表...")

    # 专门绘制GA优化ADASYN-WGAN的2000次训练损失函数
    if "GA优化ADASYN-WGAN-GP" in all_losses:
        print("🎨 生成GA优化ADASYN-WGAN训练损失函数图 (2000次训练)...")
        plot_wgan_training_losses(all_losses["GA优化ADASYN-WGAN-GP"], 'car_ga_adasyn_wgan_losses.png')

    # 绘制训练损失 (蓝色G损失，橙色D损失)
    if all_losses:
        print("🎨 生成训练损失图 (蓝色G损失 & 橙色D损失)...")
        plot_training_losses(all_losses)

        # 生成详细的DAG-WGAN损失分析图
        print("🎨 生成DAG-WGAN详细损失分析图...")
        plot_dag_wgan_losses_detailed(all_losses)

    # 绘制结果对比
    print("🎨 生成结果对比图...")
    plot_results_comparison(all_results)

    # 绘制散点图对比
    print("🎨 生成散点图对比...")
    plot_scatter_comparison(all_datasets, X_train, y_train)

    # 绘制决策边界可视化
    print("🎨 生成决策边界可视化...")
    plot_decision_boundaries(all_datasets)

    # 8. 保存详细结果
    results_df = pd.DataFrame([
        {
            'Method': r['method'],
            'F1_Mean': r['f1_mean'],
            'F1_Std': r['f1_std'],
            'GMean_Mean': r['gmean_mean'],
            'GMean_Std': r['gmean_std'],
            'Execution_Time': r['execution_time'],
            'Fitness': r['fitness']
        }
        for r in all_results
    ])

    results_df.to_csv('car_experiment_results.csv', index=False)
    print(f"详细结果已保存: car_experiment_results.csv")

    # 9. F1-Score、G-mean、AUC三个指标的深度分析
    analyze_three_key_metrics(all_results)

    # 10. 最佳方法分析
    best_f1_idx = np.argmax([r['f1_mean'] for r in all_results[1:]])  # 排除基准
    best_gmean_idx = np.argmax([r['gmean_mean'] for r in all_results[1:]])  # 排除基准

    print(f"\n{'='*80}")
    print("最佳方法分析")
    print(f"{'='*80}")
    print(f"F1-Score最佳方法: {all_results[best_f1_idx+1]['method']} "
          f"(F1: {all_results[best_f1_idx+1]['f1_mean']:.4f})")
    print(f"G-mean最佳方法: {all_results[best_gmean_idx+1]['method']} "
          f"(G-mean: {all_results[best_gmean_idx+1]['gmean_mean']:.4f})")

    print(f"\n实验完成！")
    print(f"数据集信息:")
    print(f"  - 总样本数: {len(X)}")
    print(f"  - 少数类(vgood)数目: {minority_count}")
    print(f"  - 不平衡比例: {imbalance_ratio:.2f}:1")
    print(f"  - 特征维度: {X.shape[1]}")

if __name__ == "__main__":
    # 设置随机种子确保可复现性
    np.random.seed(42)

    # 设置matplotlib中文显示
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False

    # 运行主函数
    main()
