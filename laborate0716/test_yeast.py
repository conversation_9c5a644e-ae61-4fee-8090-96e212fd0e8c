"""
测试Yeast数据集处理脚本
"""

import os
import sys
import numpy as np
import pandas as pd
import warnings
warnings.filterwarnings('ignore')

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_yeast_data_loading():
    """测试Yeast数据加载"""
    print("=" * 60)
    print("测试Yeast数据集加载")
    print("=" * 60)
    
    try:
        from yeast import load_yeast_data
        
        X, y, minority_count, majority_count, imbalance_ratio = load_yeast_data()
        
        print(f"✓ 数据加载成功")
        print(f"  数据形状: {X.shape}")
        print(f"  标签形状: {y.shape}")
        print(f"  少数类(GOL+POX+VAC)数目: {minority_count}")
        print(f"  多数类(其他)数目: {majority_count}")
        print(f"  不平衡比例: {imbalance_ratio:.2f}:1")
        print(f"  类别分布: {np.bincount(y)}")
        
        return True, (X, y)
        
    except Exception as e:
        print(f"✗ 数据加载失败: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def test_data_characteristics():
    """测试数据特征"""
    print("\n" + "=" * 60)
    print("测试Yeast数据集特征")
    print("=" * 60)
    
    try:
        # 尝试读取真实的yeast.data文件
        data_path = "C:/Users/<USER>/Desktop/GAAD/data/yeast.data"
        
        try:
            # Yeast数据集的列名
            columns = [
                'sequence_name', 'mcg', 'gvh', 'alm', 'mit', 'erl', 'pox', 'vac', 'nuc', 'class'
            ]
            
            # 尝试不同的分隔符
            separators = ['\s+', ' ', '\t']
            data = None
            
            for sep in separators:
                try:
                    data = pd.read_csv(data_path, sep=sep, names=columns)
                    if data.shape[1] == len(columns):  # 确保正确分割
                        print(f"✓ 使用分隔符 '{sep}' 成功读取数据")
                        break
                except:
                    continue
            
            if data is not None:
                print(f"  数据形状: {data.shape}")
                print(f"  列名: {list(data.columns)}")
                
                # 分析类别分布
                class_counts = data['class'].value_counts()
                print(f"\n  原始类别分布:")
                for class_name, count in class_counts.items():
                    print(f"    {class_name}: {count} 样本")
                
                # 分析GOL+POX+VAC作为少数类
                minority_classes = ['GOL', 'POX', 'VAC']
                minority_samples = data[data['class'].isin(minority_classes)]
                majority_samples = data[~data['class'].isin(minority_classes)]
                
                print(f"\n  重新分类后:")
                print(f"    少数类(GOL+POX+VAC): {len(minority_samples)} 样本")
                print(f"    多数类(其他): {len(majority_samples)} 样本")
                
                if len(minority_samples) > 0:
                    imbalance_ratio = len(majority_samples) / len(minority_samples)
                    print(f"    不平衡比例: {imbalance_ratio:.2f}:1")
                
                # 显示少数类详细分布
                print(f"\n  少数类详细分布:")
                for class_name in minority_classes:
                    count = (data['class'] == class_name).sum()
                    print(f"    {class_name}: {count} 样本")
                
                # 显示多数类前几个类别
                majority_class_counts = data[~data['class'].isin(minority_classes)]['class'].value_counts()
                print(f"\n  多数类主要类别:")
                for class_name, count in majority_class_counts.head(5).items():
                    print(f"    {class_name}: {count} 样本")
                
                # 检查特征范围
                print(f"\n  特征范围分析:")
                feature_cols = ['mcg', 'gvh', 'alm', 'mit', 'erl', 'pox', 'vac', 'nuc']
                
                for col in feature_cols[:3]:  # 只显示前3个特征
                    if col in data.columns:
                        try:
                            col_data = pd.to_numeric(data[col], errors='coerce').dropna()
                            if len(col_data) > 0:
                                print(f"    {col}: 范围 [{col_data.min():.3f}, {col_data.max():.3f}], 均值 {col_data.mean():.3f}")
                        except:
                            pass
            else:
                print(f"⚠️  无法正确解析数据文件")
                
        except Exception as e:
            print(f"⚠️  无法读取真实数据文件: {e}")
            print(f"  将使用模拟数据")
        
        return True
        
    except Exception as e:
        print(f"✗ 数据特征测试失败: {e}")
        return False

def test_traditional_adasyn():
    """测试传统ADASYN方法"""
    print("\n" + "=" * 60)
    print("测试传统ADASYN方法")
    print("=" * 60)
    
    try:
        from yeast import load_yeast_data, method_traditional_adasyn
        from sklearn.model_selection import train_test_split
        
        # 加载数据
        X, y, _, _, _ = load_yeast_data()
        
        # 数据划分
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.3, stratify=y, random_state=42
        )
        
        print(f"训练集: {len(X_train)} 样本 (少数类: {np.sum(y_train == 1)})")
        
        # 运行传统ADASYN
        X_balanced, y_balanced, losses, fitness = method_traditional_adasyn(
            X_train, y_train, X_test, y_test
        )
        
        print(f"✓ 传统ADASYN测试成功")
        print(f"  平衡后数据: {len(X_balanced)} 样本")
        print(f"  类别分布: {np.bincount(y_balanced)}")
        
        return True
        
    except Exception as e:
        print(f"✗ 传统ADASYN测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_evaluation():
    """测试评估函数"""
    print("\n" + "=" * 60)
    print("测试十折交叉验证评估")
    print("=" * 60)
    
    try:
        from yeast import load_yeast_data, evaluate_method
        
        # 加载数据
        X, y, _, _, _ = load_yeast_data()
        
        # 测试原始数据集评估
        result = evaluate_method(X, y, X, y, "原始Yeast数据集")
        
        print(f"✓ 评估测试成功")
        print(f"  F1-Score: {result['f1_mean']:.4f} ± {result['f1_std']:.4f}")
        print(f"  G-mean: {result['gmean_mean']:.4f} ± {result['gmean_std']:.4f}")
        
        return True
        
    except Exception as e:
        print(f"✗ 评估测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ga_adasyn_only():
    """测试GA只优化ADASYN参数"""
    print("\n" + "=" * 60)
    print("测试GA只优化ADASYN参数")
    print("=" * 60)
    
    try:
        from yeast import load_yeast_data, method_ga_adasyn_only
        from sklearn.model_selection import train_test_split
        
        # 加载数据
        X, y, _, _, _ = load_yeast_data()
        
        # 数据划分
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.3, stratify=y, random_state=42
        )
        
        print(f"开始GA优化ADASYN参数测试...")
        
        # 运行GA优化ADASYN（使用很小的参数进行快速测试）
        X_balanced, y_balanced, losses, fitness = method_ga_adasyn_only(
            X_train, y_train, X_test, y_test
        )
        
        print(f"✓ GA优化ADASYN测试成功")
        print(f"  最佳适应度: {fitness:.4f}")
        print(f"  平衡后数据: {len(X_balanced)} 样本")
        print(f"  类别分布: {np.bincount(y_balanced)}")
        
        return True
        
    except Exception as e:
        print(f"✗ GA优化ADASYN测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_preprocessing():
    """测试数据预处理功能"""
    print("\n" + "=" * 60)
    print("测试数据预处理功能")
    print("=" * 60)
    
    try:
        # 测试不同的数据格式处理
        data_path = "C:/Users/<USER>/Desktop/GAAD/data/yeast.data"
        columns = [
            'sequence_name', 'mcg', 'gvh', 'alm', 'mit', 'erl', 'pox', 'vac', 'nuc', 'class'
        ]
        
        # 读取原始数据
        data = pd.read_csv(data_path, sep='\s+', names=columns)
        print(f"原始数据形状: {data.shape}")
        
        # 检查缺失值
        missing_values = data.isnull().sum()
        total_missing = missing_values.sum()
        print(f"缺失值总数: {total_missing}")
        
        if total_missing > 0:
            print(f"缺失值分布:")
            for col, missing in missing_values.items():
                if missing > 0:
                    print(f"  {col}: {missing}")
        
        # 检查数据类型
        print(f"\n数据类型分布:")
        numeric_cols = data.select_dtypes(include=[np.number]).columns
        object_cols = data.select_dtypes(include=['object']).columns
        print(f"  数值型列: {len(numeric_cols)} 个")
        print(f"  对象型列: {len(object_cols)} 个")
        
        if len(object_cols) > 0:
            print(f"  对象型列: {list(object_cols)}")
        
        # 检查类别唯一值
        if 'class' in data.columns:
            unique_classes = data['class'].unique()
            print(f"\n  类别唯一值: {list(unique_classes)}")
            print(f"  类别数量: {len(unique_classes)}")
        
        print(f"✓ 数据预处理测试成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 数据预处理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("Yeast数据集处理脚本测试")
    print("=" * 80)
    
    tests = [
        ("数据加载", test_yeast_data_loading),
        ("数据特征分析", test_data_characteristics),
        ("数据预处理", test_data_preprocessing),
        ("传统ADASYN", test_traditional_adasyn),
        ("十折交叉验证评估", test_evaluation),
        ("GA优化ADASYN", test_ga_adasyn_only)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n运行测试: {test_name}")
        try:
            if test_name == "数据加载":
                result, _ = test_func()
            else:
                result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 测试结果汇总
    print("\n" + "=" * 80)
    print("测试结果汇总")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name:20}: {status}")
        if result:
            passed += 1
    
    print("-" * 80)
    print(f"总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！可以运行完整的yeast.py实验")
        print("\n运行完整实验: python yeast.py")
        print("\n数据集说明:")
        print("  - 少数类: GOL(高尔基体) + POX(过氧化物酶体) + VAC(液泡)")
        print("  - 多数类: 其他细胞定位类别")
        print("  - 这是酵母蛋白质细胞定位数据集，用于预测蛋白质的细胞定位")
        print("  - 特征包括蛋白质序列的各种生化特性")
    else:
        print("⚠️  部分测试失败，请检查错误信息")
    
    return passed == total

if __name__ == "__main__":
    # 设置随机种子
    np.random.seed(42)
    
    # 运行测试
    success = main()
    sys.exit(0 if success else 1)
