"""
系统测试脚本：验证各个模块是否正常工作
"""

import os
import sys
import numpy as np
import logging
from sklearn.datasets import make_classification
from sklearn.model_selection import train_test_split

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试所有模块导入"""
    print("测试模块导入...")
    
    try:
        from config import ga_config, param_ranges, exp_config
        print("✓ 配置模块导入成功")
        
        from wgan_gp import WGAN_GP
        print("✓ WGAN-GP模块导入成功")
        
        from genetic_algorithm import GeneticAlgorithm, Individual
        print("✓ 遗传算法模块导入成功")
        
        from fitness_function import fitness_function, decode_individual
        print("✓ 适应度函数模块导入成功")
        
        from data_utils import DataProcessor, create_synthetic_imbalanced_dataset
        print("✓ 数据处理模块导入成功")
        
        from evaluation import comprehensive_evaluation
        print("✓ 评估模块导入成功")
        
        from utils import setup_logging, create_results_directory
        print("✓ 工具模块导入成功")
        
        return True
        
    except ImportError as e:
        print(f"✗ 模块导入失败: {e}")
        return False

def test_data_processing():
    """测试数据处理功能"""
    print("\n测试数据处理...")
    
    try:
        from data_utils import create_synthetic_imbalanced_dataset, DataProcessor
        
        # 创建合成数据集
        X, y = create_synthetic_imbalanced_dataset(
            n_samples=1000, 
            n_features=10, 
            imbalance_ratio=0.1
        )
        
        print(f"✓ 合成数据集创建成功: {X.shape}, 类别分布: {np.bincount(y)}")
        
        # 测试数据处理器
        processor = DataProcessor()
        X_processed, y_processed = processor.preprocess_data(X, y, fit_transform=True)
        
        print(f"✓ 数据预处理成功: {X_processed.shape}")
        
        # 测试数据划分
        X_train_sub, X_val, X_test, y_train_sub, y_val, y_test = processor.split_data(X_processed, y_processed)
        
        print(f"✓ 数据划分成功: 训练{X_train_sub.shape[0]}, 验证{X_val.shape[0]}, 测试{X_test.shape[0]}")
        
        return True, (X_train_sub, X_val, X_test, y_train_sub, y_val, y_test)
        
    except Exception as e:
        print(f"✗ 数据处理测试失败: {e}")
        return False, None

def test_wgan_gp():
    """测试WGAN-GP功能"""
    print("\n测试WGAN-GP...")
    
    try:
        from wgan_gp import WGAN_GP
        
        # 创建简单测试数据
        np.random.seed(42)
        test_data = np.random.normal(0, 1, (100, 5))
        
        # 初始化WGAN-GP
        wgan = WGAN_GP(
            latent_dim=10,
            data_dim=5,
            lambda_gp=10,
            lr=1e-4,
            batch_size=32,
            n_critic=2
        )
        
        print("✓ WGAN-GP初始化成功")
        
        # 训练（少量epoch用于测试）
        wgan.train(test_data, epochs=5)
        print("✓ WGAN-GP训练成功")
        
        # 生成样本
        generated = wgan.generate_samples(10)
        print(f"✓ 样本生成成功: {generated.shape}")
        
        return True
        
    except Exception as e:
        print(f"✗ WGAN-GP测试失败: {e}")
        return False

def test_fitness_function():
    """测试适应度函数"""
    print("\n测试适应度函数...")
    
    try:
        from fitness_function import fitness_function, decode_individual
        from data_utils import create_synthetic_imbalanced_dataset
        
        # 创建测试数据
        X, y = create_synthetic_imbalanced_dataset(n_samples=200, n_features=5, imbalance_ratio=0.2)
        X_train, X_val, y_train, y_val = train_test_split(X, y, test_size=0.3, stratify=y, random_state=42)
        
        # 创建测试个体
        test_individual = [10, 0.7, 10.0, 3, -4.0, 1]  # [k, α, λ, n_critic, log(lr), batch_size_idx]
        
        print("✓ 测试数据和个体创建成功")
        
        # 解码测试
        decoded = decode_individual(test_individual)
        print(f"✓ 参数解码成功: k={decoded[0]}, α={decoded[1]:.2f}, λ={decoded[2]:.1f}")
        
        # 适应度计算测试（使用较小的数据集和简化的WGAN训练）
        fitness = fitness_function(test_individual, X_train, y_train, X_val, y_val)
        print(f"✓ 适应度计算成功: {fitness:.4f}")
        
        return True
        
    except Exception as e:
        print(f"✗ 适应度函数测试失败: {e}")
        return False

def test_genetic_algorithm():
    """测试遗传算法（简化版）"""
    print("\n测试遗传算法...")
    
    try:
        from genetic_algorithm import GeneticAlgorithm, Individual
        from fitness_function import fitness_function
        from config import param_ranges
        from data_utils import create_synthetic_imbalanced_dataset
        
        # 创建小规模测试数据
        X, y = create_synthetic_imbalanced_dataset(n_samples=150, n_features=4, imbalance_ratio=0.3)
        X_train, X_val, y_train, y_val = train_test_split(X, y, test_size=0.4, stratify=y, random_state=42)
        
        print("✓ 测试数据创建成功")
        
        # 初始化遗传算法（小规模参数）
        ga = GeneticAlgorithm(
            fitness_func=fitness_function,
            param_bounds=param_ranges.get_param_bounds(),
            param_types=param_ranges.get_param_types(),
            population_size=10,  # 小种群
            max_generations=3,   # 少代数
            elite_size=2
        )
        
        print("✓ 遗传算法初始化成功")
        
        # 测试种群初始化
        population = ga.initialize_population()
        print(f"✓ 种群初始化成功: {len(population)} 个个体")
        
        # 测试适应度评估（只评估前3个个体）
        for i in range(min(3, len(population))):
            try:
                fitness = fitness_function(population[i].genes, X_train, y_train, X_val, y_val)
                population[i].fitness = fitness
                print(f"✓ 个体 {i+1} 适应度: {fitness:.4f}")
            except Exception as e:
                print(f"⚠ 个体 {i+1} 适应度计算失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"✗ 遗传算法测试失败: {e}")
        return False

def test_configuration():
    """测试配置参数"""
    print("\n测试配置参数...")
    
    try:
        from config import ga_config, param_ranges, exp_config, wgan_config
        
        # 测试参数范围
        bounds = param_ranges.get_param_bounds()
        types = param_ranges.get_param_types()
        
        print(f"✓ 参数边界: {len(bounds)} 个参数")
        print(f"✓ 参数类型: {types}")
        
        # 测试配置一致性
        assert len(bounds) == len(types), "参数边界和类型数量不匹配"
        print("✓ 配置一致性检查通过")
        
        # 显示关键配置
        print(f"✓ 种群大小: {ga_config.POPULATION_SIZE}")
        print(f"✓ 最大代数: {ga_config.MAX_GENERATIONS}")
        print(f"✓ WGAN潜在维度: {wgan_config.LATENT_DIM}")
        
        return True
        
    except Exception as e:
        print(f"✗ 配置测试失败: {e}")
        return False

def run_all_tests():
    """运行所有测试"""
    print("=" * 60)
    print("遗传算法优化ADASYN+WGAN-GP系统测试")
    print("=" * 60)
    
    tests = [
        ("模块导入", test_imports),
        ("数据处理", test_data_processing),
        ("配置参数", test_configuration),
        ("WGAN-GP", test_wgan_gp),
        ("适应度函数", test_fitness_function),
        ("遗传算法", test_genetic_algorithm),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            if test_name == "数据处理":
                result, _ = test_func()
            else:
                result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 测试结果汇总
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name:15}: {status}")
        if result:
            passed += 1
    
    print("-" * 60)
    print(f"总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统可以正常运行。")
        print("\n下一步：运行 python main_experiment.py 开始完整实验")
    else:
        print("⚠️  部分测试失败，请检查错误信息并修复问题。")
    
    return passed == total

if __name__ == "__main__":
    # 设置简单日志
    logging.basicConfig(level=logging.WARNING)  # 减少日志输出
    
    success = run_all_tests()
    sys.exit(0 if success else 1)
