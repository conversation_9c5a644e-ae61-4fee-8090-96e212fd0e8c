"""
兼容性检查脚本：检查和修复常见的版本兼容性问题
"""

import sys
import importlib
import warnings
warnings.filterwarnings('ignore')

def check_python_version():
    """检查Python版本"""
    print("检查Python版本...")
    version = sys.version_info
    print(f"当前Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print("❌ Python版本过低，建议使用Python 3.7+")
        return False
    elif version.major == 3 and version.minor >= 11:
        print("⚠️  Python版本较新，某些包可能不兼容")
        return True
    else:
        print("✅ Python版本兼容")
        return True

def check_package_versions():
    """检查关键包版本"""
    print("\n检查关键包版本...")
    
    packages_to_check = {
        'numpy': '1.19.0',
        'pandas': '1.2.0',
        'sklearn': '0.24.0',
        'scipy': '1.5.0',
        'torch': '1.8.0',
        'matplotlib': '3.3.0',
        'seaborn': '0.11.0',
        'imblearn': '0.8.0'
    }
    
    results = {}
    
    for package, min_version in packages_to_check.items():
        try:
            if package == 'sklearn':
                import sklearn
                module = sklearn
                package_name = 'scikit-learn'
            elif package == 'imblearn':
                import imblearn
                module = imblearn
                package_name = 'imbalanced-learn'
            else:
                module = importlib.import_module(package)
                package_name = package
            
            version = getattr(module, '__version__', 'unknown')
            print(f"  {package_name}: {version}")
            results[package] = {'installed': True, 'version': version}
            
        except ImportError:
            print(f"  {package_name}: ❌ 未安装")
            results[package] = {'installed': False, 'version': None}
    
    return results

def check_scipy_wasserstein():
    """检查scipy中wasserstein_distance函数"""
    print("\n检查scipy.spatial.distance.wasserstein_distance...")
    
    try:
        from scipy.spatial.distance import wasserstein_distance
        print("✅ wasserstein_distance 可用")
        return True
    except ImportError:
        print("⚠️  wasserstein_distance 不可用，将使用自定义实现")
        return False

def check_torch_cuda():
    """检查PyTorch CUDA支持"""
    print("\n检查PyTorch CUDA支持...")
    
    try:
        import torch
        if torch.cuda.is_available():
            print(f"✅ CUDA可用，设备数量: {torch.cuda.device_count()}")
            for i in range(torch.cuda.device_count()):
                print(f"  设备 {i}: {torch.cuda.get_device_name(i)}")
            return True
        else:
            print("⚠️  CUDA不可用，将使用CPU")
            return False
    except ImportError:
        print("❌ PyTorch未安装")
        return False

def check_matplotlib_backend():
    """检查matplotlib后端"""
    print("\n检查matplotlib后端...")
    
    try:
        import matplotlib
        import matplotlib.pyplot as plt
        
        backend = matplotlib.get_backend()
        print(f"当前后端: {backend}")
        
        # 测试基本绘图功能
        fig, ax = plt.subplots(figsize=(6, 4))
        ax.plot([1, 2, 3], [1, 4, 2])
        ax.set_title('测试图表')
        plt.close(fig)
        
        print("✅ matplotlib绘图功能正常")
        return True
        
    except Exception as e:
        print(f"❌ matplotlib测试失败: {e}")
        return False

def test_core_functionality():
    """测试核心功能"""
    print("\n测试核心功能...")
    
    try:
        # 测试数据处理
        from data_utils import create_synthetic_imbalanced_dataset
        X, y = create_synthetic_imbalanced_dataset(n_samples=100, n_features=5)
        print("✅ 数据处理功能正常")
        
        # 测试WGAN-GP初始化
        from wgan_gp import WGAN_GP
        wgan = WGAN_GP(latent_dim=10, data_dim=5, lambda_gp=10, lr=1e-4, batch_size=16, n_critic=2)
        print("✅ WGAN-GP初始化正常")
        
        # 测试遗传算法初始化
        from genetic_algorithm import GeneticAlgorithm
        from config import param_ranges
        ga = GeneticAlgorithm(
            fitness_func=lambda x, *args: 0.5,  # 虚拟适应度函数
            param_bounds=param_ranges.get_param_bounds(),
            param_types=param_ranges.get_param_types(),
            population_size=10,
            max_generations=5
        )
        print("✅ 遗传算法初始化正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 核心功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def provide_fix_suggestions(results):
    """提供修复建议"""
    print("\n" + "="*50)
    print("修复建议")
    print("="*50)
    
    # 检查未安装的包
    missing_packages = [pkg for pkg, info in results.items() if not info['installed']]
    
    if missing_packages:
        print("未安装的包:")
        for pkg in missing_packages:
            if pkg == 'sklearn':
                print(f"  pip install scikit-learn")
            elif pkg == 'imblearn':
                print(f"  pip install imbalanced-learn")
            else:
                print(f"  pip install {pkg}")
        print()
    
    # 通用建议
    print("通用修复建议:")
    print("1. 更新pip: python -m pip install --upgrade pip")
    print("2. 安装所有依赖: pip install -r requirements.txt")
    print("3. 如果使用conda: conda install pytorch torchvision -c pytorch")
    print("4. 如果遇到版本冲突，创建新的虚拟环境:")
    print("   python -m venv new_env")
    print("   new_env\\Scripts\\activate  # Windows")
    print("   source new_env/bin/activate  # Linux/Mac")
    print("   pip install -r requirements.txt")
    print()
    
    # 特定问题建议
    print("特定问题修复:")
    print("- 如果scipy版本过旧: pip install --upgrade scipy>=1.5.0")
    print("- 如果torch安装失败: pip install torch --index-url https://download.pytorch.org/whl/cpu")
    print("- 如果matplotlib显示问题: pip install --upgrade matplotlib")
    print("- 如果在Jupyter中运行: pip install ipywidgets")

def main():
    """主函数"""
    print("="*60)
    print("遗传算法优化ADASYN+WGAN-GP系统兼容性检查")
    print("="*60)
    
    all_passed = True
    
    # 检查Python版本
    if not check_python_version():
        all_passed = False
    
    # 检查包版本
    package_results = check_package_versions()
    
    # 检查特定功能
    if not check_scipy_wasserstein():
        print("  (已提供自定义实现，不影响使用)")
    
    if not check_torch_cuda():
        print("  (将使用CPU训练，速度较慢但不影响功能)")
    
    if not check_matplotlib_backend():
        all_passed = False
    
    # 测试核心功能
    if not test_core_functionality():
        all_passed = False
    
    # 提供修复建议
    provide_fix_suggestions(package_results)
    
    # 总结
    print("\n" + "="*60)
    print("检查结果总结")
    print("="*60)
    
    if all_passed:
        print("🎉 系统兼容性检查通过！")
        print("可以运行: python run.py demo")
    else:
        print("⚠️  发现兼容性问题，请根据上述建议进行修复")
        print("修复后重新运行此脚本进行检查")
    
    return all_passed

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n检查被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n检查过程出错: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
