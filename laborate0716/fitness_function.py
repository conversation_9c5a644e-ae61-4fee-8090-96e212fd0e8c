"""
适应度函数：评估ADASYN+WGAN-GP参数组合的性能
"""

import numpy as np
import logging
from sklearn.ensemble import RandomForestClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.neural_network import MLPClassifier
from sklearn.metrics import f1_score, roc_auc_score, classification_report
from sklearn.model_selection import cross_val_score, StratifiedKFold
from imblearn.over_sampling import ADASYN
from scipy.stats import entropy
try:
    from scipy.spatial.distance import wasserstein_distance
except ImportError:
    # 如果scipy版本较旧，使用自定义实现
    def wasserstein_distance(u_values, v_values):
        """
        简化的Wasserstein距离实现（1维）
        """
        import numpy as np
        u_values = np.asarray(u_values)
        v_values = np.asarray(v_values)

        # 排序
        u_sorted = np.sort(u_values)
        v_sorted = np.sort(v_values)

        # 计算累积分布函数
        n, m = len(u_sorted), len(v_sorted)
        u_cdf = np.arange(1, n+1) / n
        v_cdf = np.arange(1, m+1) / m

        # 合并并排序所有值
        all_values = np.concatenate([u_sorted, v_sorted])
        all_values = np.sort(all_values)

        # 计算每个点的CDF值
        u_cdf_interp = np.searchsorted(u_sorted, all_values, side='right') / n
        v_cdf_interp = np.searchsorted(v_sorted, all_values, side='right') / m

        # 计算Wasserstein距离
        return np.mean(np.abs(u_cdf_interp - v_cdf_interp))
import warnings
warnings.filterwarnings('ignore')

from wgan_gp import WGAN_GP
from config import exp_config, wgan_config, param_ranges

logger = logging.getLogger(__name__)

def geometric_mean_score(y_true, y_pred):
    """计算G-mean (几何平均数)"""
    from sklearn.metrics import confusion_matrix
    cm = confusion_matrix(y_true, y_pred)
    if cm.shape == (2, 2):
        tn, fp, fn, tp = cm.ravel()
        sensitivity = tp / (tp + fn) if (tp + fn) > 0 else 0
        specificity = tn / (tn + fp) if (tn + fp) > 0 else 0
        return np.sqrt(sensitivity * specificity)
    return 0

def calculate_kl_divergence(real_data, synthetic_data):
    """计算KL散度"""
    try:
        # 对每个特征计算直方图
        kl_divs = []
        for i in range(real_data.shape[1]):
            # 计算直方图
            real_hist, bins = np.histogram(real_data[:, i], bins=20, density=True)
            synthetic_hist, _ = np.histogram(synthetic_data[:, i], bins=bins, density=True)
            
            # 避免零值
            real_hist = real_hist + 1e-8
            synthetic_hist = synthetic_hist + 1e-8
            
            # 计算KL散度
            kl_div = entropy(real_hist, synthetic_hist)
            kl_divs.append(kl_div)
        
        return np.mean(kl_divs)
    except:
        return float('inf')

def calculate_wasserstein_distance(real_data, synthetic_data):
    """计算Wasserstein距离"""
    try:
        wd_distances = []
        for i in range(real_data.shape[1]):
            wd = wasserstein_distance(real_data[:, i], synthetic_data[:, i])
            wd_distances.append(wd)
        return np.mean(wd_distances)
    except:
        return float('inf')

def decode_individual(individual):
    """解码个体参数"""
    k = int(individual[0])
    alpha = individual[1]
    lambda_gp = individual[2]
    n_critic = int(individual[3])
    lr = 10 ** individual[4]  # 对数尺度
    batch_size_idx = int(individual[5])
    batch_size = param_ranges.WGAN_BATCH_SIZES[batch_size_idx]
    
    return k, alpha, lambda_gp, n_critic, lr, batch_size

def create_classifier(classifier_type='rf'):
    """创建分类器"""
    if classifier_type == 'rf':
        return RandomForestClassifier(
            n_estimators=exp_config.RF_N_ESTIMATORS,
            max_depth=exp_config.RF_MAX_DEPTH,
            random_state=42,
            n_jobs=-1
        )
    elif classifier_type == 'lr':
        return LogisticRegression(random_state=42, max_iter=1000)
    elif classifier_type == 'svm':
        return SVC(random_state=42, probability=True)
    elif classifier_type == 'nn':
        return MLPClassifier(hidden_layer_sizes=(100, 50), random_state=42, max_iter=500)
    else:
        raise ValueError(f"Unsupported classifier type: {classifier_type}")

def fitness_function(individual, X_train_sub, y_train_sub, X_val, y_val):
    """
    适应度函数：评估个体参数的性能
    
    Args:
        individual: 个体参数 [k, α, λ, n_critic, log(lr), batch_size_idx]
        X_train_sub: 训练子集特征
        y_train_sub: 训练子集标签
        X_val: 验证集特征
        y_val: 验证集标签
    
    Returns:
        fitness: 适应度值 (越高越好)
    """
    try:
        # 解码参数
        k, alpha, lambda_gp, n_critic, lr, batch_size = decode_individual(individual)
        
        # 只在详细模式下显示参数信息
        if hasattr(logger, 'isEnabledFor') and logger.isEnabledFor(logging.DEBUG):
            logger.debug(f"评估参数: k={k}, α={alpha:.3f}, λ={lambda_gp:.3f}, "
                        f"n_critic={n_critic}, lr={lr:.2e}, bs={batch_size}")
        else:
            # 简化的进度显示
            print(f"    评估参数组合: k={k}, α={alpha:.2f}, λ={lambda_gp:.1f}, lr={lr:.1e}", end=" ")
        
        # 提取少数类和多数类
        X_min_sub = X_train_sub[y_train_sub == 1]
        X_maj_sub = X_train_sub[y_train_sub == 0]
        N_min = len(X_min_sub)
        N_maj = len(X_maj_sub)
        
        if N_min == 0 or N_maj == 0:
            logger.warning("训练集中缺少某个类别的样本")
            return 0.0
        
        G_total = N_maj - N_min  # 需要生成的总量
        
        if G_total <= 0:
            logger.warning("数据已经平衡或多数类样本不足")
            return 0.0
        
        # 1. ADASYN生成
        G_adasyn = int(alpha * G_total)
        X_adasyn_only = np.zeros((0, X_train_sub.shape[1]))
        
        if G_adasyn > 0:
            try:
                # 使用ADASYN生成样本
                target_samples = N_min + G_adasyn
                adasyn = ADASYN(
                    sampling_strategy={1: target_samples},
                    n_neighbors=min(k, N_min-1) if N_min > 1 else 1,
                    random_state=42
                )
                X_adasyn_res, y_adasyn_res = adasyn.fit_resample(X_train_sub, y_train_sub)
                
                # 提取新生成的样本
                X_adasyn_only = X_adasyn_res[len(X_train_sub):]
                
                logger.debug(f"ADASYN生成了 {len(X_adasyn_only)} 个样本")
                
            except Exception as e:
                logger.warning(f"ADASYN生成失败: {e}")
                G_adasyn = 0
        
        # 2. WGAN-GP生成
        G_wgan = G_total - G_adasyn
        X_wgan = np.zeros((0, X_train_sub.shape[1]))
        
        if G_wgan > 0:
            try:
                # 合并少数类样本作为WGAN-GP的训练数据
                if len(X_adasyn_only) > 0:
                    real_minority = np.vstack([X_min_sub, X_adasyn_only])
                else:
                    real_minority = X_min_sub
                
                # 检查数据量是否足够
                if len(real_minority) < batch_size:
                    batch_size = max(1, len(real_minority) // 2)
                
                # 初始化并训练WGAN-GP
                wgan_gp = WGAN_GP(
                    latent_dim=wgan_config.LATENT_DIM,
                    data_dim=X_train_sub.shape[1],
                    lambda_gp=lambda_gp,
                    lr=lr,
                    batch_size=batch_size,
                    n_critic=n_critic,
                    generator_hidden_dims=wgan_config.GENERATOR_HIDDEN_DIMS,
                    discriminator_hidden_dims=wgan_config.DISCRIMINATOR_HIDDEN_DIMS,
                    force_cpu=wgan_config.FORCE_CPU
                )
                
                # 训练WGAN-GP
                wgan_gp.train(real_minority, epochs=wgan_config.EPOCHS)
                
                # 生成样本
                X_wgan = wgan_gp.generate_samples(G_wgan)
                
                logger.debug(f"WGAN-GP生成了 {len(X_wgan)} 个样本")
                
            except Exception as e:
                logger.warning(f"WGAN-GP生成失败: {e}")
                G_wgan = 0
        
        # 3. 构建增强训练集
        X_synthetic = np.vstack([X_adasyn_only, X_wgan]) if len(X_adasyn_only) > 0 or len(X_wgan) > 0 else np.zeros((0, X_train_sub.shape[1]))
        
        if len(X_synthetic) == 0:
            logger.warning("没有生成任何合成样本")
            return 0.0
        
        X_train_augmented = np.vstack([X_train_sub, X_synthetic])
        y_train_augmented = np.concatenate([
            y_train_sub, 
            np.ones(len(X_synthetic))
        ])
        
        # 4. 合成质量评估
        quality_penalty = 0.0
        
        # KL散度检查
        if len(X_synthetic) > 0:
            kl_div = calculate_kl_divergence(X_min_sub, X_synthetic)
            if kl_div > exp_config.KL_DIVERGENCE_THRESHOLD:
                quality_penalty += 0.2 * (kl_div - exp_config.KL_DIVERGENCE_THRESHOLD)
            
            # Wasserstein距离检查
            wd = calculate_wasserstein_distance(X_min_sub, X_synthetic)
            if wd > exp_config.WASSERSTEIN_DISTANCE_THRESHOLD:
                quality_penalty += 0.2 * (wd - exp_config.WASSERSTEIN_DISTANCE_THRESHOLD)
        
        # 5. 训练分类器并评估
        classifier = create_classifier(exp_config.CLASSIFIER_TYPE)
        classifier.fit(X_train_augmented, y_train_augmented)
        
        # 在验证集上预测
        y_pred = classifier.predict(X_val)
        y_pred_proba = classifier.predict_proba(X_val)[:, 1] if hasattr(classifier, 'predict_proba') else y_pred
        
        # 计算评估指标
        f1_weighted = f1_score(y_val, y_pred, average='weighted')
        f1_minority = f1_score(y_val, y_pred, pos_label=1)
        
        try:
            auc = roc_auc_score(y_val, y_pred_proba)
        except:
            auc = 0.5
        
        gmean = geometric_mean_score(y_val, y_pred)
        
        # 6. 计算综合适应度
        fitness = (exp_config.F1_WEIGHT * f1_weighted + 
                  exp_config.AUC_WEIGHT * auc + 
                  exp_config.GMEAN_WEIGHT * gmean) - quality_penalty
        
        # 显示评估结果
        if hasattr(logger, 'isEnabledFor') and logger.isEnabledFor(logging.DEBUG):
            logger.debug(f"评估结果: F1={f1_weighted:.3f}, AUC={auc:.3f}, "
                        f"G-mean={gmean:.3f}, 质量惩罚={quality_penalty:.3f}, "
                        f"适应度={fitness:.3f}")
        else:
            print(f"→ 适应度={fitness:.4f}")  # 简化输出
        
        return max(0.0, fitness)  # 确保适应度非负
        
    except Exception as e:
        logger.error(f"适应度函数执行失败: {e}")
        return 0.0

def fitness_function_cv(individual, X_train_full, y_train_full):
    """
    使用交叉验证的适应度函数 (更准确但计算开销大)
    """
    try:
        k, alpha, lambda_gp, n_critic, lr, batch_size = decode_individual(individual)
        
        # 使用分层K折交叉验证
        skf = StratifiedKFold(n_splits=exp_config.CV_FOLDS, shuffle=True, random_state=42)
        fitness_scores = []
        
        for train_idx, val_idx in skf.split(X_train_full, y_train_full):
            X_train_fold = X_train_full[train_idx]
            y_train_fold = y_train_full[train_idx]
            X_val_fold = X_train_full[val_idx]
            y_val_fold = y_train_full[val_idx]
            
            # 计算单折适应度
            fold_fitness = fitness_function(individual, X_train_fold, y_train_fold, X_val_fold, y_val_fold)
            fitness_scores.append(fold_fitness)
        
        # 返回平均适应度，并加入稳定性惩罚
        mean_fitness = np.mean(fitness_scores)
        std_fitness = np.std(fitness_scores)
        
        final_fitness = mean_fitness - exp_config.STABILITY_PENALTY * std_fitness
        
        return max(0.0, final_fitness)
        
    except Exception as e:
        logger.error(f"交叉验证适应度函数执行失败: {e}")
        return 0.0
